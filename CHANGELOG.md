# Changelog Portcall+
# Version 4.8.? ??-??-????
- Updated SgMdh service with a catch EOFException to prevent the task crashing

# Version 4.8.0 25-04-2023
- Updated application.properties, added USCRP to the list of ports to be updated with Atd and Ata

# Version 4.7.1 06-04-2023
- Sanitize berth names for `VesselStayServiceV2` when starting with `BEANR`

# Version 4.7.0 05-04-2023
- Adding a log when invalid imos are in the vesselStayV2's fetchingStays.

# Version 4.5.0 13-03-2023
- [PRA-14]: ecr name change
- Add mariteam portbase
- Change fetching of existing portcalls to 5 days in the future.
- [DEV-114]: Updated resources used to have no CPU limit and memory request and limit be the same.
- cleanup of TMA, Simply5 and HarborLights.
- Upgrading used library versions.
- Brief clean up.

## Version 4.4.0 19-07-2022
- Add pilot boarding place gusong boarding ground to the check for UpdateType
- Required non-functional changes to run in EKS.

## Version 4.3.0 30-03-2022
- Add: Support for barges from Vopak myservices
- Fix: parse the currentVisit instead of the next visit from harborlights
- Fix: Do not parse EnigmaScraper Eta information when <PERSON><PERSON> was the last source
- Add: Management tools for mturk
- Fix: Search Vopak nominations by portcall interval when not found by alias
- Fix: Set the timeinterval for new portcalls per port in Vopak Nominations
- Fix: Set the timeinterval for new portcalls to 40 days in Tma Nominations
- Fix: Search by alias first when parsing TmaNominations
- Add: Store all input from Tma in the database
- Fix: Add condition when sgmdh nomination has Anch in it to make it UpdateType Anchorage
- Add: Ktlint
- Fix: set Sgmdh starttimeType based on the location

## Version 4.2.0 14-12-2021
- Add Zabbix
- Rewrite status endpoints to have specific knowledge of the tasks
- Add update rate warnings in the status endpoint
- Deactivated NM20_8HOURS Task
- Refactored the objectmapper into a bean

## Version 4.1.1 28-09-2021
- bugfix: portcall visit update added a new visit when the berthAtd was the same as the berth Ata or the berth Ata 
  was null
- 
## Version 4.1.1 28-09-2021
- bugfix: giving PortcallGenerator update the right model

## Version 4.1.0 23-09-2021
- Add: status endpoints for zabbix
- Remove: Slack report as task
- Update: Run slack service on a cron every morning at 9
- Remove: NXTPORT_V2, only use NXTPORT from now on
- Add: PORTBASE_CLIENT_REFERENCE_NUMBER
- Update: use teqplay ids in everything except for portbase portcalls. This is because Hamis and Iris create
  portcalls in platform
- Update: Add task counters automatically on task services

## Version 4.0.0 21-07-2021
- fix: do not save lastFetchIndex if null
- update previous lastIndex in case of portbase task failure
- upgrading the dependencies to the latest versions
- upgrading circle ci java to 11
- fix: handle timeouts in a better way
- add amazon linux 2 ebextensions config copy from vesselvoyage
- add the logging folder
- fix: make StayV2 stay_number nullable
- only send update type nomination for VopakNominations
- process tma nominations received through the nominations queue
- fix: return null, when an exact match to the portcallId alias is found, but source does not match
- Only allow ports for which we receive the area events

## Version 3.9.1 11-06-2021
- update: HarborLights url

## Version 3.9.0 09-06-2021
- update: removed deprecated tasks and enums
- VesselStay incremental fetch api implementtion
- log imo changes, and small refactor
- update: fetch portcalls with alias and portcallid when a bulk query is done
- update: destroy implementation and moved stopTask logic to TaskService
- update: rewrote portbase to call in sync way
- make sure visit with no eta is placed last
- update: portbase retry in case of failure

## Version 3.8.1 31-05-2021
- Check same visit for terminal name aswell as berth name

## Version 3.8.0 25-05-2021
- Added Visittype to every visit
- Do not update the portcall starttime on nominations
- Update starttime based on the first visit of the portcall

## Version 3.7.0, 12-05-2021
- handle change in imo in vopak nominations
- do not look up nominations by start and endTime filtering, if portcallId is given
- update: added jetty info processing from vopak
- fixes for visits overwrite in USHOU. clean up
- fixes for visits unnecessarily added by harborlights in USHOU. clean up
- fix: startTimeType to be updated in all services individually

## Version 3.6.2, 22-04-2021
- fix: status call flip flop

## Version 3.6.1, 14-04-2021
- fix: Consider harborlights portcall creation only if portcall is missing, or if its more than 30days

## Version 3.6.0, 23-03-2021
- update: Added startTime type
- update: added layberth for USHOU

## Version 3.5.3, 22-03-2021
- fix: added separate PortcallAliasNames for the different terminals

## Version 3.5.2, 16-03-2021
- fix: Check aliasId when look up is on /nominations call

## Version 3.5.1, 12-03-2021
- fix: increased vopak nomination interval to merge same portcall info from it and Harborlights

## Version 3.5.0, 03-03-2021
- update: lis scrapper for all schelde ports

## Version 3.4.0, 18-02-2021
- update: implemented harborLights integration for USHOU

## Version 3.3.2, 11-02-2021
- update: improved NxtPort performance and logging by configurable lookup of vesselStay locally first.
- update: indexes on the slack
  counters to improve performance

## Version 3.3.1, 02-02-2021
- update: handle any exceptions seen in processing slack counters

## Version 3.3.0, 01-02-2021
- update: cache NxtPort calls to reduce numbers
- update: save Vopak Nominations
- update: portbase per token

## Version 3.2.1, 25-01-2021
- fix: do not cache basic auth login in case of jitter

## Version 3.2.0, 15-01-2021
- update: automatic alias based on NOR Tendered for Vopak USHOU

## Version 3.1.0, 07-01-2021
- update: slack notification on deploy
- update: process vopak nomination for USHOU and SGSIN
- fix: MTurk fixes on create assignments only for new Portcalls
- fix: xml escape MTurk assignment answers

## Version 3.0.1, 2020-12-22
- update: reorder maven repository. Teqplay before jcenter

## Version 3.0.0, 2020-12-21
- update: MTurk implementation to fetch `vesselAgent` via assignments
- fix: Counters digitraffic
- fix: fetch all portcalls within the portcall time interval when getting nomination options
- Added Ssl certificate


## Version 2.8.1, 2020-12-2
- fix: handle bunker only purpose nominations

## Version 2.8.0, 2020-12-1
- update: Finnish portcalls from Digitraffic
- update: Support purpose based source handling for S5 nominations

## Version 2.7.0, 2020-10-30
- Added the option to have multiple aliasses defined for the portcallId
- separated the getOrCreatePortcall into a get, create and update 

## Version 2.6.0, 2020-09-16
- fix: portcall creation issues
- update: Simply5 nominations handling
- update: pass all spring dependencies via a constructor rather than an autowire

## Version 2.5.0, 2020-08-06
- fix: Allow portcall for a vessel when previous one is closed
- update: listen to simply rabbitmq

## Version 2.4.0, 2020-07-06
- Allow portcallId to be entered in `getOrCreatePortcall` method
- Minor fixes for `timestartFrom` in `SourceService`

## Version 2.3.1, 2020-06-12
- Added bufferTime for task failures to be 30 for all tasks

## Version 2.3.0, 2020-06-12
- Updated with time taken to run tasks
- Fixes to Portcall generator
- Fixes to VesselStay services

## Version 2.2.1, 2020-06-12
- Implemented a configurable ship filtering for NxtPort portcall fetch
- Use the filtering mechanism in NxtPort to also filter out also based on params speed, headingToBEAR
- Do not log if there is only one serviceToken found

## Version 2.2.0, 2020-05-06
- Fixes for making sure portcalls in SGSIN are not unnecessarily closed
- Portbase implementation
- Slack counter updates

## Version 2.1.0, 2020-03-26
- Increased the new portcallInterval, if the ship is in the port don't add a new eta if the portcall already has the same berth in its visits
- TaskService to include portpublisher
- append taskName in full in the ScheduledTaskRun details
- added a check to see if the orderTime is in the past
- Create portcall with agent information on arrival declaration
- Added activity categorization to fetch by port and imo

## Version 2.0.0, 2019-12-20
- Included Schelde ports: BEANR, NLVLI, BEGNE, NLTNZ
- Included SGSIN via api connection
- Release of refactored service handler
- Use portAta and portAtd to mark start and stop of portcalls
- Export excel report of all the portcalls
- Added a activity log

## Version 1.4.0, 2019-11-07
- Added Sgsin portcall endpoint to create new portcalls for SGSIN

## Version 1.3.1, 2019-11-05
- Handle ship filtering even if no speed is returned

## Version 1.3.0, 2019-10-17
- Added Enigma scraper to create new portcalls for Vlissingen and Terneuzen

## Version 1.2.4, 2019-10-29
- Ignore un-parsaable dates from LIS scraper

## Version 1.2.3, 2019-09-20
- Process ship names with [] correctly

## Version 1.2.2, 2019-09-03
- Avoid global launch co-routine. handle failures in LIS scrapper gracefully by re-scheduling.

## Version 1.2.1, 2019-09-02
- Add re-schedule any task if call fails with calling platform

## Version 1.1.1, 2019-08-19
- Added `checkTokens` parameter for the `/status` resource call

## Version 1.1.0, 2019-08-15
- Major release: Implemented authentication mechanism, token rotation, 
slack report of overall count and general fixes

## Version 1.0.0, 2019-08-07
- First release with MVP requirements in place. E.g. scheduled tasks, 
service counters, status. No authentication.