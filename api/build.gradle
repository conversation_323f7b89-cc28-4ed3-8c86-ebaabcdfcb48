import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

buildscript {
    repositories {
        mavenCentral()
        maven {
            url 's3://repo.teqplay.nl/release'
            credentials(AwsCredentials) {
                accessKey "$s3_access_key"
                secretKey "$s3_secret_key"
            }
        }
        maven {
            url 's3://repo.teqplay.nl/snapshot'
            credentials(AwsCredentials) {
                accessKey "$s3_access_key"
                secretKey "$s3_secret_key"
            }
        }
    }

    dependencies {
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "org.jetbrains.kotlin:kotlin-allopen:${kotlin_version}"
        classpath "org.jlleitschuh.gradle:ktlint-gradle:$ktlint_version"
    }
}

plugins {
    id 'org.jetbrains.kotlin.jvm' version "$kotlin_version"
}

group 'nl.teqplay.portcallplus'
version getDate() + getSuffix()

repositories {
    maven {
        url 's3://repo.teqplay.nl/release'
        credentials(AwsCredentials) {
            accessKey "$s3_access_key"
            secretKey "$s3_secret_key"
        }
    }
    maven {
        url 's3://repo.teqplay.nl/snapshot'
        credentials(AwsCredentials) {
            accessKey "$s3_access_key"
            secretKey "$s3_secret_key"
        }
    }
    maven { url 'https://jcenter.bintray.com/' }
    mavenCentral()
    maven { url 'https://jitpack.io' }
}

apply plugin: 'kotlin'
apply plugin: 'kotlin-spring'
apply plugin: 'maven-publish'
apply plugin: 'io.spring.dependency-management'
apply plugin: "org.jlleitschuh.gradle.ktlint"

ktlint {
    version = "0.50.0"
    enableExperimentalRules = false
    filter {
        exclude { element ->
            def path = element.file.path
            path.contains("\\build\\") || path.contains("/build/")
        }
    }
}

// For cyclonedxBom
dependencyManagement {
    imports {
        mavenBom "org.springframework.boot:spring-boot-dependencies:$spring_boot_version"
    }
}

dependencies {
    implementation "nl.teqplay.skeleton:datasource2:$skeleton_version"
    implementation "nl.teqplay.skeleton:common-internal-api-client:$skeleton_version"

    implementation "org.springframework.cloud:spring-cloud-starter-kubernetes-fabric8-config:$spring_cloud_version"
    implementation "org.springframework.boot:spring-boot-starter-web:$spring_boot_version"

    implementation "com.fasterxml.jackson.core:jackson-databind:$jackson_version"
    implementation "com.fasterxml.jackson.core:jackson-annotations:$jackson_version"
    implementation "com.fasterxml.jackson.module:jackson-module-kotlin:$jackson_version"
    implementation "com.fasterxml.jackson.datatype:jackson-datatype-jsr310:$jackson_version"

    implementation "nl.teqplay.platform:api:$platform_version"

    implementation "org.javers:javers-core:$javers_version"
}
// ===== Versioning and Publishing =====
static def getDate() {
    return new Date().format('yyyyMMdd')
}

static def getSuffix() {
    if (System.getenv('GH') != null) {
        if (System.getenv('BRANCH') == 'master') {
            return '-b' + System.getenv('GITHUB_RUN_NUMBER')
        } else {
            return '-SNAPSHOT'
        }
    } else {
        return '-SNAPSHOT'
    }
}

tasks.register('sourcesJar', Jar) {
    from sourceSets.main.allSource
    archiveClassifier = 'sources'
}

tasks.withType(KotlinCompile).configureEach {
    compilerOptions {
        jvmTarget = JvmTarget.JVM_17
    }
}

publishing {
    publications {
        api(MavenPublication) {
            from components.java
            artifact sourcesJar

            repositories {
                maven {
                    if (project.version.endsWith('-SNAPSHOT')) {
                        url "s3://repo.teqplay.nl/snapshot"
                    } else {
                        url "s3://repo.teqplay.nl/release"
                    }

                    credentials(AwsCredentials) {
                        accessKey s3_access_key
                        secretKey s3_secret_key
                    }
                }
            }
        }
    }
}
