package nl.teqplay.portcallplus.api.model

import nl.teqplay.platform.model.Location
import java.util.Date

/**
 * Model class reflecting a port call
 */
data class PortcallVisit(
    /** The eta of this visit. */
    var berthEta: Date? = null,
    /** The actual time of arrival at this berth. For BEANR portcalls, currently This should be filled in
     * only for the first visit. */
    var berthAta: Date? = null,
    /** The actual time of departure from this berth. For BEANR portcalls, currently This should be filled in
     * only for the last visit. */
    var berthAtd: Date? = null,
    /** The estimated time of arrival at this berth. For BEANR portcalls, currently This should be filled in
     * only for the last visit. */
    var berthEtd: Date? = null,
    /** the name of the terminal where this visit will go  */
    var terminal: String? = null,
    /**
     * The uniqueBerthId from Poma
     */
    var uniqueBerthId: String? = null,
    /** The name of the berth involved in this visit  */
    var berthName: String? = null,
    /** The identifier of the movement of the arrival towards this visit  */
    var arrivalMovementId: String? = null,
    /** The identifier of the movement departing this visit  */
    var departureMovementId: String? = null,
    /** Whether or not the portcallVisit is connected to the graph of visits */
    var connected: Boolean? = null,
    /** timings of a pilot going on or off board while either incoming or outgoing for this visit */
    var pilotIncoming: PilotTiming? = null,
    var pilotOutgoing: PilotTiming? = null,
    // draught (in decimeters) of the linked vessel when arriving at this visit
    val draughtIncoming: Double? = null,
    // draught (in decimeters) of the linked vessel when departing from this visit
    val draughtOutgoing: Double? = null,
    // the kind of visit update this is. Refers to the type of location in [berthName]
    val visitType: UpdateType? = null,
)

data class PilotTiming(
    val pilotOnBoardTime: Date? = null,
    val pilotOnBoardLoc: Location? = null,
    val pilotOffBoardTime: Date? = null,
    val pilotOffBoardLoc: Location? = null,
)
