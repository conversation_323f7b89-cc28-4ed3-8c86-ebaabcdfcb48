package nl.teqplay.portcallplus.api.model

import com.fasterxml.jackson.annotation.JsonIgnore
import org.javers.core.metamodel.annotation.DiffIgnore
import java.util.Date

/**
 * <AUTHOR>
 * Base class that is derived by all Mongo collections and helps to fetch mongo based primary key
 */
abstract class DataModel : Comparable<DataModel> {
    /**
     * Method should be linked to the primary key of this mongo document class
     */
    abstract val _id: Any

    /** Date when this model/document was created */
    @DiffIgnore
    var creationTimestamp: Date = Date()
        protected set

    /** Date when this model/document was updated (defaulted to the creationTimestmap) */
    @DiffIgnore
    var updateTimestamp: Date = creationTimestamp
        private set

    @JsonIgnore
    fun resetCreationTimestamp() {
        creationTimestamp = Date()
    }

    @JsonIgnore
    fun resetUpdateTimestamp() {
        updateTimestamp = Date()
    }

    fun resetTimestamps(existing: DataModel) {
        this.creationTimestamp = existing.creationTimestamp
        this.updateTimestamp = existing.updateTimestamp
    }

    /**
     * Sort the elements most recent update first
     */
    override fun compareTo(other: DataModel) = other.updateTimestamp.compareTo(this.updateTimestamp)
}
