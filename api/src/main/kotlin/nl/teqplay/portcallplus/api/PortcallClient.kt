package nl.teqplay.portcallplus.api

import nl.teqplay.aisengine.client.annotations.InternalApiRestTemplate
import nl.teqplay.portcallplus.api.model.Portcall
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.getForObject

class PortcallClient(
    @InternalApiRestTemplate private val restTemplate: RestTemplate,
) {
    companion object {
        // This is the internal api prefix, requests are routed to the PortcallV2Controller
        const val PREFIX = "/v1/portcall"
    }

    fun getPortcall(portcallId: String): Portcall? =
        restTemplate.getForObject(
            url = "$PREFIX/$portcallId",
        )

    fun getPortcallByImo(imoNumber: String): Portcall? =
        restTemplate.getForObject(
            url = "$PREFIX/imo/$imoNumber",
        )

    fun getAllPortcallsByImo(
        port: String,
        imoNumber: String,
    ): List<Portcall> =
        restTemplate.getForObject<Array<Portcall>>(
            url = "$PREFIX/imo/all/$port/$imoNumber",
        ).toList()
}
