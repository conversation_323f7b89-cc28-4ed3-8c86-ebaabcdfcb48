package nl.teqplay.portcallplus.api.model

import com.fasterxml.jackson.annotation.JsonCreator

enum class PortcallPurpose {
    CARGO,
    <PERSON>SSENGERS,
    BUNKER,
    SUPPLIES,
    CREW,
    REPAIR,
    OFFSHORE,
    UNUSED,
    OTHER,
    LOADING,
    DISCHARGE,
    DISCH<PERSON><PERSON>_CRUDE_OIL,
    CUSTOMS_CLEARANCE,
    A<PERSON>ITING_ORDERS,
    BUN<PERSON><PERSON>NG,
    REPAIRS,
    CARGO_TANK_CLEANING,
    SPARES,
    PROVISION_STORES,
    TECHNICIAN,
    DE_SLOPPING,
    STS,
    SLUDGE_REMOVAL,
    CASH_TO_MASTER,
    OTHERS,
    UNKNOWN,
    ;

    companion object {
        @JsonC<PERSON>
        @JvmStatic
        fun getByName(type: String): PortcallPurpose {
            var matchingType = UNKNOWN
            values().forEach { purpose ->

                val formattedName = purpose.name.replace("_", "").uppercase()
                val types =
                    type.split("/").map {
                        it.replace("-", "")
                            .replace(" ", "")
                            .replace("_", "")
                            .uppercase()
                    }

                when {
                    types.any { it.startsWith(formattedName) || formattedName.startsWith(it) } -> return purpose
                    types.any { it.contains(formattedName) || formattedName.startsWith(it) } -> matchingType = purpose
                }
            }
            return matchingType
        }
    }
}
