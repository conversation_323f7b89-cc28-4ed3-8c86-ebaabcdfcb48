package nl.teqplay.portcallplus.api.model

data class <PERSON>call<PERSON><PERSON>s(
    val source: PortcallAliasName,
    val alias: String,
)

enum class Portcall<PERSON>liasName {
    // used for all portcalls received via S5 Simply5 nominations
    @Deprecated("Old customer, still here for the database entries")
    SIMPLY5_NOMINATION,

    // used for all portcalls received via vopak nominations which are NOR Tendered
    VOPAK_NOMINATION,
    VOPAK_NOMINATION_PENJURU,
    VOPAK_NOMINATION_BANYAN,
    VOPAK_NOMINATION_SEBAROK,
    VOPAK_NOMINATION_SAKRA,

    // used for all portcalls received via vopak nominations which have no NOR Tendered
    VOPAK_NO_NOR,
    V<PERSON><PERSON><PERSON>_NO_NOR_PENJURU,
    VOPAK_NO_NOR_BANYAN,
    VOPAK_NO_NOR_SEBAROK,
    VOPAK_NO_NOR_SAKRA,

    // used for all portcalls received via NxtPort's VesselStay service
    NXTPORT,

    // used for all portcalls received via NxtPort's VesselStay service
    @Deprecated("Use NXTPORT instead")
    NXTPORT_V2,

    // used for all portcalls received via PortBase API for NLRTM and NLAMS
    PORTBASE,
    PORTBASE_CLIENT_REFERENCE_NUMBER,

    // used for all portcalls received via DIGITRAFFIC API for Finland (FIVSS and FIHEL)
    DIGITRAFFIC,
    CORPUS_CHRISTI,

    // Used for all portcalls received via the Nominations queue which have the topic tma
    @Deprecated("Old customer, still here for the database entries")
    TMA_NOMINATION,
}
