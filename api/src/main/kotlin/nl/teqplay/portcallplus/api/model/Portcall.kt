package nl.teqplay.portcallplus.api.model

import com.fasterxml.jackson.annotation.JsonIgnore
import nl.teqplay.platform.model.area.PilotBoardingPlaceType
import org.bson.codecs.pojo.annotations.BsonId
import java.util.Date

/**
 * This is a wrapper class around the [Portcall] defined by the platform api. This is done only to add a creation
 * and updateTimestamp to the bean
 */
data class Portcall(
    /** The portcall reference number, UCRN for Rotterdam  */
    @BsonId
    val portcallId: String,
    /** The list of aliases of the portcall, this portcall is also known as...
     *  Is a List of all portcall aliasses, each source should only be once in the List
     */
    val portcallAlias: Set<PortcallAlias> = emptySet(),
    /** The UNLOCODE for the port this portcall is about  */
    val port: String,
    /** The IMO code of the sea vessel related to this portcall  */
    val imo: String,
    /** The task that has created/updated this portcall */
    val source: ScheduledTaskType = ScheduledTaskType.UNKNOWN,
    /** startTime of this portCall. Either the first event or the first portAta (if found)  */
    val startTime: Date,
    /** Where the starttime is expected to be to */
    val startTimeType: UpdateType = UpdateType.NOMINATION,
    /** The status of the portcall like INBOUND, ALONGSIDE, OUTBOUND, SHIFTING  */
    val status: PortcallStatus = PortcallStatus.UNKNOWN,
    /** The last [eosAtdTime] or if not found [portAtdTime]  */
    val endTime: Date? = null,
    /** timestamp of entering the port  */
    val portAtaTime: Date? = null,
    /** timestamp of leaving the port  */
    val portAtdTime: Date? = null,
    /** The estimated Departure time  */
    val etd: Date? = null,
    /** Boolean indicating whether the pilot boarding place was already entered  */
    val enteredPBP: Boolean = false,
    /** which pilot boarding place is relevant  */
    val pilotBoardingPlaceType: PilotBoardingPlaceType = PilotBoardingPlaceType.UNKNOWN,
    /** The vessel agent related to this portcall  */
    val vesselAgent: String? = null,
    /** The origin before this visit  */
    val originUnlocode: String? = null,
    /** The destination after this visit  */
    val destinationUnlocode: String? = null,
    /** What is the vessel going to do */
    val purpose: Set<PortcallPurpose> = setOf(),
    /** The visit locations during the portcall  */
    val visits: List<PortcallVisit> = listOf(),
    /** timestamp of entering the End Of Sea passage  */
    val eosAtaTime: Date? = null,
    /** timestamp of leaving the End Of Sea passage  */
    val eosAtdTime: Date? = null,
    /** Product. Directly related to the portcall's port. */
    val product: String? = null,
    /** Pilot. Directly related to the portcall's port. */
    val pilot: String? = null,
    /** Towing company name. Directly related to the portcall's port. */
    val towingCompany: String? = null,
) : ServiceModel, DataModel() {
    @JsonIgnore
    override val _id = portcallId

    /**
     * Compares this [Portcall] with the given [other] in order of:
     * 1. [Portcall.startTime] Most recent portcall first
     * 2. [Portcall.portcallId] Most recent portcall has an incremented portcallId
     * #. [Portcall.updateTimestamp] Most recent update timestamp
     */
    override fun compareTo(other: DataModel): Int {
        if (other is Portcall) {
            var compare = other.startTime.compareTo(this.startTime)
            if (compare == 0) {
                compare = other.portcallId.compareTo(this.portcallId)
                if (compare == 0) {
                    compare = other.updateTimestamp.compareTo(this.updateTimestamp)
                }
            }
            return compare
        }
        return super.compareTo(other)
    }

    companion object {
        const val IDPREFIX_NLRTM = "NLRTM"
        const val IDPREFIX_NLAMS = "NLAMS"
        const val IDPREFIX_ANR = "BEANR"
        const val IDPREFIX_TNZ = "NLTNZ"
        const val IDPREFIX_VLI = "NLVLI"
        const val IDPREFIX_GNE = "BEGNE"
        const val IDPREFIX_SGSIN = "SGSIN"
        const val IDPREFIX_FIHEL = "FIHEL"
        const val IDPREFIX_FIVSS = "FIVSS"
        const val IDPREFIX_USHOU = "USHOU"
        const val IDPREFIX_USCRP = "USCRP"
        const val IDPREFIX_OST = "BEOST"
        const val IDPREFIX_ZEE = "BEZEE"
        const val IDPREFIX_WLB = "BEWLB"
    }
}

enum class PortcallStatus {
    INBOUND,
    OUTBOUND,
    ALONGSIDE,
    SHIFTING,
    UNKNOWN,
}

/**
 * This enum describes where the starttime is expected to be to
 *
 */
enum class UpdateType {
    // Expected time at berth
    BERTH,

    // Expected time at pilotboardingplace
    PILOTBOARDINGPLACE,

    // Expected time at anchorage
    ANCHORAGE,

    // expected port entry time
    PORT,

    // Location is not known
    NOMINATION,
    NOMINATION_TERMINAL,
    NOMINATION_AGENT,
    END_OF_SEA,
}
