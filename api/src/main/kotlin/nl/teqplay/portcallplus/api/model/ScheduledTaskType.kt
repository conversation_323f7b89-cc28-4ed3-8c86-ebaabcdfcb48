package nl.teqplay.portcallplus.api.model

import com.fasterxml.jackson.annotation.JsonIgnore
import nl.teqplay.portcallplus.api.model.Portcall.Companion.IDPREFIX_ANR
import nl.teqplay.portcallplus.api.model.Portcall.Companion.IDPREFIX_FIHEL
import nl.teqplay.portcallplus.api.model.Portcall.Companion.IDPREFIX_FIVSS
import nl.teqplay.portcallplus.api.model.Portcall.Companion.IDPREFIX_GNE
import nl.teqplay.portcallplus.api.model.Portcall.Companion.IDPREFIX_NLAMS
import nl.teqplay.portcallplus.api.model.Portcall.Companion.IDPREFIX_NLRTM
import nl.teqplay.portcallplus.api.model.Portcall.Companion.IDPREFIX_SGSIN
import nl.teqplay.portcallplus.api.model.Portcall.Companion.IDPREFIX_TNZ
import nl.teqplay.portcallplus.api.model.Portcall.Companion.IDPREFIX_VLI

/**
 * Defines and embeds the properties for the various scheduled tasks supported in this Application
 */
enum class ScheduledTaskType {
    /**
     * Task for the LIS Scrapper run every few minutes as configured
     */
    LIS_SCRAPPER,

    /**
     * Task for the Enigma Scraper on incoming vessels, run every few minutes as configured
     */
    ENIGMA_SCRAPER_INCOMING,

    /**
     * Task for the Enigma Scraper on outgoing vessels, run every few minutes as configured
     */
    ENIGMA_SCRAPER_OUTGOING,

    /**
     * Task for an update on all vessels every 2 hours in 240 nm without a portcall around BEANR and destination to BEANR
     */
    NXTPORT_V2,

    /**
     * Task for an update on all vessels every 2 hours in 240 nm without a portcall around BEANR and destination to BEANR
     */
    @Deprecated("Old customer, still here for the database entries")
    NM240_2HOURS,

    /**
     * Task for an update on all vessels every 24 hours which is within 110nm out of the port and not heading to BEANR,
     * with no
     */
    @Deprecated("Old customer, still here for the database entries")
    NM110_24HOURS,

    /**
     * Task for an update on all vessels every 12 hours which is within 20nm out of the sea pbp of wandelaar without a
     * portcall and not moving
     */
    @Deprecated("Old customer, still here for the database entries")
    NM20_12HOURS,

    /**
     * Task for an update on all vessels every 60 minutes in 20nm with a portcall around the 3 pilotboardingplace and center
     * of the BEANR PORT, currently not on a berth and destination to BEANR
     */
    @Deprecated("Old customer, still here for the database entries")
    NM20_2HOURS_MOVING,

    /**
     * Task for an update on all non-moving vessels every hour in 20nm with a portcall around the 3 pilotboardingplace
     * and center of the BEANR PORT, currently not on a berth and destination to BEANR
     */
    @Deprecated("Old customer, still here for the database entries")
    NM20_4HOURS_NO_BERTH,

    /**
     * Task for an update on all non-moving vessels every hour in 20nm with a portcall around the 3 pilotboardingplace
     * and center of the BEANR PORT, currently on a berth and destination to BEANR
     */
    @Deprecated("Old customer, still here for the database entries")
    NM20_8HOURS,

    /**
     * Task for an update on all vessels every 8 hours which is further than 10 nm, but within 110 nm out of
     * pilotboardingplace and center of the BEANR PORT, with a portcall of BEANR
     */
    @Deprecated("Old customer, still here for the database entries")
    NM10TO110_8HOURS,

    /**
     * SG-MDH Visit Arrival Declaration task, updates portcalls from Singapore
     */
    SG_MDH_VISIT_ARRIVAL_DECLARATION,

    /**
     * SG-MDH Visit Arrival Declaration task, updates portcalls from Singapore
     */
    SG_MDH_DUE_TO_ARRIVE,

    /**
     * SG-MDH Due To Depart task, updates portcalls from Singapore
     */
    SG_MDH_DUE_TO_DEPART,

    /**
     * Task to sync portcalls from one Portcall+ environment to the other (e.g. live to dev)
     */
    PORTCALL_SYNC,

    /**
     * Portbase taskType with [cycleDuration] of 0 to have it only execute once
     */
    VOPAK_PORTBASE,
    S5_PORTBASE,
    OUDKERK_PORTBASE,
    MARITEAM_PORTBASE,
    IAMCONNECTED_PORTBASE,

    /**
     * Indicator used to map portcalls created based on a PortcallGeneratorModel
     */
    PORTCALL_GENERATOR,

    /**
     * Indicator used to map portcalls created based on a [Simply5NominationsQueueHandler]
     */
    @Deprecated("Old customer, still here for the database entries")
    SIMPLY5_NOMINATION,

    /**
     * Indicator used to handle Finnish portcalls
     */
    DIGITRAFFIC,

    /**
     * Indicator used to process Vopak nominations for portcalls in SGSIN and USHOU
     */
    @Deprecated("No longer used, still here for the database entries")
    VOPAK_NOMINATION,

    /**
     * Indicator used to process TMA nominations for portcalls in USHOU
     * TMA stands for Texas Marine Agency which is an agency
     */
    @Deprecated("Old customer, still here for the database entries")
    TMA_NOMINATION,

    /**
     * Indicator used to handle Harbor Lights portcalls
     */
    @Deprecated("Old customer, still here for the database entries")
    HARBORLIGHTS,

    /**
     * Indicator used to count the number of MTurk HIT requests made
     */
    MTURK,

    /**
     * Indicator used for portcalls gotten from Smartfleet
     */
    SMARTFLEET,

    /**
     * Indicator used to process Corpus Christi port nominations
     */
    CORPUS_CHRISTI,

    /**
     * Task to map to tasks that are not known (due to deprecation)
     */
    UNKNOWN,

    ;

    /**
     * Returns true if this is one of the VesselStay task, else false
     */
    @JsonIgnore
    fun isVesselStayTask(): Boolean =
        this in
            listOf(
                NXTPORT_V2,
                NM10TO110_8HOURS,
                NM240_2HOURS,
                NM110_24HOURS,
                NM20_12HOURS,
                NM20_2HOURS_MOVING,
                NM20_4HOURS_NO_BERTH,
                NM20_8HOURS,
            )

    /**
     * Returns true if this is one of the SGSIN task, else false
     */
    @JsonIgnore
    fun isSGSINTask(): Boolean =
        this in
            listOf(
                SG_MDH_DUE_TO_ARRIVE,
                SG_MDH_VISIT_ARRIVAL_DECLARATION,
                SG_MDH_DUE_TO_DEPART,
            )

    /**
     * Returns true if this is one of the Portbase task, else false
     */
    @JsonIgnore
    fun isPortbaseTask(): Boolean =
        this in
            listOf(
                OUDKERK_PORTBASE,
                S5_PORTBASE,
                VOPAK_PORTBASE,
                MARITEAM_PORTBASE,
                IAMCONNECTED_PORTBASE,
            )

    companion object {
        /**
         * The list of all currently non-deprecated [ScheduledTaskType].
         */
        fun getAllTasks() =
            values().filter {
                it != UNKNOWN &&
                    !it::class.java.getField(it.name).isAnnotationPresent(Deprecated::class.java)
            }

        /**
         * Gets all [ScheduledTaskType] by the given [port]
         */
        fun getTasksByPort(port: String): List<ScheduledTaskType> {
            return values().filter {
                when (port) {
                    IDPREFIX_SGSIN -> it.isSGSINTask() || it == VOPAK_NOMINATION
                    IDPREFIX_ANR -> it in listOf(LIS_SCRAPPER, PORTCALL_SYNC, MTURK) || it.isVesselStayTask()
                    IDPREFIX_TNZ, IDPREFIX_VLI, IDPREFIX_GNE ->
                        it in
                            listOf(
                                ENIGMA_SCRAPER_INCOMING,
                                ENIGMA_SCRAPER_OUTGOING,
                            )
                    IDPREFIX_NLAMS, IDPREFIX_NLRTM -> it.isPortbaseTask()
                    IDPREFIX_FIHEL, IDPREFIX_FIVSS -> it == DIGITRAFFIC
                    else -> false
                }
            }
        }
    }
}
