# PortcallPlus
> Project started at: 2019

This project hosts a micro/mini-service, currently being a service 
handler for portcall updates in Antwerpen (BEANR) by connecting to NxtPort 
and LIS. 

### Prerequisites

* [Intellij](https://www.jetbrains.com/idea/download/)
* [Gradle](https://gradle.org/) (5.4 or higher)
* [Ko<PERSON><PERSON>](https://kotlinlang.org/) (1.3.0 or higher)
* [MongoDB](https://docs.mongodb.com/getting-started/shell/installation/) ([linux](https://www.digitalocean.com/community/tutorials/how-to-install-mongodb-on-ubuntu-16-04))
* [JavaSDK](http://www.oracle.com/technetwork/java/javase/downloads/jdk8-downloads-2133151.html)

## Getting started

1) Clone the repo:

```
    $ git clone https://[USERNAME]@bitbucket.org/teqplay/portcallplus.git
```

2) Configure Gradle settings, credentials are needed for the private S3 Maven repository. Look up the AWS credentials for the repository (search for 'maven' in LastPass), and add the following to your `~/.gradle/gradle.properties`:

```
s3_access_key=INSERT ACCESS KEY HERE
s3_secret_key=INSERT SECRET KEY HERE
```
    
3) cd `/portcallplus`

```
    ./gradle build
```
    
4) run mongodb

```
    mongod //run the mongodb
    mvn tomcat7:run
```

5) Support

  - *Login* could be done using Basic Auth by the credentials of any user using in the corresponding linked backend. In POSTMAN (or equivalent), has a header: `Authorization`: `Basic <token>`
      - `Dev`: Any user in https://backendprontodev.teqplay.nl/
      - `Live`: Any user in https://backendpronto.teqplay.nl/
  - *Swagger* documentation can be found here: 
      - `Dev`: http://portcallplusdev.eu-west-1.elasticbeanstalk.com/swagger-ui.html#
      - `Live`: http://portcallplus-env.eu-west-1.elasticbeanstalk.com/swagger-ui.html#
  - Description of all *scheduled tasks* running: 
      - `Dev`: http://portcallplusdev.eu-west-1.elasticbeanstalk.com/v1/status?detailed=true
      - `Live`: http://portcallplus-env.eu-west-1.elasticbeanstalk.com/v1/status?detailed=true
  - If a task is reported to be down, it could be *force triggered* using the `POST` call (and equivalent for Live): 
      - `Dev`: http://portcallplusdev.eu-west-1.elasticbeanstalk.com/swagger-ui.html#/generic-controller/forceRunTaskUsingPOST
      - All vessel information processed by various tasks are saved in the `activity` collection in mongo db. This can be used to track if a specific task did not fetch any vessel updates from the external service:
      http://portcallplusdev.eu-west-1.elasticbeanstalk.com/swagger-ui.html#/activity-controller/getActivitiesPerDayUsingGET

          If you are using Postman, use the script in [here](https://bitbucket.org/teqplay/portcallplus/src/develop/src/main/resources/postmanVisualizer/categorizeActivity.js) to see a graph showing the amount of vessel updates (by imo) seen per task similar to: 
          
![ServiceActivityGraph](https://bitbucket.org/teqplay/portcallplus/raw/64e5155350d222e9fad552a59bc9385f3bccec10/src/main/resources/postmanVisualizer/ServiceActivityGraph.png)

  - This service connects to `NxtPort` service from Antwerp PCS via a host of tasks abbreviated by `NM<number>_<taskFrequency>`. E.g. `NM240_2HOURS` stands for 240 nautical mile task running every 2 hours. One could check all the vessels that are parsed by each task by using the `GET` call (and equivalent for Live):
      - `Dev`: http://portcallplusdev.eu-west-1.elasticbeanstalk.com/swagger-ui.html#/vessel-stay-controller/getVesselImosForTaskUsingGET
  - All portcalls for a specific vessel imo can be done by the `GET` call:
      - `Dev`: http://portcallplusdev.eu-west-1.elasticbeanstalk.com/swagger-ui.html#/portcall-controller/getPortcallsByImoUsingGET
      - `Live`: http://portcallplus-env.eu-west-1.elasticbeanstalk.com/swagger-ui.html#/portcall-controller/getPortcallsByImoUsingGET
        
### Configurations
- They are saved in lastpass

## Using version numbers
Run `./gradlew releaseVersion` to automatically update SNAPSHOT version 
to stable version.
Eg. If your project version is 1.0.0-SNAPSHOT, then `./gradlew releaseVersion`
will update the version in gradle.properties to 1.0.0.

Run `./gradlew patchVersion` to automatically update Stable version to 
the next patch version.
Eg. If your version is 1.0.0, then `./gradlew patchVersion` will update 
the version in gradle.properties to 1.0.1-SNAPSHOT

## Deploying
-- Update how we can deploy this project to Amazon Elastic Beanstalk

## Support

Please [open an issue](https://bitbucket.org/teqplay/portcallplus/issues/new) for support.

## Licensing

The code in this project is licensed under Teqplay.

## credentials
The credentials are managed within Teqplay. 