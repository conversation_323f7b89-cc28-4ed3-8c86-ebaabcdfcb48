global:
  storageClass: "gp2"
  namespaceOverride: "portcall"

image:
  repository: 050356841556.dkr.ecr.eu-west-1.amazonaws.com/portcallplus

resources:
  requests:
    cpu: 0.02
    memory: 2Gi
  limits:
    memory: 2Gi

mongodb:
  enabled: false

logs:
  - name: lis-incoming
    file: /var/log/app/lis-incoming.out
  - name: lis-outgoing
    file: /var/log/app/lis-outgoing.out
  - name: updated-portcalls-returned
    file: /var/log/app/updatedPortcallsReturned.out
  - name: nominations
    file: /var/log/app/nominations.out