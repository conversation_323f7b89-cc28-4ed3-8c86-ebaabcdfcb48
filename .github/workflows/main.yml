name: Teqplay Standard Workflow
run-name : ${{ github.event.head_commit.message }}
on: [push]

jobs:
  external-main:
    name: Teqplay standard workflow
    uses: teqplay/actions/.github/workflows/backend-standard.yml@master
    with:
      publish_libraries: true
      publish_libraries_submodule: "api:"
      dependency_track: true
      override_gradle_properties: false
    secrets:
      aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
      aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      cm_username: ${{ secrets.CM_USERNAME }}
      cm_password: ${{ secrets.CM_PASSWORD }}
      dt_api_key: ${{ secrets.DT_API_KEY }}
