{"lastIndex": 101652975015625060, "clientSegment": [0, 128], "updates": [{"index": 101652975015625040, "timestamp": "2019-03-04T18:43:31.099Z", "before": null, "after": {"crn": "NLRTM19950030", "portOfCall": {"port": {"name": "Rotterdam", "locationUnCode": "NLRTM", "countryUnCode": "NL", "euPort": true}, "portAuthority": {"fullName": "Havenbedrijf Rotterdam N.V.", "portAuthorityId": "HbR", "shortName": "HBRRTM", "emailAddress": "<EMAIL>", "address": "Wilhelminakade 909", "city": "Rotterdam", "countryUnCode": "NL", "countryName": "Netherlands", "zipCode": "3072 AP", "phoneNumber": "+31-10-2521195", "faxNumber": "+31-10-2521000", "contact": "<PERSON>", "customsEORINumber": "NL24354561", "ean": null}, "customsOffice": {"name": "Rotterdam Haven/Kantoor Ma<PERSON>vlak<PERSON>", "unCode": "NL000396"}, "ataReported": true, "atdReported": true, "atdPortReported": true, "paDeclarationRequired": true, "swDeclarationRequired": true, "wasteEnabled": true, "dangerousGoodsEnabled": true, "harbourDuesEnabled": false, "orderNauticalServicesEnabled": true, "nauticalServicesApplicable": true, "enableNotificationsToPa": false, "paxDisabled": false, "msvDisabled": false, "authoriseOrganisationsDisabled": false, "notRequiringTugBoats": false}, "vessel": {"imoCode": "9571117", "name": "AQUA 6", "radioCallSign": "9V8340", "motUnCode": "155X", "motName": "Other special tanker", "summerDeadWeight": 6510, "maxWidth": 18, "flagCode": "SGP", "flagCountryUnCode": "SG", "netTonnage": 1921, "grossTonnage": 4855, "registrationPlaceUnloCode": "SGSIN", "registrationPlaceName": "Singapore", "registrationDate": "2010-10-21", "mmsiNumber": "563765000", "fullLength": 99.7}, "owner": {"fullName": "Ship Operator 1", "portAuthorityId": "SO1_R", "shortName": "SO1", "emailAddress": "r.van.winger<PERSON>@portbase.com", "address": "Ship Operatorlaan 1", "city": "Ship Operatorstad", "countryUnCode": "NL", "countryName": "Netherlands", "zipCode": "1111SO", "phoneNumber": "0102522288", "faxNumber": null, "contact": "test centrum", "customsEORINumber": "NL123456789", "ean": null}, "declarant": {"fullName": "Ship Operator 1", "portAuthorityId": "SO1_R", "shortName": "SO1", "emailAddress": "r.van.winger<PERSON>@portbase.com", "address": "Ship Operatorlaan 1", "city": "Ship Operatorstad", "countryUnCode": "NL", "countryName": "Netherlands", "zipCode": "1111SO", "phoneNumber": "0102522288", "faxNumber": null, "contact": "test centrum", "customsEORINumber": "NL123456789", "ean": null}, "cargoDeclarants": [{"fullName": "Ship Operator 1", "portAuthorityId": "SO1_R", "shortName": "SO1", "emailAddress": "r.van.winger<PERSON>@portbase.com", "address": "Ship Operatorlaan 1", "city": "Ship Operatorstad", "countryUnCode": "NL", "countryName": "Netherlands", "zipCode": "1111SO", "phoneNumber": "0102522288", "faxNumber": null, "contact": "test centrum", "customsEORINumber": "NL123456789", "ean": null}, {"fullName": "Cargo Handling Agent 1", "portAuthorityId": "CHA1", "shortName": "CHA1", "emailAddress": "<EMAIL>", "address": "Cargo Handling Agentlaan 1", "city": "Cargo Handling Agentdorp", "countryUnCode": "NL", "countryName": "Netherlands", "zipCode": "1111CH", "phoneNumber": "0102522255", "faxNumber": "0102522250", "contact": "TC", "customsEORINumber": "NL100001897", "ean": null}], "visitDeclaration": {"clientReferenceNumber": "GATSHIP123", "etaFirstEntryEu": null, "arrivalVoyage": {"voyageNumber": "123456789", "carrier": {"customsId": null, "name": "China Ocean Shipping (COSCO)", "scacCode": "COSU", "smdgCode": "COS"}}, "previousPorts": [{"id": "f3b6c6b0-6267-4753-98c0-1e75fee1c14c", "arrival": "2019-03-03T11:00:00Z", "departure": "2019-03-04T11:00:00Z", "port": {"name": "Hamburg", "locationUnCode": "DEHAM", "countryUnCode": "DE", "euPort": true}, "portFacilityVisits": [{"portFacility": {"code": "DEHAM-0005", "name": "Hamburg / BLOHM + VOSS  GMBH"}, "arrivalDate": "2019-03-03", "departureDate": "2019-03-03", "securityLevel": "SL1", "additionalSecurityMeasures": null}, {"portFacility": {"code": "DEHAM-0226", "name": "Hamburg / RollBo Transport GmbH"}, "arrivalDate": "2019-03-03", "departureDate": "2019-03-04", "securityLevel": "SL1", "additionalSecurityMeasures": null}]}, {"id": "9912d976-caf8-4b4e-9bd0-f645e2eb5a3b", "arrival": "2019-03-01T11:00:00Z", "departure": "2019-03-02T11:00:00Z", "port": {"name": "London", "locationUnCode": "GBLON", "countryUnCode": "GB", "euPort": true}, "portFacilityVisits": [{"portFacility": {"code": "GBLON-0097", "name": "Docklands Wharf"}, "arrivalDate": "2019-03-01", "departureDate": "2019-03-02", "securityLevel": "SL1", "additionalSecurityMeasures": null}]}], "portVisit": {"pilotStation": {"code": "MC", "name": "Maascenter", "atSea": true}, "entryPoint": {"code": "LL", "name": "Lage Licht", "atSea": null}, "portEntry": null, "etaPort": "2019-03-13T11:00:00Z", "firstMovement": {"vesselDraft": 12, "vesselMasterName": "<PERSON><PERSON>", "numberOfCrew": 20, "numberOfPassengers": 1, "cargo": "CONTAINERIZED", "pilotExemption": null, "offStandardBeam": null, "pilotService": {"required": true, "serviceProvider": {"name": "Loodswezen", "portAuthorityId": "LRR"}, "remarks": "blabla"}, "order": null, "stormPilotageInformation": null}, "passingThroughTugboats": null, "defectTypes": [], "defectTypeRemarks": null, "berthVisits": [{"id": "ef09cc29-d20b-43dd-879b-1ef579828fb1", "berth": {"name": "EUROH APM TERMINALS", "code": "G8200", "terminalCode": "19387327", "berthGroupCode": "EUROH APM TERMINALS", "buoy": false, "partyToNotify": null}, "eta": "2019-03-14T11:00:00Z", "requestedEta": null, "ata": null, "etd": "2019-03-15T11:00:00Z", "requestedEtd": null, "atd": null, "mooring": "PORT_SIDE", "remarks": null, "visitPurposes": ["DISCHARGE"], "bollardFrom": null, "bollardTo": null, "tugboatAtArrival": {"required": true, "serviceProvider": {"name": "Fairplay", "portAuthorityId": "FAIRPLAY"}, "requiredQuantity": "PILOT_DETERMINES", "remarks": "blabla"}, "boatmenAtArrival": {"required": true, "serviceProvider": {"name": "V.I.O.S.", "portAuthorityId": "VIOS"}, "remarks": "blabla"}, "boatmenAtDeparture": {"required": true, "serviceProvider": {"name": "V.I.O.S.", "portAuthorityId": "VIOS"}, "remarks": "blabla"}, "tugboatAtDeparture": {"required": true, "serviceProvider": {"name": "Fairplay", "portAuthorityId": "FAIRPLAY"}, "requiredQuantity": "PILOT_DETERMINES", "remarks": "blabla"}, "nextMovement": {"vesselDraft": 12, "vesselMasterName": "<PERSON><PERSON>", "numberOfCrew": 20, "numberOfPassengers": 1, "cargo": "CONTAINERIZED", "pilotExemption": null, "offStandardBeam": null, "pilotService": {"required": true, "serviceProvider": {"name": "Loodswezen", "portAuthorityId": "LRR"}, "remarks": "blabla"}, "order": null, "stormPilotageInformation": null}}, {"id": "94ef8a92-de9f-420b-9b43-8a21b21b3c79", "berth": {"name": "AMAZH ECT DDE", "code": "G8157", "terminalCode": "19387239", "berthGroupCode": "AMAZH ECT DDE", "buoy": false, "partyToNotify": null}, "eta": "2019-03-16T11:00:00Z", "requestedEta": null, "ata": null, "etd": "2019-03-17T11:00:00Z", "requestedEtd": null, "atd": null, "mooring": "STARBOARD_SIDE", "remarks": "blabla", "visitPurposes": ["BUNKERING", "DISCHARGE"], "bollardFrom": 10, "bollardTo": 50, "tugboatAtArrival": {"required": true, "serviceProvider": {"name": "Fairplay", "portAuthorityId": "FAIRPLAY"}, "requiredQuantity": "PILOT_DETERMINES", "remarks": "blabla"}, "boatmenAtArrival": {"required": true, "serviceProvider": {"name": "KRVE", "portAuthorityId": "RVE"}, "remarks": "blabla"}, "boatmenAtDeparture": {"required": true, "serviceProvider": {"name": "KRVE", "portAuthorityId": "RVE"}, "remarks": "blabla"}, "tugboatAtDeparture": {"required": true, "serviceProvider": {"name": "<PERSON><PERSON><PERSON><PERSON>", "portAuthorityId": "SVW"}, "requiredQuantity": "PILOT_DETERMINES", "remarks": "blabla"}, "nextMovement": {"vesselDraft": 15, "vesselMasterName": "<PERSON><PERSON>", "numberOfCrew": 20, "numberOfPassengers": 1, "cargo": "CONTAINERIZED", "pilotExemption": null, "offStandardBeam": null, "pilotService": {"required": true, "serviceProvider": {"name": "Loodswezen", "portAuthorityId": "LRR"}, "remarks": "blabla"}, "order": null, "stormPilotageInformation": null}}], "dangerousGoodsLoading": false, "dangerousGoodsDischarge": true, "dangerousGoodsTransit": false, "possibleAnchorage": false, "vesselInspectionRequired": false, "shipConfiguration": null, "exitPoint": {"code": "LL", "name": "Lage Licht", "atSea": null}, "etdPort": "2019-03-18T11:00:00Z", "atdPort": "2019-03-18T11:00:00Z", "ataPort": "2019-03-18T05:00:00Z"}, "departureVoyage": {"voyageNumber": "987654321", "carrier": {"customsId": null, "name": "China Ocean Shipping (COSCO)", "scacCode": "COSU", "smdgCode": "COS"}}, "nextPorts": [{"id": "b25baf11-b1fb-4f79-8a8e-aa298b0a7087", "arrival": "2019-03-19T11:00:00Z", "departure": "2019-03-20T11:00:00Z", "port": {"name": "Antwerpen", "locationUnCode": "BEANR", "countryUnCode": "BE", "euPort": true}, "customsOffice": {"name": "ANTWERPEN DAE", "unCode": "BE101000"}}, {"id": "f464df2f-1acb-44d2-98fa-c06a2ae71881", "arrival": "2019-03-21T11:00:00Z", "departure": "2019-03-22T11:00:00Z", "port": {"name": "Le Havre", "locationUnCode": "FRLEH", "countryUnCode": "FR", "euPort": true}, "customsOffice": {"name": "Le Havre Port bureau", "unCode": "FR002300"}}]}, "securityDeclaration": null, "dangerousGoodsDeclaration": null, "wasteDeclaration": null, "shipStoresDeclaration": null, "paxDeclarationSummaries": [], "declarationStatuses": {"VISIT": "REJECTED"}, "etaPortAis": null, "ignoreEtaPortAis": false, "cancelled": false, "orderIncomingMovement": false}, "updateType": null, "triggeredByRecipient": true}]}