{"lastIndex": 112523172407345170, "lastTimestamp": "2024-05-29T07:22:32.407Z", "clientSegment": [0, 128], "updates": [{"after": {"crn": "NLRTM24012220", "portOfCall": {"port": {"name": "Rotterdam", "locationUnCode": "NLRTM", "countryUnCode": "NL", "euPort": true}, "portAuthority": null, "customsOffice": null, "ataReported": false, "atdReported": false, "atdPortReported": false, "paDeclarationRequired": false, "swDeclarationRequired": false, "wasteEnabled": false, "dangerousGoodsEnabled": false, "harbourDuesEnabled": false, "orderNauticalServicesEnabled": false, "enableNotificationsToPa": false, "authoriseOrganisationsDisabled": false, "tugBoatsRequired": false, "nauticalServicesApplicable": true, "notRequiringTugBoats": true}, "vessel": {"imoCode": "9224166", "name": null, "radioCallSign": null, "motUnCode": null, "motName": null, "summerDeadWeight": null, "maxWidth": null, "flagCode": null, "flagCountryUnCode": null, "netTonnage": null, "grossTonnage": null, "registrationPlaceUnloCode": null, "registrationPlaceName": null, "registrationDate": null, "mmsiNumber": null, "fullLength": null, "emailAddress": null, "statCode5": null}, "owner": {"fullName": "Vertom Agencies B.V.", "shortName": null, "iamConnectedId": null, "portAuthorityId": "VERTOM", "emailAddress": null, "address": null, "city": "R<PERSON>on", "zipCode": null, "countryUnCode": null, "countryName": "Netherlands", "phoneNumber": null, "faxNumber": null, "contact": null, "customsEORINumber": null, "ean": null, "chamberOfCommerceNumber": null, "scacCode": null}, "declarant": {"fullName": "Vertom Agencies B.V.", "shortName": null, "iamConnectedId": null, "portAuthorityId": "VERTOM", "emailAddress": null, "address": null, "city": "R<PERSON>on", "zipCode": null, "countryUnCode": null, "countryName": "Netherlands", "phoneNumber": null, "faxNumber": null, "contact": null, "customsEORINumber": null, "ean": null, "chamberOfCommerceNumber": null, "scacCode": null}, "cargoDeclarants": [], "visitDeclaration": {"clientReferenceNumber": "10241641", "vesselEmailAddress": null, "etaFirstEntryEu": "2024-05-31T12:00:00Z", "arrivalVoyage": {"voyageNumber": null, "carrier": null}, "previousPorts": [{"id": "eeae170e-0341-46fa-80d4-4d27fc03916a", "arrival": "2024-05-26T10:00:00Z", "departure": "2024-05-28T10:00:00Z", "port": {"name": "<PERSON><PERSON><PERSON>", "locationUnCode": "NOZFN", "countryUnCode": "NO", "euPort": false}, "portFacilityVisits": null}, {"id": "33ac19cd-a626-4984-9a40-411791d60993", "arrival": "2024-05-11T10:00:00Z", "departure": "2024-05-24T10:00:00Z", "port": {"name": "Glomfjord", "locationUnCode": "NOGLO", "countryUnCode": "NO", "euPort": false}, "portFacilityVisits": null}, {"id": "d9f1e6cd-3f2f-48bf-bebc-ade4c5bbdca1", "arrival": "2024-05-03T10:00:00Z", "departure": "2024-05-06T10:00:00Z", "port": {"name": "Vlissingen", "locationUnCode": "NLVLI", "countryUnCode": "NL", "euPort": true}, "portFacilityVisits": null}, {"id": "255cbd61-6c2d-4778-b18f-6cd93c314012", "arrival": "2024-04-30T10:00:00Z", "departure": "2024-05-01T10:00:00Z", "port": {"name": "Drogheda", "locationUnCode": "IEDRO", "countryUnCode": "IE", "euPort": true}, "portFacilityVisits": null}, {"id": "6f4ddea7-3d5c-4b12-85d9-e5f794c2fbd3", "arrival": "2024-04-17T10:00:00Z", "departure": "2024-04-24T10:00:00Z", "port": {"name": "Gdynia", "locationUnCode": "PLGDY", "countryUnCode": "PL", "euPort": true}, "portFacilityVisits": null}, {"id": "726363d1-8ab2-45a4-8abc-a861adec7fd0", "arrival": "2024-04-15T10:00:00Z", "departure": "2024-04-16T10:00:00Z", "port": {"name": "Szczecin", "locationUnCode": "PLSZZ", "countryUnCode": "PL", "euPort": true}, "portFacilityVisits": null}, {"id": "d7e76429-9487-4af1-a2b6-005fc09343b2", "arrival": "2024-04-11T10:00:00Z", "departure": "2024-04-12T10:00:00Z", "port": {"name": "Eikefet", "locationUnCode": "NOEKF", "countryUnCode": "NO", "euPort": false}, "portFacilityVisits": null}, {"id": "4a0b33f6-56c8-436a-b0dd-1023fd463308", "arrival": "2024-04-04T10:00:00Z", "departure": "2024-04-09T10:00:00Z", "port": {"name": "Herøy", "locationUnCode": "NOHEY", "countryUnCode": "NO", "euPort": false}, "portFacilityVisits": null}, {"id": "bbd12db0-aace-44c0-92a9-487cb0aad76b", "arrival": "2024-03-28T11:00:00Z", "departure": "2024-03-31T10:00:00Z", "port": {"name": "Glomfjord", "locationUnCode": "NOGLO", "countryUnCode": "NO", "euPort": false}, "portFacilityVisits": null}, {"id": "880a949f-0c0e-439b-a183-369e936eee3d", "arrival": "2024-03-21T11:00:00Z", "departure": "2024-03-22T11:00:00Z", "port": {"name": "Antwerpen", "locationUnCode": "BEANR", "countryUnCode": "BE", "euPort": true}, "portFacilityVisits": null}, {"id": "b6442b30-fe26-40f7-87f6-8612e9690b83", "arrival": null, "departure": null, "port": {"name": "<PERSON><PERSON><PERSON>", "locationUnCode": "NOZFN", "countryUnCode": "NO", "euPort": false}, "portFacilityVisits": null}], "portVisit": {"pilotStation": null, "entryPoint": {"code": "LL", "name": "Lage Licht", "atSea": true}, "etaPort": null, "portEntry": {"origin": "SEA", "baseForPlanning": "PILOT_BOARDING_PLACE", "earliestTimeOfPortEntry": null, "entryDependency": null, "etaPilotBoardingPlace": "2024-05-31T12:00:00Z", "etaSeaBuoy": "2024-05-31T12:00:00Z", "intention": "REQUEST_FOR_ENTRY", "requestedEtaPilotBoardingPlace": null}, "firstMovement": {"vesselDraft": 5, "vesselMasterName": null, "numberOfCrew": null, "numberOfPassengers": null, "cargo": null, "orderEmail": null, "orderSms": null, "pilotExemption": null, "offStandardBeam": null, "pilotService": {"required": true, "serviceProvider": {"name": "Loodswezen", "portAuthorityId": "LRR"}, "remarks": null}, "order": false, "cancellationReason": null, "stormPilotageInformation": {"heliSuitable": null, "hoisting": null, "remotePilotage": null, "imoLoaRegistered": null, "lowFreeboard": null, "remarks": null}}, "passingThroughTugboats": null, "defectTypes": null, "defectTypeRemarks": null, "berthVisits": [], "dangerousGoodsLoading": null, "dangerousGoodsDischarge": null, "dangerousGoodsTransit": null, "vesselInspectionRequired": null, "shipConfiguration": null, "exitPoint": {"code": "LL", "name": "Lage Licht", "atSea": true}, "etdPort": "2024-06-06T10:15:00Z", "atdPort": null, "ataPort": null, "externalInfo": null}, "departureVoyage": {"voyageNumber": null, "carrier": null}, "nextPorts": [{"id": "efedab82-7c19-45df-b9e3-6de74c81678d", "arrival": "2024-06-07T10:00:00Z", "departure": null, "port": {"name": "Antwerpen", "locationUnCode": "BEANR", "countryUnCode": "BE", "euPort": true}, "customsOffice": null}]}, "securityDeclaration": null, "dangerousGoodsDeclaration": null, "wasteDeclaration": null, "wasteCollections": [], "shipStoresDeclaration": null, "paxDeclarationSummaries": [], "healthDeclaration": null, "declarationStatuses": {"VISIT": "ACCEPTED"}, "etaPortAis": null, "ignoreEtaPortAis": false, "cancelled": false, "ataAcknowledged": false, "orderIncomingMovement": true, "importDeclarations": [], "transhipments": []}, "before": {"crn": "NLRTM24012220", "portOfCall": {"port": {"name": "Rotterdam", "locationUnCode": "NLRTM", "countryUnCode": "NL", "euPort": true}, "portAuthority": null, "customsOffice": null, "ataReported": false, "atdReported": false, "atdPortReported": false, "paDeclarationRequired": false, "swDeclarationRequired": false, "wasteEnabled": false, "dangerousGoodsEnabled": false, "harbourDuesEnabled": false, "orderNauticalServicesEnabled": false, "enableNotificationsToPa": false, "authoriseOrganisationsDisabled": false, "tugBoatsRequired": false, "nauticalServicesApplicable": true, "notRequiringTugBoats": true}, "vessel": {"imoCode": "9224166", "name": null, "radioCallSign": null, "motUnCode": null, "motName": null, "summerDeadWeight": null, "maxWidth": null, "flagCode": null, "flagCountryUnCode": null, "netTonnage": null, "grossTonnage": null, "registrationPlaceUnloCode": null, "registrationPlaceName": null, "registrationDate": null, "mmsiNumber": null, "fullLength": null, "emailAddress": null, "statCode5": null}, "owner": {"fullName": "Vertom Agencies B.V.", "shortName": null, "iamConnectedId": null, "portAuthorityId": "VERTOM", "emailAddress": null, "address": null, "city": "R<PERSON>on", "zipCode": null, "countryUnCode": null, "countryName": "Netherlands", "phoneNumber": null, "faxNumber": null, "contact": null, "customsEORINumber": null, "ean": null, "chamberOfCommerceNumber": null, "scacCode": null}, "declarant": {"fullName": "Vertom Agencies B.V.", "shortName": null, "iamConnectedId": null, "portAuthorityId": "VERTOM", "emailAddress": null, "address": null, "city": "R<PERSON>on", "zipCode": null, "countryUnCode": null, "countryName": "Netherlands", "phoneNumber": null, "faxNumber": null, "contact": null, "customsEORINumber": null, "ean": null, "chamberOfCommerceNumber": null, "scacCode": null}, "cargoDeclarants": [], "visitDeclaration": {"clientReferenceNumber": "10241641", "vesselEmailAddress": null, "etaFirstEntryEu": "2024-05-31T12:00:00Z", "arrivalVoyage": {"voyageNumber": null, "carrier": null}, "previousPorts": [{"id": "eeae170e-0341-46fa-80d4-4d27fc03916a", "arrival": "2024-05-26T10:00:00Z", "departure": "2024-05-28T10:00:00Z", "port": {"name": "<PERSON><PERSON><PERSON>", "locationUnCode": "NOZFN", "countryUnCode": "NO", "euPort": false}, "portFacilityVisits": null}, {"id": "33ac19cd-a626-4984-9a40-411791d60993", "arrival": "2024-05-11T10:00:00Z", "departure": "2024-05-24T10:00:00Z", "port": {"name": "Glomfjord", "locationUnCode": "NOGLO", "countryUnCode": "NO", "euPort": false}, "portFacilityVisits": null}, {"id": "d9f1e6cd-3f2f-48bf-bebc-ade4c5bbdca1", "arrival": "2024-05-03T10:00:00Z", "departure": "2024-05-06T10:00:00Z", "port": {"name": "Vlissingen", "locationUnCode": "NLVLI", "countryUnCode": "NL", "euPort": true}, "portFacilityVisits": null}, {"id": "255cbd61-6c2d-4778-b18f-6cd93c314012", "arrival": "2024-04-30T10:00:00Z", "departure": "2024-05-01T10:00:00Z", "port": {"name": "Drogheda", "locationUnCode": "IEDRO", "countryUnCode": "IE", "euPort": true}, "portFacilityVisits": null}, {"id": "6f4ddea7-3d5c-4b12-85d9-e5f794c2fbd3", "arrival": "2024-04-17T10:00:00Z", "departure": "2024-04-24T10:00:00Z", "port": {"name": "Gdynia", "locationUnCode": "PLGDY", "countryUnCode": "PL", "euPort": true}, "portFacilityVisits": null}, {"id": "726363d1-8ab2-45a4-8abc-a861adec7fd0", "arrival": "2024-04-15T10:00:00Z", "departure": "2024-04-16T10:00:00Z", "port": {"name": "Szczecin", "locationUnCode": "PLSZZ", "countryUnCode": "PL", "euPort": true}, "portFacilityVisits": null}, {"id": "d7e76429-9487-4af1-a2b6-005fc09343b2", "arrival": "2024-04-11T10:00:00Z", "departure": "2024-04-12T10:00:00Z", "port": {"name": "Eikefet", "locationUnCode": "NOEKF", "countryUnCode": "NO", "euPort": false}, "portFacilityVisits": null}, {"id": "4a0b33f6-56c8-436a-b0dd-1023fd463308", "arrival": "2024-04-04T10:00:00Z", "departure": "2024-04-09T10:00:00Z", "port": {"name": "Herøy", "locationUnCode": "NOHEY", "countryUnCode": "NO", "euPort": false}, "portFacilityVisits": null}, {"id": "bbd12db0-aace-44c0-92a9-487cb0aad76b", "arrival": "2024-03-28T11:00:00Z", "departure": "2024-03-31T10:00:00Z", "port": {"name": "Glomfjord", "locationUnCode": "NOGLO", "countryUnCode": "NO", "euPort": false}, "portFacilityVisits": null}, {"id": "880a949f-0c0e-439b-a183-369e936eee3d", "arrival": "2024-03-21T11:00:00Z", "departure": "2024-03-22T11:00:00Z", "port": {"name": "Antwerpen", "locationUnCode": "BEANR", "countryUnCode": "BE", "euPort": true}, "portFacilityVisits": null}], "portVisit": {"pilotStation": null, "entryPoint": {"code": "LL", "name": "Lage Licht", "atSea": true}, "etaPort": null, "portEntry": {"origin": "SEA", "baseForPlanning": "PILOT_BOARDING_PLACE", "earliestTimeOfPortEntry": null, "entryDependency": null, "etaPilotBoardingPlace": "2024-05-31T12:00:00Z", "etaSeaBuoy": "2024-05-31T12:00:00Z", "intention": "REQUEST_FOR_ENTRY", "requestedEtaPilotBoardingPlace": null}, "firstMovement": {"vesselDraft": 5, "vesselMasterName": null, "numberOfCrew": null, "numberOfPassengers": null, "cargo": null, "orderEmail": null, "orderSms": null, "pilotExemption": null, "offStandardBeam": null, "pilotService": {"required": true, "serviceProvider": {"name": "Loodswezen", "portAuthorityId": "LRR"}, "remarks": null}, "order": false, "cancellationReason": null, "stormPilotageInformation": {"heliSuitable": null, "hoisting": null, "remotePilotage": null, "imoLoaRegistered": null, "lowFreeboard": null, "remarks": null}}, "passingThroughTugboats": null, "defectTypes": null, "defectTypeRemarks": null, "berthVisits": [{"id": "f3c6c577-a0d3-444d-9e68-eba840a13551", "callId": null, "berth": {"name": "MERWEH BSR VAN UDEN 1", "shortName": null, "code": "G386", "terminalCode": "4908", "terminalName": "MERWEH V UDEN", "organisationShortName": "VANUDENSTEVE", "organisationName": null, "berthGroupCode": null, "buoy": null, "partyToNotify": null, "bollards": null}, "terminal": null, "stevedore": null, "eta": "2024-05-31T15:00:00Z", "requestedEta": null, "ata": null, "etd": null, "requestedEtd": null, "atd": null, "mooring": "NO_PREFERENCE", "remarks": null, "visitPurposes": ["DISCHARGE"], "bollardFrom": null, "bollardTo": null, "tugboatAtArrival": {"required": false, "serviceProvider": {"name": "<PERSON><PERSON><PERSON>", "portAuthorityId": "BOLUDA"}, "requiredQuantity": null, "remarks": null}, "boatmenAtArrival": {"required": true, "serviceProvider": {"name": "KRVE", "portAuthorityId": "RVE"}, "remarks": null}, "boatmenAtDeparture": {"required": true, "serviceProvider": {"name": "KRVE", "portAuthorityId": "RVE"}, "remarks": null}, "tugboatAtDeparture": {"required": false, "serviceProvider": {"name": "<PERSON><PERSON><PERSON>", "portAuthorityId": "BOLUDA"}, "requiredQuantity": null, "remarks": null}, "nextMovement": {"vesselDraft": 5, "vesselMasterName": null, "numberOfCrew": null, "numberOfPassengers": null, "cargo": null, "orderEmail": null, "orderSms": null, "pilotExemption": null, "offStandardBeam": null, "pilotService": {"required": true, "serviceProvider": {"name": "Loodswezen", "portAuthorityId": "LRR"}, "remarks": null}, "order": null, "cancellationReason": null, "stormPilotageInformation": {"heliSuitable": null, "hoisting": null, "remotePilotage": null, "imoLoaRegistered": null, "lowFreeboard": null, "remarks": null}}, "vesselServiceCode": null, "vesselServiceName": null, "discharge": null, "load": null, "restow": null, "status": null}], "dangerousGoodsLoading": null, "dangerousGoodsDischarge": null, "dangerousGoodsTransit": null, "vesselInspectionRequired": null, "shipConfiguration": null, "exitPoint": {"code": "LL", "name": "Lage Licht", "atSea": true}, "etdPort": "2024-06-06T10:15:00Z", "atdPort": null, "ataPort": null, "externalInfo": null}, "departureVoyage": {"voyageNumber": null, "carrier": null}, "nextPorts": [{"id": "efedab82-7c19-45df-b9e3-6de74c81678d", "arrival": "2024-06-07T10:00:00Z", "departure": null, "port": {"name": "Antwerpen", "locationUnCode": "BEANR", "countryUnCode": "BE", "euPort": true}, "customsOffice": null}]}, "securityDeclaration": null, "dangerousGoodsDeclaration": null, "wasteDeclaration": null, "wasteCollections": [], "shipStoresDeclaration": null, "paxDeclarationSummaries": [], "healthDeclaration": null, "declarationStatuses": {"VISIT": "ACCEPTED"}, "etaPortAis": null, "ignoreEtaPortAis": false, "cancelled": false, "ataAcknowledged": false, "orderIncomingMovement": true, "importDeclarations": [], "transhipments": []}, "index": 112523158276407300, "timestamp": "2024-05-29T07:18:56.741Z", "triggeredByRecipient": false, "updateType": "VISIT", "trackingType": "visit"}]}