package nl.teqplay.portcallplus

import com.mongodb.kotlin.client.FindIterable
import com.mongodb.kotlin.client.MongoCollection
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.portcallplus.config.ObjectMapperConfiguration
import org.bson.conversions.Bson
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock

private fun <T : Any> getMockIterable() = mock<FindIterable<T>>()

fun getMockMongoDB(): MongoDatabase =
    mock {
        val mockCollection = mock<MongoCollection<Any>> {
            val iterable = getMockIterable<Any>()
            on { createIndex(any<Bson>(), any()) } doReturn ""
            on { find() } doReturn iterable
            on { find(any<Bson>()) } doReturn iterable
        }
        on { getCollection(any(), any<Class<Any>>()) } doReturn mockCollection
    }

val objectMapper = ObjectMapperConfiguration().objectMapper()
