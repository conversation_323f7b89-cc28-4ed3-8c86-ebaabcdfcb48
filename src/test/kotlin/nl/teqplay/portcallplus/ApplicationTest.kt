package nl.teqplay.portcallplus

import nl.teqplay.skeleton.common.BaseTest
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Import
import org.springframework.context.annotation.Primary

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class ApplicationTest : BaseTest() {
    @Configuration
    @Import(Application::class)
    class Config {
        @Bean(name = ["mockMongoDatabase"])
        @Primary
        fun mongoDatabase() = getMockMongoDB()
    }
}
