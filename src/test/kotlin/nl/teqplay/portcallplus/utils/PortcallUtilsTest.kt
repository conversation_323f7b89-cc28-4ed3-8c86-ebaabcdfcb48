package nl.teqplay.portcallplus.utils

import nl.teqplay.platform.model.PortcallVisit
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallAlias
import nl.teqplay.portcallplus.api.model.PortcallAliasName
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.core.Is.`is`
import org.junit.jupiter.api.Test
import java.util.Date
import java.util.concurrent.TimeUnit
import nl.teqplay.platform.model.Portcall as PlatformPortcall
import nl.teqplay.platform.model.Portcall.PortcallStatus as PlatformPortcallStatus

class PortcallUtilsTest {
    @Test
    fun generatePortcallIdTest() {
        val date = Date(1574174583000)
        val id = generatePortcallId(Portcall.IDPREFIX_TNZ, "123", date)
        assertThat(id, `is`("NLTNZ1231911190T"))

        val id2 = generatePortcallId(Portcall.IDPREFIX_TNZ, "123", date, 1)
        assertThat(id2, `is`("NLTNZ1231911191T"))
    }

    @Test
    fun convertPlatformPortcallTest() {
        val startTime = System.currentTimeMillis()
        val platformPortcall = getPlatformPortcall(startTime)
        // test method
        val portcall = convertPlatformPortcall(platformPortcall, ScheduledTaskType.UNKNOWN)
        assertThat(portcall.portcallId, `is`(platformPortcall.portcallId))
        assertThat(portcall.port, `is`(platformPortcall.port))
        assertThat(portcall.source, `is`(ScheduledTaskType.UNKNOWN))
        assertThat(portcall.imo, `is`(platformPortcall.imo))
        assertThat(portcall.visits, `is`(emptyList()))
        assertThat(portcall.visits, `is`(emptyList()))
        assertThat(portcall.startTime, `is`(Date(startTime)))
    }

    @Test
    fun `convertPlatformPortcall with visits test`() {
        val startTime = System.currentTimeMillis()
        val portcallVisit = PortcallVisit(
            "terminalName",
            "berthName",
            "berthOwner",
            startTime,
            null,
            null,
        )
        val platformPortcall = getPlatformPortcall(
            startTime,
            status = PlatformPortcallStatus.INBOUND,
            vesselAgent = "Agency",
            visits = listOf(portcallVisit),
        )
        platformPortcall.originUnlocode = Portcall.IDPREFIX_NLAMS
        platformPortcall.destinationUnlocode = Portcall.IDPREFIX_NLRTM

        val portcall = convertPlatformPortcall(platformPortcall, ScheduledTaskType.ENIGMA_SCRAPER_OUTGOING)
        assertThat(portcall.vesselAgent, `is`(portcall.vesselAgent))
        assertThat(portcall.originUnlocode, `is`(portcall.originUnlocode))
        assertThat(portcall.destinationUnlocode, `is`(portcall.destinationUnlocode))
        assertThat(portcall.source, `is`(ScheduledTaskType.ENIGMA_SCRAPER_OUTGOING))

        assertThat(portcall.visits.size, `is`(1))
        val firstPortcallVisit = portcall.visits.first()
        assertThat(firstPortcallVisit.terminal, `is`(portcallVisit.terminal))
        assertThat(firstPortcallVisit.berthName, `is`(portcallVisit.berthName))
        assertThat(firstPortcallVisit.berthEta?.time, `is`(portcallVisit.startTime))
    }

    @Test
    fun `convertPlatformPortcall with endTime test`() {
        val startTime = System.currentTimeMillis()
        val portAta = startTime + TimeUnit.HOURS.toMillis(5)
        val portAtd = startTime + TimeUnit.HOURS.toMillis(20)
        val endTime = startTime + TimeUnit.DAYS.toMillis(1)
        val platformPortcall = getPlatformPortcall(startTime, endTime, portAta, portAtd, status = PlatformPortcallStatus.OUTBOUND)

        val portcall = convertPlatformPortcall(platformPortcall, ScheduledTaskType.UNKNOWN)
        assertThat(portcall.status.name, `is`(platformPortcall.status.name))
        assertThat(portcall.portAtaTime?.time, `is`(portAta))
        assertThat(portcall.portAtdTime?.time, `is`(portAtd))
        assertThat(portcall.endTime?.time, `is`(endTime))
    }

    /**
     * Needed to support an alias for different terminals of the same company
     */
    @Test
    fun `multiple aliasses of the same source`() {
        val source = PortcallAliasName.VOPAK_NOMINATION
        val id1 = "1234"
        val id2 = "5678"
        val portcall = Portcall(id1, port = "NLRTM", imo = "78965", startTime = Date())
        assertThat(portcall.portcallAlias.size, `is`(0))
        val portcall1 = portcall.copy(portcallAlias = portcall.portcallAlias + PortcallAlias(source, id1))
        assertThat(portcall1.portcallAlias.size, `is`(1))
        val portcall2 = portcall1.copy(portcallAlias = portcall1.portcallAlias + PortcallAlias(source, id2))
        assertThat(portcall2.portcallAlias.size, `is`(2))
        assertThat(portcall2.portcallAlias.first().alias, `is`(id1))
        assertThat(portcall2.portcallAlias.last().alias, `is`(id2))
    }

    /**
     * Make sure in the aliasses there are no exact duplicates
     */
    @Test
    fun `no duplicate aliasses`() {
        val source = PortcallAliasName.VOPAK_NOMINATION
        val id1 = "1234"
        val portcall = Portcall(id1, port = "NLRTM", imo = "78965", startTime = Date())
        val portcall1 = portcall.copy(portcallAlias = portcall.portcallAlias + PortcallAlias(source, id1))
        val portcall2 = portcall1.copy(portcallAlias = portcall1.portcallAlias + PortcallAlias(source, id1))
        assertThat(portcall2.portcallAlias.size, `is`(1))
    }

    /**
     * Using the setPortcallAlias function, do not allow multple aliasses per terminal but only one per source
     */
    @Test
    fun `no multiple aliasses of the same source using setPortcallAlias`() {
        val source = PortcallAliasName.VOPAK_NOMINATION
        val id1 = "1234"
        val id2 = "5678"
        val portcall = Portcall(id1, port = "NLRTM", imo = "78965", startTime = Date())
        assertThat(portcall.portcallAlias.size, `is`(0))
        val portcall1 = portcall.copy(portcallAlias = setPortcallAlias(portcall, source, id1))
        assertThat(portcall1.portcallAlias.size, `is`(1))
        val portcall2 = portcall1.copy(portcallAlias = setPortcallAlias(portcall1, source, id2))
        assertThat(portcall2.portcallAlias.size, `is`(1))
        assertThat(portcall2.portcallAlias.first().alias, `is`(id1))
    }

    private fun getPlatformPortcall(
        startTime: Long,
        endTime: Long? = null,
        portAta: Long? = null,
        portAtd: Long? = null,
        vesselAgent: String = "test agency",
        visits: List<PortcallVisit>? = null,
        status: PlatformPortcallStatus = PlatformPortcallStatus.INBOUND,
    ): PlatformPortcall {
        return PlatformPortcall(
            "testPortcallId", "testImo", Portcall.IDPREFIX_NLRTM,
            visits, startTime, endTime, portAta, portAtd, null, null, null,
            null, status, vesselAgent, null, null, null,
        )
    }
}
