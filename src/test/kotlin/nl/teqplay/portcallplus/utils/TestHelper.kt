package nl.teqplay.portcallplus.utils

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.JsonNode
import com.google.common.io.Resources.getResource
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallVisit
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.objectMapper
import nl.teqplay.portcallplus.service.external.LIS_DATE_FORMAT
import org.mockito.Mockito
import java.io.File
import java.util.Date

object TestHelper {
    fun getTestPortcall(
        port: String = Portcall.IDPREFIX_ANR,
        portcallId: String = "testPortcallId",
        imo: String = "001",
        source: ScheduledTaskType = ScheduledTaskType.UNKNOWN,
        startTime: Date = fetchTimestamp("01/01/2019 10:00", LIS_DATE_FORMAT),
        visits: List<PortcallVisit> = listOf(),
        vesselAgent: String? = null,
    ) = Portcall(
        portcallId = portcallId,
        portcallAlias = emptySet(),
        port = port,
        imo = imo,
        source = source,
        startTime = startTime,
        vesselAgent = vesselAgent,
        visits = visits,
    )

    fun <T> any() = Mockito.any() as T

    fun <T : Any> readFromFile(
        filename: String,
        key: String,
        typeRef: TypeReference<T>,
    ): T {
        val resource = objectMapper.readValue(
            this::class.java.classLoader.getResourceAsStream(filename),
            JsonNode::class.java,
        ).get(key)
        return objectMapper.convertValue(resource, typeRef)
    }

    fun <T : Any> readFromFile(
        filename: String,
        typeRef: TypeReference<T>,
    ): T = objectMapper.readValue<T>(this::class.java.classLoader.getResourceAsStream(filename), typeRef)

    fun getResourceAsByteArray(filename: String): ByteArray {
        val jsonString: String = File(getResource(filename).file).readText(Charsets.UTF_8)
        return jsonString.toByteArray(Charsets.UTF_8)
    }
}
