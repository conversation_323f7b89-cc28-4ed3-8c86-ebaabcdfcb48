package nl.teqplay.portcallplus.utils

import nl.teqplay.portcallplus.model.data.mturk.MTurkHITDetails
import nl.teqplay.portcallplus.model.data.mturk.MTurkHITResponse
import nl.teqplay.portcallplus.model.service.MTurkHIT
import software.amazon.awssdk.services.mturk.model.AssignmentStatus
import software.amazon.awssdk.services.mturk.model.HITReviewStatus
import software.amazon.awssdk.services.mturk.model.HITStatus
import java.util.Date
import java.util.UUID

object MTurkUtilsTest {
    fun getMTurkHITDetails(
        imo: String = "1234567",
        portcallId: String = "someAlias",
        hitId: String = UUID.randomUUID().toString(),
        hitTypeId: String = UUID.randomUUID().toString(),
        hitReviewStatus: HITReviewStatus = HITReviewStatus.UNKNOWN_TO_SDK_VERSION,
        hitStatus: HITStatus = HITStatus.UNKNOWN_TO_SDK_VERSION,
        expired: Boolean = false,
        disappearedInAmazon: Boolean = false,
        assignments: List<MTurkHITResponse> = emptyList(),
        overallAssignmentStatus: AssignmentStatus = AssignmentStatus.UNKNOWN_TO_SDK_VERSION,
        approvedAnswer: String? = null,
    ): MTurkHITDetails {
        return MTurkHITDetails(
            request = MTurkHIT(imo, portcallId),
            hitId = hitId,
            hitTypeId = hitTypeId,
            hitReviewStatus = hitReviewStatus,
            hitStatus = hitStatus,
            expired = expired,
            disappearedInAmazon = disappearedInAmazon,
            assignments = assignments,
            overallAssignmentStatus = overallAssignmentStatus,
            approvedAnswer = approvedAnswer,
        )
    }

    fun getMTurkHITResponse(
        assignmentId: String = UUID.randomUUID().toString(),
        workerId: String = UUID.randomUUID().toString(),
        status: AssignmentStatus = AssignmentStatus.SUBMITTED,
        formAgent: String? = null,
        submitTime: Date? = null,
        approvalTime: Date? = null,
        isRejected: Boolean = false,
        feedback: String? = null,
    ): MTurkHITResponse {
        val answer = "<?xml version=\"1.0\" encoding=\"ASCII\"?><QuestionFormAnswers xmlns=\"http://mechanicalturk.amazonaws.com/AWSMechanicalTurkDataSchemas/2005-10-01/QuestionFormAnswers.xsd\"><Answer><QuestionIdentifier>agent</QuestionIdentifier><FreeText>" +
            formAgent +
            "</FreeText></Answer></QuestionFormAnswers>"
        return MTurkHITResponse(
            assignmentId = assignmentId,
            workerId = workerId,
            status = status,
            answer = answer,
            submitTime = submitTime,
            approvalTime = approvalTime,
            isRejected = isRejected,
            feedback = feedback,
        )
    }
}
