package nl.teqplay.portcallplus.utils

import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallVisit
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import java.util.Date
import java.util.concurrent.TimeUnit

class ExportUtilsTest {
    @Test
    fun createExcelWorkbook() {
        val workbook = createPortcallsWorkbook(getTestPortcalls())
        Assertions.assertNotNull(workbook)
        val sheet = workbook.getSheetAt(0)

        // check if the portcall id matches
        Assertions.assertEquals("1", sheet.getRow(1).getCell(0).stringCellValue)
        Assertions.assertEquals("2", sheet.getRow(2).getCell(0).stringCellValue)
        Assertions.assertEquals("3", sheet.getRow(3).getCell(0).stringCellValue)

        // check if the berth matches with 2 berths
        Assertions.assertEquals("AA", sheet.getRow(1).getCell(5).stringCellValue)
        Assertions.assertEquals("BB", sheet.getRow(1).getCell(6).stringCellValue)

        // check if the berth matches with 1 berth
        Assertions.assertEquals("BB", sheet.getRow(2).getCell(5).stringCellValue)
        Assertions.assertEquals("AA", sheet.getRow(3).getCell(5).stringCellValue)
        Assertions.assertEquals("AA", sheet.getRow(4).getCell(5).stringCellValue)
    }

    private fun getTestPortcalls(): List<Portcall> {
        return listOf<Portcall>(
            createPortcallWithVisit(Portcall.IDPREFIX_GNE, "1", "1234", Date(), "AA", "BB"),
            createPortcallWithVisit(Portcall.IDPREFIX_GNE, "2", "12345", Date(), "BB"),
            createPortcallWithVisit(Portcall.IDPREFIX_GNE, "3", "123456", Date(), "AA"),
            createPortcallWithVisit(Portcall.IDPREFIX_TNZ, "4", "12345", Date(), "AA"),
            createPortcallWithVisit(Portcall.IDPREFIX_GNE, "5", "1234", Date(), "AA"),
            createPortcallWithVisit(Portcall.IDPREFIX_TNZ, "6", "123456", Date(), "AA"),
            createPortcallWithVisit(Portcall.IDPREFIX_GNE, "7", "1234567", Date(), "AA"),
            createPortcallWithVisit(Portcall.IDPREFIX_GNE, "8", "1234", Date(), "AA"),
        )
    }

    private fun createPortcallWithVisit(
        port: String,
        portcallId: String,
        imo: String,
        startTime: Date,
        berth: String,
        berth2: String? = null,
    ): Portcall {
        val visits = mutableListOf(PortcallVisit(berthEta = startTime, berthName = berth))
        if (berth2 != null) {
            visits.add(PortcallVisit(berthEta = fetchTimestamp(startTime, 1, TimeUnit.HOURS), berthName = berth2))
        }
        return Portcall(
            portcallId = portcallId,
            portcallAlias = emptySet(),
            port = port,
            imo = imo,
            source = ScheduledTaskType.UNKNOWN,
            startTime = startTime,
            visits = visits,
        )
    }
}
