package nl.teqplay.portcallplus.utils

import nl.teqplay.portcallplus.service.common.PortOfAntwerpBrugesResponse
import nl.teqplay.portcallplus.service.common.PortOfAntwerpBrugesResponseStay

object PortOfAntwerpBrugesUtilsTest {
    fun getTestResponse(
        vesselName: String? = null,
        imoNumber: String? = null,
        flag: String? = null,
        countryNl: String? = null,
        countryEn: String? = null,
        vesselTypeFull: String? = null,
        vesselType: String? = null,
        loa: String? = null,
        gbr: String? = null,
        stayDetail: List<PortOfAntwerpBrugesResponseStay>? = emptyList(),
    ) = PortOfAntwerpBrugesResponse(
        vesselName = vesselName,
        imoNumber = imoNumber,
        flag = flag,
        countryNl = countryNl,
        countryEn = countryEn,
        vesselTypeFull = vesselTypeFull,
        vesselType = vesselType,
        loa = loa,
        gbr = gbr,
        stayDetail = stayDetail,
    )

    fun getTestResponseStay(
        stayId: String? = null,
        agentName: String? = null,
        agentCode: String? = null,
        berthArrival: String? = null,
        eta: Long? = null,
        ata: Long? = null,
        berthDeparture: String? = null,
        etd: Long? = null,
        atd: Long? = null,
        origin: String? = null,
        destination: String? = null,
        originFull: String? = null,
        destinationFull: String? = null,
    ) = PortOfAntwerpBrugesResponseStay(
        stayId = stayId,
        agentName = agentName,
        agentCode = agentCode,
        berthArrival = berthArrival,
        eta = eta,
        ata = ata,
        berthDeparture = berthDeparture,
        etd = etd,
        atd = atd,
        origin = origin,
        destination = destination,
        originFull = originFull,
        destinationFull = destinationFull,
    )
}
