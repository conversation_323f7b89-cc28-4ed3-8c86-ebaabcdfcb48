package nl.teqplay.portcallplus.utils

import nl.teqplay.poma.api.v1.AvailabilityType
import nl.teqplay.poma.api.v1.Berth
import nl.teqplay.poma.api.v1.CargoCategoryType
import nl.teqplay.poma.api.v1.CargoType
import nl.teqplay.poma.api.v1.Country
import nl.teqplay.poma.api.v1.FunctionType
import nl.teqplay.poma.api.v1.Location
import nl.teqplay.poma.api.v1.MooringType
import nl.teqplay.poma.api.v1.Port
import nl.teqplay.poma.api.v1.VesselTypeAllowed

fun createTestBerth(
    uniqueId: String? = null,
    authorityId: String? = null,
    name: String = "",
    nameLong: String? = null,
    terminalName: String? = null,
    terminalId: String? = null,
    harbourName: String? = null,
    harbourId: String? = null,
    ports: Set<String> = emptySet(),
    length: Double? = null,
    width: Double? = null,
    draught: Double? = null,
    owner: String? = null,
    quayId: String? = null,
    dangerousGoodsLevel: Int? = null,
    vesselTypeAllowed: Set<VesselTypeAllowed>? = null,
    availabilityType: AvailabilityType? = null,
    mooringType: MooringType? = null,
    cargoCategoryType: Set<CargoCategoryType>? = null,
    cargoType: Set<CargoType>? = null,
    functionType: Set<FunctionType>? = null,
    bollardIds: List<String>? = null,
    location: Location = Location(1.0, 1.0),
    area: List<Location> = emptyList(),
    areaSizeInM2: Long? = null,
    manualOverriddenArea: Boolean = false,
    modelType: String = "",
    source: String? = null,
    sourceType: String? = null,
    _id: String? = null,
    validatedByUser: Boolean = true,
) =
    Berth(uniqueId, authorityId, name, name, nameLong, terminalName, terminalId, harbourName, harbourId, ports.toList(), length, width, draught, owner, quayId, dangerousGoodsLevel, vesselTypeAllowed, availabilityType, mooringType, cargoCategoryType, cargoType, functionType, bollardIds, location, area, areaSizeInM2, manualOverriddenArea, modelType, source, sourceType, _id, true, null)

fun createTestPort(
    name: String = "Test Port",
    displayName: String = "Test Port",
    unlocode: String? = null,
    country: Country? = null,
    location: Location = Location(1.0, 1.0),
    area: List<Location> = emptyList(),
    areaSizeInM2: Long? = null,
    manualOverriddenArea: Boolean = false,
    outerArea: List<Location> = emptyList(),
    nm12Area: List<Location> = emptyList(),
    nm60Area: List<Location> = emptyList(),
    nm80Area: List<Location> = emptyList(),
    nm120Area: List<Location> = emptyList(),
    eosArea: List<Location>? = null,
    alternativeNames: List<String> = emptyList(),
    destinations: List<String> = emptyList(),
    mainPort: String? = null,
    margin: Double = 0.0,
    uniqueId: String? = null,
    modelType: String = "TEST model type",
    source: String? = null,
    sourceType: String? = null,
    id: String? = null,
    validatedByUser: Boolean = false,
) = Port(
    name = name,
    displayName = displayName,
    unlocode = unlocode,
    country = country,
    location = location,
    area = area,
    areaSizeInM2 = areaSizeInM2,
    manualOverriddenArea = manualOverriddenArea,
    outerArea = outerArea,
    nm12Area = nm12Area,
    nm60Area = nm60Area,
    nm80Area = nm80Area,
    nm120Area = nm120Area,
    eosArea = eosArea,
    alternativeNames = alternativeNames,
    destinations = destinations,
    mainPort = mainPort,
    margin = margin,
    uniqueId = uniqueId,
    modelType = modelType,
    source = source,
    sourceType = sourceType,
    _id = id,
    validatedByUser = validatedByUser,
    countryCode = null,
)
