package nl.teqplay.portcallplus.utils

import nl.teqplay.poma.api.v1.Berth
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallVisit
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.ServiceModel
import nl.teqplay.portcallplus.service.common.SourceService
import nl.teqplay.portcallplus.service.internal.PoMaService
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.util.Date
import java.util.concurrent.TimeUnit
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class PortcallVisitUtilsTest {
    private val pomaServiceMock = mock<PoMaService>()

    private val testService = object : SourceService(mock(), pomaServiceMock, mock()) {
        override val allowOtherSourcePortcallUpdate: Boolean = true

        override fun getServiceModels(taskType: ScheduledTaskType): Collection<Pair<String, ServiceModel>> {
            return emptyList()
        }

        override fun convertToPortcall(
            serviceModel: ServiceModel,
            imoNumber: String,
        ): Portcall {
            throw NotImplementedError()
        }
    }

    @BeforeEach
    fun before() {
        whenever(pomaServiceMock.getBerths(anyOrNull(), anyOrNull())).thenReturn(emptyArray<Berth>())
        whenever(pomaServiceMock.getBerths(eq("BERTH1"), anyOrNull()))
            .thenReturn(arrayOf(createTestBerth(_id = "BERTH1", uniqueId = "NOT_BERTH1")))
        whenever(pomaServiceMock.getBerths(eq("BERTH2"), anyOrNull()))
            .thenReturn(arrayOf(createTestBerth(_id = "BERTH2", uniqueId = "NOT_BERTH2")))
    }

    @Test
    fun portcallVisitsAddEtaTest() {
        val eta1 = Date(1570630732755)
        val eta2 = Date(1570630734755)

        with(testService) {
            val portcall = Portcall(
                portcallId = "NLTNV",
                portcallAlias = emptySet(),
                port = "1",
                imo = "1234",
                source = ScheduledTaskType.UNKNOWN,
                startTime = eta1,
            ).getUpdatedPortcall(
                newVisits = listOf(
                    PortcallVisit(berthEta = eta1, berthName = "BERTH1"),
                    PortcallVisit(berthEta = eta2, berthName = "BERTH2"),
                ),
            )
            assertEquals(2, portcall.visits.size)
            assertEquals(eta1.time, portcall.startTime.time)
        }
    }

    @Test
    fun twoPortcallVisitsAddEtaTest() {
        val eta1 = Date(1570630732755)
        val etd1 = Date(1570630733755)
        val eta2 = Date(1570630734755)
        val visit1 = PortcallVisit(eta1, null, null, etd1, null, "BERTH1")

        with(testService) {
            val portcall = Portcall(
                portcallId = "NLTNV",
                portcallAlias = emptySet(),
                port = "1",
                imo = "1234",
                source = ScheduledTaskType.UNKNOWN,
                startTime = eta1,
                visits = listOf(visit1),
            ).getUpdatedPortcall(newVisits = listOf(PortcallVisit(berthEta = eta2, berthName = "BERTH2")))
            assertEquals(2, portcall.visits.size)
            assertEquals(eta1.time, portcall.startTime.time)
        }
    }

    @Test
    fun updatePortcallVisitsUpdateEtaTest() {
        val eta1 = Date(1570630732755)
        val etd1 = Date(1570630733755)
        val eta2 = Date(1570630733855)

        with(testService) {
            val portcall = Portcall(
                portcallId = "NLTNV",
                portcallAlias = emptySet(),
                port = "1",
                imo = "1234",
                source = ScheduledTaskType.UNKNOWN,
                startTime = eta1,
                visits = listOf(PortcallVisit(eta1, null, null, etd1, null, "BERTH1")),
            ).getUpdatedPortcall(
                newVisits = listOf(PortcallVisit(berthEta = eta2, berthName = "BERTH1")),
            )
            assertEquals(1, portcall.visits.size)
            assertEquals(eta2, portcall.startTime)
        }
    }

    @Test
    fun updatePortcallVisitsEtaMultipleTest() {
        val eta1 = Date()
        val etd1 = fetchTimestamp(eta1, addTime = 1, unit = TimeUnit.DAYS)
        val eta2 = fetchTimestamp(eta1, addTime = 2, unit = TimeUnit.DAYS)
        val eta3 = fetchTimestamp(eta1, addTime = 3, unit = TimeUnit.DAYS)
        val eta1Update = fetchTimestamp(eta1, addTime = 1, unit = TimeUnit.HOURS)
        val eta3Update = fetchTimestamp(eta3, addTime = 2, unit = TimeUnit.HOURS)

        with(testService) {
            val portcall = Portcall(
                portcallId = "NLTNV",
                portcallAlias = emptySet(),
                port = "1",
                imo = "1234",
                source = ScheduledTaskType.UNKNOWN,
                startTime = eta1,
                visits = listOf(PortcallVisit(berthEta = eta1, berthEtd = etd1, berthName = "BERTH1")),
            ).getUpdatedPortcall(
                newVisits = listOf(
                    PortcallVisit(berthName = "BERTH2", berthEta = eta2),
                    PortcallVisit(berthName = "BERTH3", berthEta = eta3),
                    PortcallVisit(berthName = "BERTH1", berthEta = eta1Update),
                    PortcallVisit(berthName = "BERTH3", berthEta = eta3Update),
                ),
            )

            assertEquals(3, portcall.visits.size)
            assertEquals(eta1Update, portcall.startTime)
            assertEquals(eta3Update, portcall.visits[2].berthEta)
        }
    }

    @Test
    fun `check visits when portcall is copied`() {
        val eta = Date()
        val etd = fetchTimestamp(timestamp = eta, addTime = 1, unit = TimeUnit.DAYS)
        val portcall = Portcall(
            portcallId = "NLTNV",
            portcallAlias = emptySet(),
            port = "1",
            imo = "1234",
            source = ScheduledTaskType.UNKNOWN,
            startTime = eta,
            visits = listOf(PortcallVisit(berthEta = eta, berthEtd = etd, berthName = "BERTH1")),
        )

        // make a copy of the portcall
        val copiedPortcall = portcall.copy(source = ScheduledTaskType.ENIGMA_SCRAPER_OUTGOING)
        assertEquals(portcall.visits, copiedPortcall.visits)
    }

    @Test
    fun updatePortcallVisitsEtaAndEtdMultipleTest() {
        val eta1 = Date(1570630732755)
        val etd1 = Date(1570630733755)
        val eta2 = Date(1570630734755)
        val etd2 = Date(1570630735755)
        val eta3 = Date(1570630738755)
        val eta1Update = Date(1570630732855)
        val eta3Update = Date(1570630739755)
        val etd2Update = Date(1570630737755)
        val etdWithoutEta = Date(1570631739755)

        with(testService) {
            val portcall = Portcall(
                portcallId = "NLTNV",
                portcallAlias = emptySet(),
                port = "1",
                imo = "1234",
                source = ScheduledTaskType.UNKNOWN,
                startTime = eta1,
                visits =
                listOf(PortcallVisit(berthEta = eta1, berthEtd = etd1, berthName = "BERTH1")),
            ).getUpdatedPortcall(
                newVisits = listOf(
                    PortcallVisit(berthName = "BERTH2", berthEta = eta2),
                    PortcallVisit(berthName = "BERTH3", berthEta = eta3),
                    PortcallVisit(berthName = "BERTH1", berthEta = eta1Update),
                    PortcallVisit(berthName = "BERTH3", berthEta = eta3Update),
                    PortcallVisit(berthName = "BERTH2", berthEtd = etd2),
                    PortcallVisit(berthName = "BERTH2", berthEtd = etd2Update),
                    PortcallVisit(berthName = "BERTH4", berthEtd = etdWithoutEta),
                ),
            )

            assertEquals(4, portcall.visits.size)
            assertEquals(eta1Update, portcall.startTime)
            assertEquals(eta3Update, portcall.visits[2].berthEta)
            assertEquals(etd2Update, portcall.visits[1].berthEtd)
        }
    }

    @Test
    fun testPortcallEquals() {
        // no changes in the values
        var portcall1 = TestHelper.getTestPortcall(visits = listOf(PortcallVisit()))
        var portcall2 = TestHelper.getTestPortcall(visits = listOf(PortcallVisit(terminal = null)))
        assertTrue(portcall1 == portcall2)

        // changes in additional visits must be detected
        portcall1 = TestHelper.getTestPortcall(visits = listOf(PortcallVisit()))
        portcall2 = TestHelper.getTestPortcall()
        assertTrue(portcall1 != portcall2)

        // changes in additional visits and times must be detected
        val time = Date()
        portcall1 = TestHelper.getTestPortcall(visits = listOf(PortcallVisit(terminal = "test"))).copy(portAtdTime = time)
        portcall2 = TestHelper.getTestPortcall(visits = listOf(PortcallVisit(terminal = "test"))).copy(portAtdTime = time)
        assertTrue(portcall1 == portcall2)

        // update a member of the visit
        portcall2.visits.first().berthAta = Date()
        assertTrue(portcall1 != portcall2)

        // changes in the visits is detected
        assertTrue(
            portcall1 != portcall2.copy(portAtdTime = null, visits = listOf(PortcallVisit(terminal = "test"))),
        )
    }

    private fun vopakLocationTestData() =
        Stream.of(
            Arguments.of("JETTY 1", "VTS SAKRA", "VOPAK SAKRA TERMINAL OSK 1"),
            Arguments.of("JETTY 1A", "VTS SAKRA", "VOPAK SAKRA TERMINAL OSK 1A"),
            Arguments.of("JETTY 2", "VTS SAKRA", "VOPAK SAKRA TERMINAL OSK 2"),
            Arguments.of("JETTY 1", "VTS BANYAN", "VOPAK BANYAN OBV 1"),
            Arguments.of("JETTY 2", "VTS BANYAN", "VOPAK BANYAN OBV 2"),
            Arguments.of("JETTY 3", "VTS BANYAN", "VOPAK BANYAN OBV 3"),
            Arguments.of("JETTY 4", "VTS BANYAN", "VOPAK BANYAN OBV 4"),
            Arguments.of("JETTY 5", "VTS BANYAN", "VOPAK BANYAN OBV 5"),
            Arguments.of("JETTY 6", "VTS BANYAN", "VOPAK BANYAN OBV 6"),
            Arguments.of("JETTY 7", "VTS BANYAN", "VOPAK BANYAN OBV 7"),
            Arguments.of("JETTY 2", "VTS SEBAROK", "SHELL BUKOM OSV 4"),
            Arguments.of("JETTY 3", "VTS SEBAROK", "SHELL BUKOM OSV 3"),
            Arguments.of("JETTY 4", "VTS SEBAROK", "SHELL BUKOM OSV 2"),
            Arguments.of("JETTY 5", "VTS SEBAROK", "SHELL BUKOM OSV 13"),
            Arguments.of("JETTY 6", "VTS SEBAROK", "SHELL BUKOM OSV 9"),
            Arguments.of("JETTY 7", "VTS SEBAROK", "SHELL BUKOM OSV 8"),
            Arguments.of("JETTY 8", "VTS SEBAROK", "SHELL BUKOM OSV 12"),
            Arguments.of("JETTY 9", "VTS SEBAROK", "SHELL BUKOM OSV 11"),
            Arguments.of("JETTY 10", "VTS SEBAROK", "SHELL BUKOM OSV 10"),
            Arguments.of("JETTY 1", "VTS PENJURU", "VOPAK PENJURU OVPJ 1"),
            Arguments.of("JETTY 2", "VTS PENJURU", "VOPAK PENJURU OVPJ 2"),
            Arguments.of("JETTY 3", "VTS PENJURU", "VOPAK PENJURU OVPJ 3"),
            Arguments.of("1SD", "DEER PARK", "VOPAK1-95.0964799331069_29.74454115076873"),
            Arguments.of("2SD", "DEER PARK", "VOPAK2-95.09727789971701_29.74599303113119"),
            Arguments.of("3SD", "DEER PARK", "VOPAK3-95.09790965122484_29.7477925526672"),
            Arguments.of("4SD", "DEER PARK", "VOPAK3-95.09790965122484_29.7477925526672"),
            Arguments.of("5SD", "DEER PARK", "VOPAK5-95.09621543551528_29.74903779207846"),
            Arguments.of("5sd", "DEER PARK", "VOPAK5-95.09621543551528_29.74903779207846"),
            Arguments.of("5SD", "Deer Park", "VOPAK5-95.09621543551528_29.74903779207846"),
        )

    @ParameterizedTest
    @MethodSource("vopakLocationTestData")
    fun `should resolve vopak location on portcall update`(
        berthName: String,
        terminalName: String,
        expectedUniqueId: String,
    ) {
        val visitEta = Date(1570630732755)

        val portcall = TestHelper.getTestPortcall()
        val newVisit = PortcallVisit(
            berthName = berthName,
            terminal = terminalName,
            berthEta = visitEta,
        )

        with(testService) {
            val updatedPortcall = portcall.getUpdatedPortcall(newVisits = listOf(newVisit))
            val updatedVisits = updatedPortcall.visits

            assertEquals(1, updatedVisits.size)
            val updatedVisit = updatedVisits.first()
            assertEquals(expectedUniqueId, updatedVisit.uniqueBerthId)
        }
    }
}
