package nl.teqplay.portcallplus.model.data

import nl.teqplay.portcallplus.api.model.ScheduledTaskType.ENIGMA_SCRAPER_INCOMING
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.ENIGMA_SCRAPER_OUTGOING
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.SG_MDH_DUE_TO_ARRIVE
import nl.teqplay.portcallplus.utils.fetchTimestamp
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.`is`
import org.junit.jupiter.api.Test
import java.util.Date
import java.util.concurrent.TimeUnit

class ActivityTest {
    /**
     * Test to ensure that the sorting of [Activity] into [Activity.ActivityData]
     */
    @Test
    fun testActivitySort() {
        val yesterday = fetchTimestamp(Date(), -1, TimeUnit.DAYS)
        val today = Date()
        val tomorrow = fetchTimestamp(Date(), 1, TimeUnit.DAYS)

        // list of activities for 3 tasks, across 3 days. yesterday, today and tomorrow
        val activities = listOf(
            Activity(hashSetOf("1", "2"), ENIGMA_SCRAPER_INCOMING, yesterday),
            Activity(hashSetOf(), ENIGMA_SCRAPER_INCOMING, yesterday),
            Activity(hashSetOf("2", "3"), ENIGMA_SCRAPER_OUTGOING, yesterday),
            Activity(hashSetOf("3"), ENIGMA_SCRAPER_INCOMING, today),
            Activity(hashSetOf("2"), SG_MDH_DUE_TO_ARRIVE, tomorrow),
            Activity(hashSetOf("2", "3"), ENIGMA_SCRAPER_OUTGOING, tomorrow),
        )
        val activityData = Activity.ActivityData.getActivityData(activities).toList()

        // 3 tasks, spread across 3 days must result in 3x3 tasks in total (one task/day)
        assertThat(activityData.size, `is`(9))

        val activitiesForYesterday = activityData.filter { it.timestamp == fetchTimestamp(yesterday, "yyyy-MM-dd") }
        assertThat(activitiesForYesterday.size, `is`(3))
        assertThat(activitiesForYesterday[0].taskName, `is`(ENIGMA_SCRAPER_INCOMING))
        assertThat(activitiesForYesterday[0].getCount(), `is`(2))
        assertThat(activitiesForYesterday[1].taskName, `is`(ENIGMA_SCRAPER_OUTGOING))
        assertThat(activitiesForYesterday[1].getCount(), `is`(2))
        assertThat(activitiesForYesterday[2].taskName, `is`(SG_MDH_DUE_TO_ARRIVE))
        assertThat(activitiesForYesterday[2].getCount(), `is`(0))

        val activitiesForToday = activityData.filter { it.timestamp == fetchTimestamp(today, "yyyy-MM-dd") }
        assertThat(activitiesForToday.size, `is`(3))
        assertThat(activitiesForToday[0].taskName, `is`(ENIGMA_SCRAPER_INCOMING))
        assertThat(activitiesForToday[0].getCount(), `is`(1))
        assertThat(activitiesForToday[1].taskName, `is`(ENIGMA_SCRAPER_OUTGOING))
        assertThat(activitiesForToday[1].getCount(), `is`(0))
        assertThat(activitiesForToday[2].taskName, `is`(SG_MDH_DUE_TO_ARRIVE))
        assertThat(activitiesForToday[2].getCount(), `is`(0))

        val activitiesForTomorrow = activityData.filter { it.timestamp == fetchTimestamp(tomorrow, "yyyy-MM-dd") }
        assertThat(activitiesForTomorrow.size, `is`(3))
        assertThat(activitiesForTomorrow[0].taskName, `is`(ENIGMA_SCRAPER_INCOMING))
        assertThat(activitiesForTomorrow[0].getCount(), `is`(0))
        assertThat(activitiesForTomorrow[1].taskName, `is`(ENIGMA_SCRAPER_OUTGOING))
        assertThat(activitiesForTomorrow[1].getCount(), `is`(2))
        assertThat(activitiesForTomorrow[2].taskName, `is`(SG_MDH_DUE_TO_ARRIVE))
        assertThat(activitiesForTomorrow[2].getCount(), `is`(1))
    }
}
