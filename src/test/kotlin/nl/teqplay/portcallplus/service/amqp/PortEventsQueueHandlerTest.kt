package nl.teqplay.portcallplus.service.amqp

import com.google.common.io.Resources.getResource
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallStatus
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.UpdateType
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.properties.PortEventsProperties
import nl.teqplay.portcallplus.service.internal.CsiService
import nl.teqplay.portcallplus.service.internal.amqp.PortEventsQueueHandler
import nl.teqplay.portcallplus.utils.fetchTimestamp
import nl.teqplay.portcallplus.utils.getShipRegister
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.`is`
import org.junit.jupiter.api.Assertions.assertDoesNotThrow
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito.anyString
import org.mockito.Mockito.never
import org.mockito.Mockito.spy
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.eq
import org.springframework.amqp.core.Message
import java.io.File
import java.util.Date
import java.util.concurrent.TimeUnit

@ExtendWith(MockitoExtension::class)
internal class PortEventsQueueHandlerTest {
    private lateinit var portEventsQueueHandler: PortEventsQueueHandler

    @Mock
    lateinit var portcallDataSource: PortcallDataSource

    @Mock
    lateinit var csiService: CsiService

    private val config = PortEventsProperties(listOf("USCRP"))

    @BeforeEach
    fun before() {
        portEventsQueueHandler = PortEventsQueueHandler(portcallDataSource, csiService, config)
    }

    @Test
    fun testProcessPortAta() {
        val imo = "123"
        val port = "a"
        val dateTime = Date()
        `when`(portcallDataSource.getNearestByImoAndDate(eq(imo), eq(port), eq(dateTime), anyOrNull(), anyOrNull(), eq(null), anyOrNull()))
            .thenReturn(Portcall("x", emptySet(), port, imo, ScheduledTaskType.UNKNOWN, dateTime, UpdateType.PORT, PortcallStatus.INBOUND))
        var updatedPortcall = portEventsQueueHandler.processPortAta(imo, port, dateTime.time)
        assertNotNull(updatedPortcall)
        assertThat(updatedPortcall?.status, `is`(PortcallStatus.INBOUND))
        assertThat(updatedPortcall?.portAtaTime, `is`(dateTime))

        // trigger another port.ata event after 2 hours
        val updateAta = fetchTimestamp(dateTime, 2, TimeUnit.DAYS)
        `when`(portcallDataSource.getNearestByImoAndDate(eq(imo), eq(port), eq(updateAta), anyOrNull(), anyOrNull(), eq(null), anyOrNull()))
            .thenReturn(updatedPortcall)
        updatedPortcall = portEventsQueueHandler.processPortAta(imo, port, updateAta.time)
        assertNotNull(updatedPortcall)
        assertThat(updatedPortcall?.status, `is`(PortcallStatus.INBOUND))
        // assert that portAtd or startTime has not changed
        assertThat(updatedPortcall?.portAtaTime, `is`(dateTime))
        assertThat(updatedPortcall?.startTime, `is`(dateTime))

        val imo2 = ""
        val notUpdatedPortcall = portEventsQueueHandler.processPortAta(imo2, port, dateTime.time)
        assertNull(notUpdatedPortcall)
    }

    @Test
    fun testProcessPortAtd() {
        val imo = "123"
        val port = "a"
        val portAtd = Date()
        val fromDate = fetchTimestamp(Date(), -1, TimeUnit.DAYS)
        `when`(portcallDataSource.getNearestByImoAndDate(imo, port, portAtd, fromDate, portAtd, null))
            .thenReturn(
                Portcall("x", emptySet(), port, imo, ScheduledTaskType.UNKNOWN, portAtd, UpdateType.NOMINATION, PortcallStatus.INBOUND, null),
            )

        var updatedPortcall = portEventsQueueHandler.processPortAtd(imo, port, portAtd.time, fromDate)
        assertNotNull(updatedPortcall)
        assertThat(updatedPortcall?.status, `is`(PortcallStatus.OUTBOUND))
        assertThat(updatedPortcall?.portAtdTime, `is`(portAtd))
        assertThat(updatedPortcall?.endTime, `is`(portAtd))

        // trigger another port.atd event after 2 hours
        val updateAtd = fetchTimestamp(portAtd, 2, TimeUnit.DAYS)
        `when`(portcallDataSource.getNearestByImoAndDate(imo, port, updateAtd, fromDate, updateAtd, null))
            .thenReturn(updatedPortcall)

        updatedPortcall = portEventsQueueHandler.processPortAtd(imo, port, updateAtd.time, fromDate)
        assertNotNull(updatedPortcall)
        assertThat(updatedPortcall?.status, `is`(PortcallStatus.OUTBOUND))
        // assert that portAtd time has not changed
        assertThat(updatedPortcall?.portAtdTime, `is`(portAtd))
        // but the endTime is updated with the most recent one
        assertThat(updatedPortcall?.endTime, `is`(updateAtd))

        val imo2 = ""
        val notUpdatedPortcall = portEventsQueueHandler.processPortAtd(imo2, port, portAtd.time)
        assertNull(notUpdatedPortcall)
    }

    @Test
    fun `test if USCRP port is in list and ATA being updated`() {
        assertTrue(portEventsQueueHandler.portEventsPorts.contains("USCRP"))
        assertFalse(portEventsQueueHandler.portEventsPorts.contains("NLTEST"))

        val jsonString: String = File(getResource("portevents/event_start.json").file).readText(Charsets.UTF_8)
        val testMessage = Message(jsonString.toByteArray(Charsets.UTF_8))
        val newDate = Date()
        `when`(
            portcallDataSource.getNearestByImoAndDate(eq("123"), eq("USCRP"), eq(newDate), anyOrNull(), anyOrNull(), eq(null), anyOrNull()),
        )
            .thenReturn(
                Portcall("x", emptySet(), "USCRP", "123", ScheduledTaskType.UNKNOWN, newDate, UpdateType.PORT, PortcallStatus.INBOUND),
            )
        `when`(csiService.getShipByMmsi(anyString())).thenReturn(getShipRegister(mmsi = "367534690", name = "test ship", imo = "123"))
        assertDoesNotThrow { portEventsQueueHandler.onMessage(testMessage) }
    }

    @Test
    fun `test if USCRP port is in list and ATD being updated`() {
        assertTrue(portEventsQueueHandler.portEventsPorts.contains("USCRP"))
        assertFalse(portEventsQueueHandler.portEventsPorts.contains("NLTEST"))

        val jsonString: String = File(getResource("portevents/event_end.json").file).readText(Charsets.UTF_8)
        val testMessage = Message(jsonString.toByteArray(Charsets.UTF_8))

        val imo = "123"
        val port = "USCRP"
        val portAtd = Date()
        val fromDate = fetchTimestamp(Date(), -1, TimeUnit.DAYS)
        `when`(portcallDataSource.getNearestByImoAndDate(imo, port, portAtd, fromDate, portAtd, null, true))
            .thenReturn(
                Portcall("x", emptySet(), port, imo, ScheduledTaskType.UNKNOWN, portAtd, UpdateType.NOMINATION, PortcallStatus.INBOUND, null),
            )

        `when`(csiService.getShipByMmsi(anyString())).thenReturn(getShipRegister(mmsi = "367534690", name = "test ship", imo = "123"))
        assertDoesNotThrow { portEventsQueueHandler.onMessage(testMessage) }
    }

    @Test
    fun `test if portEvent message is not processed`() {
        assertFalse(portEventsQueueHandler.portEventsPorts.contains("BRTUB"))

        val jsonString: String = File(getResource("portevents/event_test.json").file).readText(Charsets.UTF_8)
        val testMessage = Message(jsonString.toByteArray(Charsets.UTF_8))

        val imo = "12"
        val port = "BRTUB"
        val portAtd = Date()
        val portEventsQueueHandlerMock = spy(portEventsQueueHandler)
        assertDoesNotThrow { portEventsQueueHandlerMock.onMessage(testMessage) }
        verify(portEventsQueueHandlerMock, never()).processPortAta(imo, port, 1680159299000)
        verify(portEventsQueueHandlerMock, never()).processPortAtd(imo, port, 1680159299000, portAtd)
    }
}
