package nl.teqplay.portcallplus.service

import nl.teqplay.portcallplus.datasource.ActivityDataSource
import nl.teqplay.portcallplus.datasource.MTurkHITDataSource
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.datasource.ServiceFetchCounterDataSource
import nl.teqplay.portcallplus.model.data.mturk.MTurkHITResponse
import nl.teqplay.portcallplus.properties.MTurkProperties
import nl.teqplay.portcallplus.service.common.EventService
import nl.teqplay.portcallplus.service.external.MTurkService
import nl.teqplay.portcallplus.service.external.MTurkService.Companion.extractEventFromNotification
import nl.teqplay.portcallplus.service.external.MTurkService.Companion.parseAnswer
import nl.teqplay.portcallplus.utils.MTurkUtilsTest
import nl.teqplay.portcallplus.utils.removeSpecialChars
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.`is`
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever
import software.amazon.awssdk.services.mturk.model.AssignmentStatus
import java.util.Date
import java.util.UUID

@ExtendWith(MockitoExtension::class)
internal class MTurkServiceTest {
    @Mock
    lateinit var serviceFetchCounterDataSource: ServiceFetchCounterDataSource

    @Mock
    lateinit var activityDataSource: ActivityDataSource

    @Mock
    lateinit var portcallDataSource: PortcallDataSource

    @Mock
    lateinit var mTurkHITDataSource: MTurkHITDataSource

    private lateinit var mTurkService: MTurkService

    @Mock
    private lateinit var eventService: EventService

    @Mock
    private lateinit var config: MTurkProperties

    @BeforeEach
    fun before() {
        whenever(config.pullAssignments).thenReturn(true)
        whenever(config.pushAssignments).thenReturn(false)
        whenever(config.daysConfigured).thenReturn(7)
        whenever(config.approveInvalidAnswers).thenReturn(true)
        whenever(config.disposeAfterExpiring).thenReturn(true)
        whenever(config.minimumSameValidAnswers).thenReturn(2)
        whenever(config.enabled).thenReturn(true)
        /* Leaving the following lines commented as a reminder:
         * Just to avoid very unlikely random behaviour in Amazon MTurk (as the client actually tries to connect - it's not wrapped
         * and mocked in a clientService :/ ).
         * Also, to speed up the tests (so there will be no actual client creation's 400 calls). */
        /*whenever(config[mTurk.accessKeyId]).thenReturn("notEmptyString")
        whenever(config[mTurk.secretAccessKey]).thenReturn("notEmptyString")
        whenever(config[mTurk.uri]).thenReturn(URI.create("https://mturk-requester-sandbox.us-east-1.amazonaws.com"))*/
        mTurkService = MTurkService(
            activityDataSource, serviceFetchCounterDataSource, portcallDataSource, mTurkHITDataSource, config, eventService,
        )
    }

    @Test
    fun testFetchEventFromNotification() {
        val notificationMessage =
            "{ \"Type\" : \"Notification\", \"MessageId\" : \"eca242c0-28b0-5a2f-b2f4-1a97758fba64\", \"TopicArn\" : \"arn:aws:sns:us-east-1:************:PortcallPlus-DEV\", \"Subject\" : \"**********\", \"Message\" : \"{\\\"Events\\\":[{\\\"Answer\\\":\\\"<?xml version=\\\\\\\"1.0\\\\\\\" encoding=\\\\\\\"ASCII\\\\\\\"?><QuestionFormAnswers xmlns=\\\\\\\"http://mechanicalturk.amazonaws.com/AWSMechanicalTurkDataSchemas/2005-10-01/QuestionFormAnswers.xsd\\\\\\\"><Answer><QuestionIdentifier>agent<\\\\/QuestionIdentifier><FreeText>Promar agencies (belgium) nv (PROMAR)<\\\\/FreeText><\\\\/Answer><\\\\/QuestionFormAnswers>\\\",\\\"HITGroupId\\\":\\\"34KQN2TBL0ZN33DFWTQTGGHYPCXSAB\\\",\\\"EventType\\\":\\\"AssignmentSubmitted\\\",\\\"EventTimestamp\\\":\\\"2020-12-19T01:44:00Z\\\",\\\"HITId\\\":\\\"3FCO4VKO0AX35ICN6QKU7PPC8ZRE7A\\\",\\\"AssignmentId\\\":\\\"326O153BMPIL8BBRN5SIV1VVRLHEDN\\\",\\\"WorkerId\\\":\\\"A3PVABWAMDAXS8\\\",\\\"HITTypeId\\\":\\\"3RSP1MSSW6ZRFKPC8CTPZS9T4TCUKL\\\"}],\\\"EventDocId\\\":\\\"19046c05464b9de71929f75ceeb3441e0e4ee0c7\\\",\\\"SourceAccount\\\":\\\"************\\\",\\\"CustomerId\\\":\\\"A36HH7QA9D2EVK\\\",\\\"EventDocVersion\\\":\\\"2014-08-15\\\"}\", \"Timestamp\" : \"2020-12-19T01:44:31.502Z\", \"SignatureVersion\" : \"1\", \"Signature\" : \"PI74R9Q3Kifit8kJ+jc5Bsq3GkEigIeI7duGxu6ey+Sl4QSJ3WWBrL7aAP1wZrLuk1sLzoybUUUf1+9qRn0jgoa8v0nb5/Locc+0ReX/XpD2WzQCcOhmFr6duzQMR3ODa0sih8LFqvylPmGNriEbF10GKv7Dl3WvbIIdQoJ5V0v863SvHmj+U0eKAKzk/Wr0zVPAvZF4BhsXuM6814iztE4PW/9C61jCG+Xy6EjCTLHcEAlEI9QYAD/B5nqwyu4h/VHHvhpjW2kcTwEEFU7Z7Yz8ivdcoyN+EgQzCuiWG2xVt0lvauo4BXiCnt7hYUURt4jR8y3HTR4Hc/vXIkJMHQ==\", \"SigningCertURL\" : \"https://sns.us-east-1.amazonaws.com/SimpleNotificationService-010a507c1833636cd94bdb98bd93083a.pem\", \"UnsubscribeURL\" : \"https://sns.us-east-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:us-east-1:************:PortcallPlus-DEV:60642ac8-3e3b-4209-9bac-76a8cf4d4e93\""
        val eventMessage = extractEventFromNotification(notificationMessage)
        assertEquals(
            "{\"Events\":[{\"Answer\":\"<?xml version=\\\"1.0\\\" encoding=\\\"ASCII\\\"?><QuestionFormAnswers xmlns=\\\"http://mechanicalturk.amazonaws.com/AWSMechanicalTurkDataSchemas/2005-10-01/QuestionFormAnswers.xsd\\\"><Answer><QuestionIdentifier>agent<\\/QuestionIdentifier><FreeText>Promar agencies (belgium) nv (PROMAR)<\\/FreeText><\\/Answer><\\/QuestionFormAnswers>\",\"HITGroupId\":\"34KQN2TBL0ZN33DFWTQTGGHYPCXSAB\",\"EventType\":\"AssignmentSubmitted\",\"EventTimestamp\":\"2020-12-19T01:44:00Z\",\"HITId\":\"3FCO4VKO0AX35ICN6QKU7PPC8ZRE7A\",\"AssignmentId\":\"326O153BMPIL8BBRN5SIV1VVRLHEDN\",\"WorkerId\":\"A3PVABWAMDAXS8\",\"HITTypeId\":\"3RSP1MSSW6ZRFKPC8CTPZS9T4TCUKL\"}],\"EventDocId\":\"19046c05464b9de71929f75ceeb3441e0e4ee0c7\",\"SourceAccount\":\"************\",\"CustomerId\":\"A36HH7QA9D2EVK\",\"EventDocVersion\":\"2014-08-15\"}",
            eventMessage,
        )
        val eventCallback = mTurkService.getEventFromNotification(notificationMessage)
        val answer = eventCallback!!.events.first().answer
        assertTrue(answer!!.contains("Promar agencies (belgium) nv (PROMAR)"))

        assertThat(parseAnswer(answer), `is`("PROMAR AGENCIES (BELGIUM) NV"))
        assertThat(removeSpecialChars(parseAnswer(answer)!!), `is`("PROMARAGENCIESBELGIUMNV"))
    }

    @Test
    fun testParseAnswer() {
        val notificationMessage =
            "{ \"Type\" : \"Notification\", \"MessageId\" : \"eca242c0-28b0-5a2f-b2f4-1a97758fba64\", \"TopicArn\" : \"arn:aws:sns:us-east-1:************:PortcallPlus-DEV\", \"Subject\" : \"**********\", \"Message\" : \"{\\\"Events\\\":[{\\\"Answer\\\":\\\"<?xml version=\\\\\\\"1.0\\\\\\\" encoding=\\\\\\\"ASCII\\\\\\\"?><QuestionFormAnswers xmlns=\\\\\\\"http://mechanicalturk.amazonaws.com/AWSMechanicalTurkDataSchemas/2005-10-01/QuestionFormAnswers.xsd\\\\\\\"><Answer><QuestionIdentifier>agent<\\\\/QuestionIdentifier><FreeText>Herfurth &amp; co (HERFUR)<\\\\/FreeText><\\\\/Answer><\\\\/QuestionFormAnswers>\\\",\\\"HITGroupId\\\":\\\"34KQN2TBL0ZN33DFWTQTGGHYPCXSAB\\\",\\\"EventType\\\":\\\"AssignmentSubmitted\\\",\\\"EventTimestamp\\\":\\\"2020-12-19T01:44:00Z\\\",\\\"HITId\\\":\\\"3FCO4VKO0AX35ICN6QKU7PPC8ZRE7A\\\",\\\"AssignmentId\\\":\\\"326O153BMPIL8BBRN5SIV1VVRLHEDN\\\",\\\"WorkerId\\\":\\\"A3PVABWAMDAXS8\\\",\\\"HITTypeId\\\":\\\"3RSP1MSSW6ZRFKPC8CTPZS9T4TCUKL\\\"}],\\\"EventDocId\\\":\\\"19046c05464b9de71929f75ceeb3441e0e4ee0c7\\\",\\\"SourceAccount\\\":\\\"************\\\",\\\"CustomerId\\\":\\\"A36HH7QA9D2EVK\\\",\\\"EventDocVersion\\\":\\\"2014-08-15\\\"}\", \"Timestamp\" : \"2020-12-19T01:44:31.502Z\", \"SignatureVersion\" : \"1\", \"Signature\" : \"PI74R9Q3Kifit8kJ+jc5Bsq3GkEigIeI7duGxu6ey+Sl4QSJ3WWBrL7aAP1wZrLuk1sLzoybUUUf1+9qRn0jgoa8v0nb5/Locc+0ReX/XpD2WzQCcOhmFr6duzQMR3ODa0sih8LFqvylPmGNriEbF10GKv7Dl3WvbIIdQoJ5V0v863SvHmj+U0eKAKzk/Wr0zVPAvZF4BhsXuM6814iztE4PW/9C61jCG+Xy6EjCTLHcEAlEI9QYAD/B5nqwyu4h/VHHvhpjW2kcTwEEFU7Z7Yz8ivdcoyN+EgQzCuiWG2xVt0lvauo4BXiCnt7hYUURt4jR8y3HTR4Hc/vXIkJMHQ==\", \"SigningCertURL\" : \"https://sns.us-east-1.amazonaws.com/SimpleNotificationService-010a507c1833636cd94bdb98bd93083a.pem\", \"UnsubscribeURL\" : \"https://sns.us-east-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:us-east-1:************:PortcallPlus-DEV:60642ac8-3e3b-4209-9bac-76a8cf4d4e93\""
        val eventMessage = extractEventFromNotification(notificationMessage)
        assertEquals(
            "{\"Events\":[{\"Answer\":\"<?xml version=\\\"1.0\\\" encoding=\\\"ASCII\\\"?><QuestionFormAnswers xmlns=\\\"http://mechanicalturk.amazonaws.com/AWSMechanicalTurkDataSchemas/2005-10-01/QuestionFormAnswers.xsd\\\"><Answer><QuestionIdentifier>agent<\\/QuestionIdentifier><FreeText>Herfurth &amp; co (HERFUR)<\\/FreeText><\\/Answer><\\/QuestionFormAnswers>\",\"HITGroupId\":\"34KQN2TBL0ZN33DFWTQTGGHYPCXSAB\",\"EventType\":\"AssignmentSubmitted\",\"EventTimestamp\":\"2020-12-19T01:44:00Z\",\"HITId\":\"3FCO4VKO0AX35ICN6QKU7PPC8ZRE7A\",\"AssignmentId\":\"326O153BMPIL8BBRN5SIV1VVRLHEDN\",\"WorkerId\":\"A3PVABWAMDAXS8\",\"HITTypeId\":\"3RSP1MSSW6ZRFKPC8CTPZS9T4TCUKL\"}],\"EventDocId\":\"19046c05464b9de71929f75ceeb3441e0e4ee0c7\",\"SourceAccount\":\"************\",\"CustomerId\":\"A36HH7QA9D2EVK\",\"EventDocVersion\":\"2014-08-15\"}",
            eventMessage,
        )
        val eventCallback = mTurkService.getEventFromNotification(notificationMessage)
        val answer = eventCallback!!.events.first().answer
        assertTrue(answer!!.contains("Herfurth &amp; co (HERFUR)"))

        assertThat(parseAnswer(answer), `is`("HERFURTH & CO"))
        assertThat(removeSpecialChars(parseAnswer(answer)!!), `is`("HERFURTHCO"))
    }

    @Test
    fun testServiceCount() {
        whenever(config.autoReject).thenReturn(false)
        val notificationMessage =
            "{ \"Type\" : \"Notification\", \"MessageId\" : \"eca242c0-28b0-5a2f-b2f4-1a97758fba64\", \"TopicArn\" : \"arn:aws:sns:us-east-1:************:PortcallPlus-DEV\", \"Subject\" : \"**********\", \"Message\" : \"{\\\"Events\\\":[{\\\"Answer\\\":\\\"<?xml version=\\\\\\\"1.0\\\\\\\" encoding=\\\\\\\"ASCII\\\\\\\"?><QuestionFormAnswers xmlns=\\\\\\\"http://mechanicalturk.amazonaws.com/AWSMechanicalTurkDataSchemas/2005-10-01/QuestionFormAnswers.xsd\\\\\\\"><Answer><QuestionIdentifier>agent<\\\\/QuestionIdentifier><FreeText>Promar agencies (belgium) nv (PROMAR)<\\\\/FreeText><\\\\/Answer><\\\\/QuestionFormAnswers>\\\",\\\"HITGroupId\\\":\\\"34KQN2TBL0ZN33DFWTQTGGHYPCXSAB\\\",\\\"EventType\\\":\\\"AssignmentSubmitted\\\",\\\"EventTimestamp\\\":\\\"2020-12-19T01:44:00Z\\\",\\\"HITId\\\":\\\"3FCO4VKO0AX35ICN6QKU7PPC8ZRE7A\\\",\\\"AssignmentId\\\":\\\"326O153BMPIL8BBRN5SIV1VVRLHEDN\\\",\\\"WorkerId\\\":\\\"A3PVABWAMDAXS8\\\",\\\"HITTypeId\\\":\\\"3RSP1MSSW6ZRFKPC8CTPZS9T4TCUKL\\\"}],\\\"EventDocId\\\":\\\"19046c05464b9de71929f75ceeb3441e0e4ee0c7\\\",\\\"SourceAccount\\\":\\\"************\\\",\\\"CustomerId\\\":\\\"A36HH7QA9D2EVK\\\",\\\"EventDocVersion\\\":\\\"2014-08-15\\\"}\", \"Timestamp\" : \"2020-12-19T01:44:31.502Z\", \"SignatureVersion\" : \"1\", \"Signature\" : \"PI74R9Q3Kifit8kJ+jc5Bsq3GkEigIeI7duGxu6ey+Sl4QSJ3WWBrL7aAP1wZrLuk1sLzoybUUUf1+9qRn0jgoa8v0nb5/Locc+0ReX/XpD2WzQCcOhmFr6duzQMR3ODa0sih8LFqvylPmGNriEbF10GKv7Dl3WvbIIdQoJ5V0v863SvHmj+U0eKAKzk/Wr0zVPAvZF4BhsXuM6814iztE4PW/9C61jCG+Xy6EjCTLHcEAlEI9QYAD/B5nqwyu4h/VHHvhpjW2kcTwEEFU7Z7Yz8ivdcoyN+EgQzCuiWG2xVt0lvauo4BXiCnt7hYUURt4jR8y3HTR4Hc/vXIkJMHQ==\", \"SigningCertURL\" : \"https://sns.us-east-1.amazonaws.com/SimpleNotificationService-010a507c1833636cd94bdb98bd93083a.pem\", \"UnsubscribeURL\" : \"https://sns.us-east-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:us-east-1:************:PortcallPlus-DEV:60642ac8-3e3b-4209-9bac-76a8cf4d4e93\""
        val eventMessage = mTurkService.getEventFromNotification(notificationMessage)

        val portcallId = "test"
        val answer = parseAnswer(eventMessage!!.events.first().answer!!)

        var counter = mTurkService.addServiceCounter(portcallId, answer, "PROMAR AGENCIES (BELGIUM) NV")
        assertThat(counter.agentUpdate, `is`(setOf(portcallId)))

        counter = mTurkService.addServiceCounter(portcallId, answer, "PROMAR AGENCIES (BELGIUM)  NV")
        assertThat(counter.agentUpdate, `is`(setOf(portcallId)))

        counter = mTurkService.addServiceCounter(portcallId, answer, "PROMAR AGENCIES (BELGIUM)")
        assertThat(counter.failedImoNumbers, `is`(setOf(portcallId)))

        counter = mTurkService.addServiceCounter(portcallId, " ", "PROMAR AGENCIES (BELGIUM) NV")
        assertThat(counter.emptyResult, `is`(setOf(portcallId)))
    }

    @Test
    fun testAnswersEvaluation_validAnswer() {
        val assignment1 = getMTurkHITResponse(formAgent = "Independent port agencies bvba (IPA)")
        val assignment2 = getMTurkHITResponse(formAgent = "Euro nordic agencies belgium nv (EUNOAB)")
        val assignment3 = getMTurkHITResponse(formAgent = "some random invalid answer")
        val assignment4 = getMTurkHITResponse(formAgent = "Independent port agencies bvba (IPA)")
        val assignment5 = getMTurkHITResponse(formAgent = "A.p. m&#246;ller - maersk a/s (MAERSK)")

        val mTurkHITDetails = MTurkUtilsTest.getMTurkHITDetails(
            assignments = listOf(assignment1, assignment2, assignment3, assignment4, assignment5),
        )

        val vesselAgentsByFormattedKey = listOf(
            "INDEPENDENT PORT AGENCIES BVBA",
            "WILHELMSEN PORT SERVICES BELGIUM NV",
            "EURO NORDIC AGENCIES BELGIUM NV",
            "A.P. MÖLLER - MAERSK A/S",
        ).associateBy {
            removeSpecialChars(it)
        }

        val (processedHitDetails, validAgent) = with(mTurkService) {
            processAssignments(mTurkHITDetails, vesselAgentsByFormattedKey)
        }
        assertEquals("INDEPENDENT PORT AGENCIES BVBA", validAgent)
    }

    @Test
    fun testAnswersEvaluation_notValidAnswer() {
        val assignment1 = getMTurkHITResponse(formAgent = "Independent port agencies bvba (IPA)")
        val assignment2 = getMTurkHITResponse(formAgent = "Euro nordic agencies belgium nv (EUNOAB)")
        val assignment3 = getMTurkHITResponse(formAgent = "some random invalid answer")
        val assignment4 = getMTurkHITResponse(formAgent = "Cma cgm belgium nv (CMACGM)")
        val assignment5 = getMTurkHITResponse(formAgent = "A.p. m&#246;ller - maersk a/s (MAERSK)")

        val mTurkHITDetails = MTurkUtilsTest.getMTurkHITDetails(
            assignments = listOf(assignment1, assignment2, assignment3, assignment4, assignment5),
        )

        val vesselAgentsByFormattedKey = listOf(
            "INDEPENDENT PORT AGENCIES BVBA",
            "WILHELMSEN PORT SERVICES BELGIUM NV",
            "EURO NORDIC AGENCIES BELGIUM NV",
            "A.P. MÖLLER - MAERSK A/S",
        ).associateBy {
            removeSpecialChars(it)
        }

        val (processedHitDetails, validAgent) = with(mTurkService) {
            processAssignments(mTurkHITDetails, vesselAgentsByFormattedKey)
        }
        assertNull(validAgent)
    }

    private fun getMTurkHITResponse(
        assignmentId: String = UUID.randomUUID().toString(),
        workerId: String = UUID.randomUUID().toString(),
        status: AssignmentStatus = AssignmentStatus.SUBMITTED,
        formAgent: String? = null,
        submitTime: Date? = null,
        approvalTime: Date? = null,
        isRejected: Boolean = false,
        feedback: String? = null,
    ): MTurkHITResponse {
        val answer = "<?xml version=\"1.0\" encoding=\"ASCII\"?><QuestionFormAnswers xmlns=\"http://mechanicalturk.amazonaws.com/AWSMechanicalTurkDataSchemas/2005-10-01/QuestionFormAnswers.xsd\"><Answer><QuestionIdentifier>agent</QuestionIdentifier><FreeText>" +
            formAgent +
            "</FreeText></Answer></QuestionFormAnswers>"
        return MTurkHITResponse(
            assignmentId = assignmentId,
            workerId = workerId,
            status = status,
            answer = answer,
            submitTime = submitTime,
            approvalTime = approvalTime,
            isRejected = isRejected,
            feedback = feedback,
        )
    }
}
