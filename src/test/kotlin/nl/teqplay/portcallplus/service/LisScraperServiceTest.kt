package nl.teqplay.portcallplus.service

import com.google.common.io.Resources.getResource
import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.shiphistory.model.AisCurrentMessage
import nl.teqplay.csi.model.ship.info.ShipRegisterInfo
import nl.teqplay.poma.api.v1.Port
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.datasource.ActivityDataSource
import nl.teqplay.portcallplus.datasource.LisDataSource
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.datasource.ScheduledTaskRunDataSource
import nl.teqplay.portcallplus.datasource.ServiceFetchCounterDataSource
import nl.teqplay.portcallplus.model.ScheduledTaskTypeFieldsProxy
import nl.teqplay.portcallplus.model.service.LisTravel
import nl.teqplay.portcallplus.properties.LisProperties
import nl.teqplay.portcallplus.service.external.LIS_DATE_FORMAT
import nl.teqplay.portcallplus.service.external.LIS_TIMEZONE
import nl.teqplay.portcallplus.service.external.LisScraperService
import nl.teqplay.portcallplus.service.internal.CsiService
import nl.teqplay.portcallplus.service.internal.PlatformService
import nl.teqplay.portcallplus.service.internal.PoMaService
import nl.teqplay.portcallplus.utils.TestHelper
import nl.teqplay.portcallplus.utils.createTestPort
import nl.teqplay.portcallplus.utils.fetchTimestamp
import nl.teqplay.portcallplus.utils.getScheldePort
import nl.teqplay.portcallplus.utils.getShipRegister
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.util.FileLog
import org.jsoup.Jsoup
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import org.mockito.kotlin.spy
import org.mockito.kotlin.whenever
import java.io.File
import java.time.Duration
import java.time.Instant
import java.util.Date
import java.util.concurrent.TimeUnit

@ExtendWith(MockitoExtension::class)
internal class LisScraperServiceTest {
    private lateinit var lisScraperService: LisScraperService

    @Mock
    lateinit var lisDataSource: LisDataSource

    @Mock
    lateinit var scheduledTaskRunDataSource: ScheduledTaskRunDataSource

    @Mock
    lateinit var activityDataSource: ActivityDataSource

    @Mock
    lateinit var serviceFetchCounterDataSource: ServiceFetchCounterDataSource

    @Mock
    lateinit var portcallDataSource: PortcallDataSource

    @Mock
    lateinit var platformService: PlatformService

    @Mock
    private lateinit var scheduledTaskTypeFieldsProxy: ScheduledTaskTypeFieldsProxy

    @Mock
    private lateinit var csiService: CsiService

    private val config = LisProperties(false, false, Duration.ZERO)

    private val pomaService = mock<PoMaService>()

    private val fileLogMock: FileLog = mock<FileLog>()

    @BeforeEach
    fun before() {
        lisScraperService = spy(
            LisScraperService(
                lisDataSource, platformService, csiService,
                scheduledTaskRunDataSource, activityDataSource, serviceFetchCounterDataSource, portcallDataSource, config,
                scheduledTaskTypeFieldsProxy, mock(), pomaService, mock(),
            ),
        )
    }

    @Test
    fun testOrderTimeChanged() {
        val timestamp = fetchTimestamp(Date(), 2, TimeUnit.HOURS)
        val orderTime = fetchTimestamp(timestamp, LIS_DATE_FORMAT, LIS_TIMEZONE)
        val portcall = TestHelper.getTestPortcall(
            port = Portcall.IDPREFIX_VLI,
            startTime = lisScraperService.getOrderTime(orderTime)!!,
        )

        `when`(portcallDataSource.getMostRecentByImo(portcall.imo, portcall.port)).thenReturn(portcall)

        val updatedPortcall = lisScraperService.convertToPortcall(
            LisTravel(
                "West: Wandelaar",
                "Vlissingen: Cittershaven Total 6",
                "CB ADRIATIC",
                "12345",
                orderTime,
                null,
                null,
            ),
            portcall.imo,
            ScheduledTaskType.LIS_SCRAPPER,
        )
        assertEquals(lisScraperService.getOrderTime(orderTime), updatedPortcall.startTime)
    }

    @Test
    fun testFetchLisTravels() {
        val document = Jsoup.parse(
            File(getResource("lis/lis-incoming.html").file),
            "UTF-8",
            "https://lis.loodswezen.be/Lis/VerwachteReizen.aspx",
        )
        assertTrue(document != null)
        val lisUpdates = lisScraperService.fetchLisTravels(document)
        assertEquals(91, lisUpdates.size)
        assertEquals("22/07/19 15:00", lisUpdates.find { it.shipName == "SKS MOSEL" }!!.orderTime)
    }

    @Test
    fun testSelectBestShipInfo() {
        val currentInfoMap = mutableMapOf<String, AisCurrentMessage>()
        val amakristina = getShipRegister("*********", null, "AMAKRISTINA", length = 135.0)
        val amakswan = getShipRegister("*********", "9217333", "AMAK SWAN", length = 113.0)
        val amakswan2 = getShipRegister("*********", "9217332", "AMAK SWAN", length = 158.0)

        currentInfoMap[amakristina.identifiers.mmsi!!] = getAisMessage(
            amakristina,
            destination = "BASEL",
            messageTime = Instant.now() - Duration.ofMinutes(1),
        )
        currentInfoMap[amakswan.identifiers.mmsi!!] = getAisMessage(
            amakswan,
            destination = "NOWHERE",
            messageTime = Instant.now() - Duration.ofMinutes(1),
        )
        currentInfoMap[amakswan2.identifiers.mmsi!!] = getAisMessage(
            amakswan2,
            destination = "PTLEI>BEANR",
            messageTime = Instant.now() - Duration.ofHours(1),
        )
        whenever(platformService.getCurrentByMmsis(any())).then { args ->
            val mmsis = (args.arguments[0] as? Set<*>) ?: emptySet<String>()
            currentInfoMap.filter { it.key in mmsis }
                .map { it.value }
        }

        val selectedShipInfo = lisScraperService.selectBestShipInfo(
            listOf(amakristina, amakswan),
            LisTravel(
                "From",
                "To",
                "AMAK SWAN",
                null,
                "21/02/21 20:00",
                113.0,
                17.0,
            ),
        )
        assertNotNull(selectedShipInfo)
        assertEquals("AMAK SWAN", selectedShipInfo!!.identifiers.name)

        val selectedShipInfo2 = lisScraperService.selectBestShipInfo(
            listOf(amakristina, amakswan, amakswan2),
            LisTravel(
                "From",
                "To",
                "AMAK SWAN",
                null,
                "21/02/21 20:00",
                113.0,
                17.0,
            ),
        )
        assertNotNull(selectedShipInfo2)
        assertEquals("AMAK SWAN", selectedShipInfo2!!.identifiers.name)
        assertEquals(amakswan.identifiers.mmsi, selectedShipInfo2.identifiers.mmsi)

        // see if ship is matched based on the destination
        val selectedShipInfo3 = lisScraperService.selectBestShipInfo(
            listOf(amakristina, amakswan, amakswan2),
            LisTravel(
                "From",
                "Antwerpen",
                "AMAK SWAN",
                null,
                "21/02/21 20:00",
                190.0,
                17.0,
            ),
        )
        assertNotNull(selectedShipInfo3)
        assertEquals("AMAK SWAN", selectedShipInfo3!!.identifiers.name)
        assertEquals(amakswan2.identifiers.mmsi, selectedShipInfo3.identifiers.mmsi)

        val selectedShipInfo4 = lisScraperService.selectBestShipInfo(
            listOf(amakristina),
            LisTravel(
                "From",
                "To",
                "AMAK SWAN",
                null,
                "21/02/21 20:00",
                190.0,
                17.0,
            ),
        )
        assertNull(selectedShipInfo4)

        currentInfoMap[amakswan2.identifiers.mmsi!!] = getAisMessage(
            amakswan2,
            destination = "PTLEI>BEANR",
            messageTime = Instant.now() - Duration.ofDays(14),
        )
        val selectedShipInfo5 = lisScraperService.selectBestShipInfo(
            listOf(amakristina, amakswan, amakswan2),
            LisTravel(
                "From",
                "To",
                "AMAK SWAN",
                null,
                "21/02/21 20:00",
                190.0,
                17.0,
            ),
        )
        assertEquals(amakswan.identifiers.mmsi, selectedShipInfo5?.identifiers?.mmsi)
    }

    @Test
    fun testShipNameFilter() {
        var shipName = "KAROLINE N [IMO2]"
        assertEquals("KAROLINE N", lisScraperService.filterShipName(shipName))
        shipName = "KAROLINE N[IMO2]"
        assertEquals("KAROLINE N", lisScraperService.filterShipName(shipName))
        shipName = "[IMO2] KAROLINE N"
        assertEquals("KAROLINE N", lisScraperService.filterShipName(shipName))
        shipName = "[IMO2]KAROLINE N"
        assertEquals("KAROLINE N", lisScraperService.filterShipName(shipName))
        shipName = "KAROLINE [IMO2]N"
        assertEquals("KAROLINE N", lisScraperService.filterShipName(shipName))
        shipName = "[IMO2]KAROLINE [IMO2]N"
        assertEquals("KAROLINE N", lisScraperService.filterShipName(shipName))
        shipName = "KAROLINE N (d)"
        assertEquals("KAROLINE N", lisScraperService.filterShipName(shipName))
        shipName = "(d) KAROLINE N"
        assertEquals("KAROLINE N", lisScraperService.filterShipName(shipName))
        shipName = "KAROLINE (d) N"
        assertEquals("KAROLINE N", lisScraperService.filterShipName(shipName))
        shipName = "[IMO2] KAROLINE (d) N"
        assertEquals("KAROLINE N", lisScraperService.filterShipName(shipName))
    }

    private fun getAisMessage(
        shipInfo: ShipRegisterInfo,
        messageTime: Instant,
        destination: String,
    ) = AisCurrentMessage(
        AisMessage(
            mmsi = shipInfo.identifiers.mmsi?.toInt() ?: 0,
            messageTime = messageTime,
            sources = emptySet(),
            location = Location(0.0, 0.0),
            destination = destination,
            imo = shipInfo.identifiers.imo?.toInt(),
            name = shipInfo.identifiers.name,
        ),
        derived = null,
    )

    @Test
    fun testShipFoundInMapping() {
        val timestamp = fetchTimestamp(Date(), 2, TimeUnit.HOURS)
        val orderTime = fetchTimestamp(timestamp, LIS_DATE_FORMAT, LIS_TIMEZONE)
        val portcall = TestHelper.getTestPortcall(
            port = Portcall.IDPREFIX_VLI,
            startTime = lisScraperService.getOrderTime(orderTime)!!,
        )

        `when`(portcallDataSource.getMostRecentByImo(portcall.imo, portcall.port)).thenReturn(portcall)

        val updatedPortcall = lisScraperService.convertToPortcall(
            LisTravel(
                "West: Wandelaar",
                "Vlissingen: Cittershaven Total 6",
                "CB ADRIATIC",
                "12345",
                orderTime,
                null,
                null,
            ),
            portcall.imo,
            ScheduledTaskType.LIS_SCRAPPER,
        )

        assertEquals(updatedPortcall.port, Portcall.IDPREFIX_VLI)
    }

    @Test
    fun testLookupInPomaExistingPort() {
        val timestamp = fetchTimestamp(Date(), 2, TimeUnit.HOURS)
        val orderTime = fetchTimestamp(timestamp, LIS_DATE_FORMAT, LIS_TIMEZONE)
        val portcall = TestHelper.getTestPortcall(
            port = "TEST",
            startTime = lisScraperService.getOrderTime(orderTime)!!,
        )
        val testLisTravel = LisTravel(
            "West: Wandelaar",
            "TestPort: Test 1",
            "CB ADRIATIC",
            "12345",
            orderTime,
            null,
            null,
        )
        // Checking if there's no mapping for this unknown test port
        assertEquals(getScheldePort(testLisTravel.to), null)
        // Since the getScheldePort returns null then we will do a lookup in PoMa
        doReturn(arrayOf(createTestPort(unlocode = "TEST"))).`when`(pomaService).getPorts("TestPort")
        // Mock this so we assume existing portcall so it doesn't write to file as it doesn't matter
        `when`(portcallDataSource.getMostRecentByImo(portcall.imo, portcall.port)).thenReturn(portcall)
        val updatedPortcall = lisScraperService.convertToPortcall(
            testLisTravel,
            portcall.imo,
            ScheduledTaskType.LIS_SCRAPPER,
        )
        // Verifying the PoMa lookup
        assertEquals(updatedPortcall.port, "TEST")
    }

    @Test
    fun testLookupInPomaNonExistingPort() {
        val timestamp = fetchTimestamp(Date(), 2, TimeUnit.HOURS)
        val orderTime = fetchTimestamp(timestamp, LIS_DATE_FORMAT, LIS_TIMEZONE)
        val portcall = TestHelper.getTestPortcall(
            port = "BEANR",
            startTime = lisScraperService.getOrderTime(orderTime)!!,
        )
        val testLisTravel = LisTravel(
            "West: Wandelaar",
            "TestPort: Test 1",
            "CB ADRIATIC",
            "12345",
            orderTime,
            null,
            null,
        )
        // Checking if there's no mapping for this unknown test port
        assertEquals(getScheldePort(testLisTravel.to), null)
        // Since the getScheldePort returns null then the default Port should be BEANR for the portcall lookup
        doReturn(emptyArray<Port>()).`when`(pomaService).getPorts("TestPort")
        // Mock this so we assume existing portcall so it doesn't write to file as it doesn't matter
        `when`(portcallDataSource.generateId(any(), any(), any())).thenReturn("TEST1")
        doReturn(fileLogMock).`when`(lisScraperService).getFileLog()
        val updatedPortcall = lisScraperService.convertToPortcall(
            testLisTravel,
            portcall.imo,
            ScheduledTaskType.LIS_SCRAPPER,
        )
        // Verifying the PoMa lookup
        assertEquals(updatedPortcall.port, "BEANR")
    }
}
