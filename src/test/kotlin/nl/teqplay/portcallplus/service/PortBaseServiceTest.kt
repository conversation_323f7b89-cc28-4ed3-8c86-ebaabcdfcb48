package nl.teqplay.portcallplus.service

import com.fasterxml.jackson.module.kotlin.readValue
import com.google.common.io.Resources
import nl.teqplay.poma.api.v1.Berth
import nl.teqplay.portcallplus.api.model.PortcallPurpose
import nl.teqplay.portcallplus.api.model.PortcallStatus
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.datasource.ActivityDataSource
import nl.teqplay.portcallplus.datasource.PortBaseDataSource
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.datasource.ScheduledTaskRunDataSource
import nl.teqplay.portcallplus.datasource.ServiceFetchCounterDataSource
import nl.teqplay.portcallplus.model.ScheduledTaskTypeFieldsProxy
import nl.teqplay.portcallplus.model.httpResponse.PortBaseListenResponse
import nl.teqplay.portcallplus.objectMapper
import nl.teqplay.portcallplus.properties.PortBaseClientProperties
import nl.teqplay.portcallplus.properties.PortBaseProperties
import nl.teqplay.portcallplus.service.external.portbase.IAmConnectedPortBaseService
import nl.teqplay.portcallplus.service.external.portbase.VopakPortBaseService
import nl.teqplay.portcallplus.service.internal.PoMaService
import nl.teqplay.portcallplus.utils.fetchTimestamp
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.spy
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.boot.test.system.CapturedOutput
import org.springframework.boot.test.system.OutputCaptureExtension
import java.io.File
import java.util.Date

@ExtendWith(MockitoExtension::class)
@ExtendWith(OutputCaptureExtension::class)
internal class PortBaseServiceTest {
    @Mock
    lateinit var portcallDataSource: PortcallDataSource

    @Mock
    lateinit var scheduledTaskRunDataSource: ScheduledTaskRunDataSource

    @Mock
    lateinit var portBaseDataSource: PortBaseDataSource

    @Mock
    lateinit var activityDataSource: ActivityDataSource

    @Mock
    lateinit var serviceFetchCounterDataSource: ServiceFetchCounterDataSource

    @Mock
    private lateinit var scheduledTaskTypeFieldsProxy: ScheduledTaskTypeFieldsProxy

    private val clientProperties = PortBaseClientProperties(true, true, "", "", "")
    private val config =
        PortBaseProperties(true, "", 0L, 10000L, clientProperties, clientProperties, clientProperties, clientProperties, clientProperties)

    private lateinit var portBaseService: VopakPortBaseService
    private lateinit var iAmConnectedPortBaseService: IAmConnectedPortBaseService

    private val pomaServiceMock = mock<PoMaService>()

    @BeforeEach
    fun before() {
        whenever(pomaServiceMock.getBerths(anyOrNull(), anyOrNull())).thenReturn(emptyArray<Berth>())
        portBaseService = VopakPortBaseService(
            portBaseDataSource, scheduledTaskRunDataSource, activityDataSource,
            serviceFetchCounterDataSource, portcallDataSource, config, scheduledTaskTypeFieldsProxy, mock(), pomaServiceMock, mock(), mock(),
        )

        iAmConnectedPortBaseService = IAmConnectedPortBaseService(
            portBaseDataSource, scheduledTaskRunDataSource, activityDataSource,
            serviceFetchCounterDataSource, portcallDataSource, config, scheduledTaskTypeFieldsProxy, mock(), pomaServiceMock, mock(), mock(),
        )
    }

    @Test
    fun `test getStatus Inbound`() {
        val portBaseListenResponse = getTestPortBaseListenResponse(PortcallStatus.INBOUND)
        val status = portBaseService.getStatus(portBaseListenResponse!!.updates.first().after!!)
        assertEquals(PortcallStatus.INBOUND, status)
    }

    @Test
    fun `test getStatus Alongside`() {
        val portBaseListenResponse = getTestPortBaseListenResponse(PortcallStatus.ALONGSIDE)
        val status = portBaseService.getStatus(portBaseListenResponse!!.updates.first().after!!)
        assertEquals(PortcallStatus.ALONGSIDE, status)
    }

    @Test
    fun `test getStatus Outbound`() {
        val portBaseListenResponse = getTestPortBaseListenResponse(PortcallStatus.OUTBOUND)
        val status = portBaseService.getStatus(portBaseListenResponse!!.updates.first().after!!)
        assertEquals(PortcallStatus.OUTBOUND, status)
    }

    @Test
    fun `test getStatus Shifting`() {
        val portBaseListenResponse = getTestPortBaseListenResponse(PortcallStatus.SHIFTING)
        val status = portBaseService.getStatus(portBaseListenResponse!!.updates.first().after!!)
        assertEquals(PortcallStatus.SHIFTING, status)
    }

    @Test
    fun `convert to portcall`() {
        val portBaseListenResponse = getTestPortBaseListenResponse(PortcallStatus.INBOUND)
        val portcall = portBaseService.convertToPortcall(
            portBaseListenResponse?.updates?.first()?.after!!,
            portBaseListenResponse.updates.first().after?.vessel?.imoCode!!,
        )

        assertEquals("NLRTM", portcall.port)
        assertEquals("NLRTM19950030", portcall.portcallId)
        assertEquals("9571117", portcall.imo)
        assertEquals("NLRTM", portcall.destinationUnlocode)
        assertEquals(PortcallStatus.INBOUND, portcall.status)

        assertEquals("EUROH APM TERMINALS", portcall.visits.first().berthName)
        assertEquals(fetchTimestamp("2019-03-14T11:00:00Z"), portcall.visits.first().berthEta)
        assertEquals(fetchTimestamp("2019-03-15T11:00:00Z"), portcall.visits.first().berthEtd)
        assertNull(portcall.visits.first().berthAta)
        assertNull(portcall.visits.first().berthAtd)

        assertEquals("AMAZH ECT DDE", portcall.visits[1].berthName)
        assertEquals(fetchTimestamp("2019-03-16T11:00:00Z"), portcall.visits[1].berthEta)
        assertEquals(fetchTimestamp("2019-03-17T11:00:00Z"), portcall.visits[1].berthEtd)
        assertNull(portcall.visits[1].berthAta)
        assertNull(portcall.visits[1].berthAtd)
        assertTrue(portcall.purpose.contains(PortcallPurpose.BUNKERING))
        assertTrue(portcall.purpose.contains(PortcallPurpose.DISCHARGE))
    }

    private fun getTestPortBaseListenResponse(status: PortcallStatus): PortBaseListenResponse? {
        val file = when (status) {
            PortcallStatus.INBOUND -> File(Resources.getResource("portbase/PortbaseListenResponseInbound.json").file)
            PortcallStatus.ALONGSIDE -> File(Resources.getResource("portbase/PortbaseListenResponseAlongside.json").file)
            PortcallStatus.SHIFTING -> File(Resources.getResource("portbase/PortbaseListenResponseShifting.json").file)
            PortcallStatus.OUTBOUND -> File(Resources.getResource("portbase/PortbaseListenResponseOutbound.json").file)
            else -> null
        } ?: return null
        return objectMapper.readValue<PortBaseListenResponse>(file)
    }

    @Test
    fun `test portcall no startTime to be found`(output: CapturedOutput) {
        val portBaseListenResponse = objectMapper.readValue<PortBaseListenResponse>(
            File(Resources.getResource("portbase/PortbaseIAMConnectedInvalid.json").file),
        )
        val mockService = spy(iAmConnectedPortBaseService)
        mockService.processResponse(portBaseListenResponse)
        assertTrue(output.contains("9224166 - Not processing PortBasePortCall update"))
        verify(mockService, times(1)).convertToPortcall(any(), eq("9224166"), eq(ScheduledTaskType.IAMCONNECTED_PORTBASE))
    }

    @Test
    fun `test portcall valid startTime to be found`(output: CapturedOutput) {
        val portBaseListenResponse = objectMapper.readValue<PortBaseListenResponse>(
            File(Resources.getResource("portbase/PortbaseIAMConnected.json").file),
        )
        val mockService = spy(iAmConnectedPortBaseService)
        whenever(mockService.getTimestampFrom()).thenReturn(Date(1717020000000))
        mockService.processResponse(portBaseListenResponse)
        assertFalse(output.contains("9224166 - Not processing PortBasePortCall update"))
        assertTrue(
            output.contains(
                "NLRTM:NLRTM24012220:9224166 - Portcall agent mapping not found for 'Vertom Agencies B.V.' and 'NLRTM', thus creating new one",
            ),
        )
        assertTrue(output.contains("NLRTM:NLRTM24012220:9224166 - Creating portcall with startTime: 2024-05-31T15:00:00Z"))
        assertTrue(
            output.contains(
                "NLRTM:NLRTM24012220:9224166 - PortCall updated by task: IAMCONNECTED_PORTBASE via IAmConnectedPortBaseService",
            ),
        )
        verify(mockService, times(1)).convertToPortcall(any(), eq("9224166"), eq(ScheduledTaskType.IAMCONNECTED_PORTBASE))
    }
}
