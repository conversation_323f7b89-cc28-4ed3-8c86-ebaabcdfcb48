package nl.teqplay.portcallplus.service

import nl.teqplay.aisengine.event.interfaces.PortcallPlusEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusAgentChangedEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusAtaEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusEtaEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusVisitsUpdateEvent
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.poma.api.v1.Berth
import nl.teqplay.portcallplus.PreconditionException
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.Portcall.Companion.IDPREFIX_USCRP
import nl.teqplay.portcallplus.api.model.PortcallAlias
import nl.teqplay.portcallplus.api.model.PortcallAliasName
import nl.teqplay.portcallplus.api.model.PortcallStatus
import nl.teqplay.portcallplus.api.model.PortcallVisit
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.CORPUS_CHRISTI
import nl.teqplay.portcallplus.api.model.UpdateType
import nl.teqplay.portcallplus.datasource.ActivityDataSource
import nl.teqplay.portcallplus.datasource.CorpusChristiProcessedFilesDataSource
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.datasource.ScheduledTaskRunDataSource
import nl.teqplay.portcallplus.datasource.ServiceFetchCounterDataSource
import nl.teqplay.portcallplus.model.ScheduledTaskTypeFieldsProxy
import nl.teqplay.portcallplus.model.data.corpuschristi.FileParsingResult
import nl.teqplay.portcallplus.model.data.corpuschristi.FileParsingSummary
import nl.teqplay.portcallplus.model.data.corpuschristi.Movement
import nl.teqplay.portcallplus.objectMapper
import nl.teqplay.portcallplus.properties.CorpusChristiProperties
import nl.teqplay.portcallplus.properties.CorpusChristiS3Properties
import nl.teqplay.portcallplus.service.common.EventService
import nl.teqplay.portcallplus.service.common.S3BucketClient
import nl.teqplay.portcallplus.service.external.CorpusChristiService
import nl.teqplay.portcallplus.service.internal.PoMaService
import nl.teqplay.portcallplus.service.internal.UserService
import nl.teqplay.portcallplus.utils.TestHelper
import nl.teqplay.portcallplus.utils.createTestBerth
import nl.teqplay.portcallplus.utils.fetchTimestamp
import nl.teqplay.skeleton.common.exception.NotFoundException
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.doAnswer
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.spy
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.Date
import java.util.UUID
import java.util.concurrent.TimeUnit

@ExtendWith(MockitoExtension::class)
class CorpusChristiServiceTest() {
    // the service to test!
    private lateinit var corpusChristiService: CorpusChristiService
    private lateinit var eventService: EventService

    // the required mocked beans
    private val s3BucketClient = mock<S3BucketClient>()
    private val processedFilesDataSource = mock<CorpusChristiProcessedFilesDataSource>()
    private val scheduledTaskRunDataSource = mock<ScheduledTaskRunDataSource>()
    private val activityDataSource = mock<ActivityDataSource>()
    private val serviceFetchCounterDataSource = mock<ServiceFetchCounterDataSource>()
    private val portcallDataSource = mock<PortcallDataSource>()
    private val scheduledTaskTypeFieldsProxy = mock<ScheduledTaskTypeFieldsProxy>()
    private val userService = mock<UserService>()
    private val pomaService = mock<PoMaService>()
    private val eventStreamService = mock<EventStreamService>()

    @BeforeEach
    fun beforeEach() {
        val s3Config = CorpusChristiS3Properties("", "", "", "", Duration.ofDays(30))
        val config = CorpusChristiProperties(true, true, Duration.ofDays(30), Duration.ZERO, true, 10, s3Config)

        eventService = spy(EventService(eventStreamService))

        corpusChristiService = CorpusChristiService(
            s3BucketClient = s3BucketClient,
            processedFilesDataSource = processedFilesDataSource,
            mapper = objectMapper,
            scheduledTaskRunDataSource = scheduledTaskRunDataSource,
            activityDataSource = activityDataSource,
            serviceFetchCounterDataSource = serviceFetchCounterDataSource,
            portcallDataSource = portcallDataSource,
            scheduledTaskTypeFieldsProxy = scheduledTaskTypeFieldsProxy,
            userService = userService,
            pomaService = pomaService,
            eventService = eventService,
            corpusChristiProperties = config,
        )
    }

    @Test
    fun testProcess_FileNotFound() {
        val testFileName = "corpusChristi/test_20250303_084946.json"
        whenever(s3BucketClient.getSimpleObject(testFileName)).thenThrow(NotFoundException(""))
        val now = Instant.now()
        val processResult = corpusChristiService.processFileKey(testFileName, now)
        assertEquals(0, processResult.validMovements.size)
        assertEquals(0, processResult.invalidRawMovements.size)
    }

    @Test
    fun testProcess_emptyFile() {
        val testFileName = "corpusChristi/emptyFile.json"
        whenever(s3BucketClient.getSimpleObject(testFileName)).thenReturn(TestHelper.getResourceAsByteArray(testFileName))
        val now = Instant.now()
        val processResult = corpusChristiService.processFileKey(testFileName, now)
        assertEquals(0, processResult.validMovements.size)
        assertEquals(0, processResult.invalidRawMovements.size)
    }

    // 1 completely invalid Movement (lacking of all needed fields)
    // 1 valid Movement
    @Test
    fun testProcess_OneInvalid_Movement() {
        val testFileName = "corpusChristi/test_invalidMovement.json"
        whenever(s3BucketClient.getSimpleObject(testFileName)).thenReturn(TestHelper.getResourceAsByteArray(testFileName))

        val now = Instant.now()
        val processResult = corpusChristiService.processFileKey(testFileName, now)
        assertEquals(1, processResult.validMovements.size)
        assertEquals(1, processResult.invalidRawMovements.size)
    }

    // 1 invalid Movement (lacking of in dataExportTime)
    // 1 valid Movement
    @Test
    fun testProcess_invalidByNotDataExportTime() {
        val testFileName = "corpusChristi/test_no_dateExportTime.json"
        whenever(s3BucketClient.getSimpleObject(testFileName)).thenReturn(TestHelper.getResourceAsByteArray(testFileName))

        val now = Instant.now()
        val processResult = corpusChristiService.processFileKey(testFileName, now)
        assertEquals(1, processResult.validMovements.size)
        assertEquals(1, processResult.invalidRawMovements.size)
    }

    // 1 invalid Movement (lacking of visitNumber)
    // 1 valid Movement
    @Test
    fun testProcess_invalidByNotVisitNumber() {
        val testFileName = "corpusChristi/test_no_visitNumber.json"
        whenever(s3BucketClient.getSimpleObject(testFileName)).thenReturn(TestHelper.getResourceAsByteArray(testFileName))

        val now = Instant.now()
        val processResult = corpusChristiService.processFileKey(testFileName, now)
        assertEquals(1, processResult.validMovements.size)
        assertEquals(1, processResult.invalidRawMovements.size)
    }

    // 1 invalid Movement (lacking of vesselIMO)
    // 1 valid Movement
    @Test
    fun testProcess_invalidByNotVesselIMO() {
        val testFileName = "corpusChristi/test_noVesselIMO.json"
        whenever(s3BucketClient.getSimpleObject(testFileName)).thenReturn(TestHelper.getResourceAsByteArray(testFileName))

        val now = Instant.now()
        val processResult = corpusChristiService.processFileKey(testFileName, now)
        assertEquals(1, processResult.validMovements.size)
        assertEquals(1, processResult.invalidRawMovements.size)
    }

    // 1 invalid Movement (lacking of from_stop_location)
    // 1 valid Movement
    @Test
    fun testProcess_invalidByNoFromStopLocation() {
        val testFileName = "corpusChristi/test_no_fromStopLoation.json"
        whenever(s3BucketClient.getSimpleObject(testFileName)).thenReturn(TestHelper.getResourceAsByteArray(testFileName))

        val now = Instant.now()
        val processResult = corpusChristiService.processFileKey(testFileName, now)
        assertEquals(1, processResult.validMovements.size)
        assertEquals(1, processResult.invalidRawMovements.size)
    }

    // 1 invalid Movement (lacking of to_stop_location)
    // 1 valid Movement
    @Test
    fun testProcess_invalidByNoToStopLocation() {
        val testFileName = "corpusChristi/test_no_toStopLocation.json"
        whenever(s3BucketClient.getSimpleObject(testFileName)).thenReturn(TestHelper.getResourceAsByteArray(testFileName))

        val now = Instant.now()
        val processResult = corpusChristiService.processFileKey(testFileName, now)
        assertEquals(1, processResult.validMovements.size)
        assertEquals(1, processResult.invalidRawMovements.size)
    }

    // 1 invalid Movement (lacking of movementAgency)
    // 1 valid Movement
    @Test
    fun testProcess_invalidByNoMovementAgency() {
        val testFileName = "corpusChristi/test_no_movementAgency.json"
        whenever(s3BucketClient.getSimpleObject(testFileName)).thenReturn(TestHelper.getResourceAsByteArray(testFileName))

        val now = Instant.now()
        val processResult = corpusChristiService.processFileKey(testFileName, now)
        assertEquals(1, processResult.validMovements.size)
        assertEquals(1, processResult.invalidRawMovements.size)
    }

    // 1 invalid Movement (bad format in dataExportTime)
    // 1 valid Movement
    @Test
    fun testProcess_invalidByDataExportTimeBadFormat() {
        val testFileName = "corpusChristi/test_badFormat_dataExportTime.json"
        whenever(s3BucketClient.getSimpleObject(testFileName)).thenReturn(TestHelper.getResourceAsByteArray(testFileName))

        val now = Instant.now()
        val processResult = corpusChristiService.processFileKey(testFileName, now)
        assertEquals(1, processResult.validMovements.size)
        assertEquals(1, processResult.invalidRawMovements.size)
    }

    // 1 invalid Movement (bad format in scheduledTime)
    // 1 valid Movement
    @Test
    fun testProcess_invalidByScheduledTimeBadFormat() {
        val testFileName = "corpusChristi/test_badFormat_scheduledTime.json"
        whenever(s3BucketClient.getSimpleObject(testFileName)).thenReturn(TestHelper.getResourceAsByteArray(testFileName))

        val now = Instant.now()
        val processResult = corpusChristiService.processFileKey(testFileName, now)
        assertEquals(1, processResult.validMovements.size)
        assertEquals(1, processResult.invalidRawMovements.size)
    }

    // 1 invalid Movement (bad format in underwayTime)
    // 1 valid Movement
    @Test
    fun testProcess_invalidByUnderwayTimeBadFormat() {
        val testFileName = "corpusChristi/test_badFormat_underwayTime.json"
        whenever(s3BucketClient.getSimpleObject(testFileName)).thenReturn(TestHelper.getResourceAsByteArray(testFileName))

        val now = Instant.now()
        val processResult = corpusChristiService.processFileKey(testFileName, now)
        assertEquals(1, processResult.validMovements.size)
        assertEquals(1, processResult.invalidRawMovements.size)
    }

    // 1 invalid Movement (bad format in offTime)
    // 1 valid Movement
    @Test
    fun testProcess_invalidByOffTimeBadFormat() {
        val testFileName = "corpusChristi/test_badFormat_OffTime.json"
        whenever(s3BucketClient.getSimpleObject(testFileName)).thenReturn(TestHelper.getResourceAsByteArray(testFileName))

        val now = Instant.now()
        val processResult = corpusChristiService.processFileKey(testFileName, now)
        assertEquals(1, processResult.validMovements.size)
        assertEquals(1, processResult.invalidRawMovements.size)
    }

    @Test
    fun get_unprocessed_files() {
        val filesInBucket = listOf("processedFile1.json", "processedFile2.json", "unprocessedFile3.json", "unprocessedFile4.json")
        whenever(s3BucketClient.listObjects()).thenReturn(filesInBucket)
        whenever(processedFilesDataSource.get(filesInBucket)).thenReturn(
            listOf(
                FileParsingSummary("processedFile1.json", Instant.now().minus(1, ChronoUnit.DAYS), 0, 0),
                FileParsingSummary("processedFile2.json", Instant.now().minus(2, ChronoUnit.DAYS), 0, 0),
            ),
        )

        val unprocessedFiles = corpusChristiService.getUnreadFileKeys()
        assertEquals(listOf("unprocessedFile3.json", "unprocessedFile4.json"), unprocessedFiles)
    }

    @Test
    fun dont_process_already_processed_files() {
        val filesInBucket = listOf("processedFile1.json", "processedFile2.json", "unprocessedFile3.json", "unprocessedFile4.json")
        whenever(s3BucketClient.listObjects()).thenReturn(filesInBucket)

        whenever(processedFilesDataSource.get(filesInBucket)).thenReturn(
            listOf(
                FileParsingSummary("processedFile1.json", Instant.now().minus(1, ChronoUnit.DAYS), 0, 0),
                FileParsingSummary("processedFile2.json", Instant.now().minus(2, ChronoUnit.DAYS), 0, 0),
            ),
        )

        val spiedCorpusChristiService = spy(corpusChristiService)
        // We need to use doReturn().`when`().method() because we're spying the actual service to test.
        // verifying that the processedFilesDataSource.createOrUpdate(any()) is called for two files would also be enough.
        doReturn(
            FileParsingResult(emptyList(), emptyList(), ""),
        ).`when`(
            spiedCorpusChristiService,
        ).processFileKey(any<String>(), any<Instant>())
        spiedCorpusChristiService.getServiceModels(CORPUS_CHRISTI)
        verify(spiedCorpusChristiService, times(0)).processFileKey(eq("processedFile1.json"), any())
        verify(spiedCorpusChristiService, times(0)).processFileKey(eq("processedFile2.json"), any())
        verify(spiedCorpusChristiService, times(1)).processFileKey(eq("unprocessedFile3.json"), any())
        verify(spiedCorpusChristiService, times(1)).processFileKey(eq("unprocessedFile4.json"), any())
    }

    @Test
    fun make_sure_files_are_deleted_when_expired() {
        val now = Instant.now()
        val nonExpiredProcessedFile = FileParsingSummary("nonExpiredProcessedFile.json", now - Duration.ofDays(15), 0, 0)
        val expiredProcessedFile = FileParsingSummary("expiredProcessedFile.json", now - Duration.ofDays(45), 0, 0)
        val filesInBucket = listOf(
            nonExpiredProcessedFile.fileKey,
            expiredProcessedFile.fileKey,
            "unprocessedFile3.json",
            "unprocessedFile4.json",
        )
        whenever(s3BucketClient.listObjects()).thenReturn(filesInBucket)
        val processedFiles = listOf(nonExpiredProcessedFile, expiredProcessedFile)
        whenever(processedFilesDataSource.get(filesInBucket)).thenReturn(processedFiles)
        corpusChristiService.cleanUpS3Files()
        verify(s3BucketClient, times(0)).delete(nonExpiredProcessedFile.fileKey)
        verify(s3BucketClient, times(1)).delete(expiredProcessedFile.fileKey)
        verify(s3BucketClient, times(0)).delete("unprocessedFile3.json")
        verify(s3BucketClient, times(0)).delete("unprocessedFile4.json")
    }

    // Testing convertToPortcall scenarios

    private fun getTestingMovement(
        from_stop_location_code: String?,
        to_stop_location_code: String?,
        movement_agency: String = "A random agency",
        vesselIMO: String = "1234567",
        scheduledTime: Date? = null,
        underwayTime: Date? = null,
        offTime: Date? = null,
        _id: String = UUID.randomUUID().toString(),
        dataExportTime: Date = Date(),
        movementId: String? = UUID.randomUUID().toString(),
        visitId: String? = UUID.randomUUID().toString(),
        visitNumber: String = UUID.randomUUID().toString(),
        jobType: String? = "Arrival",
        movement_status_type_id: String? = UUID.randomUUID().toString(),
        jobStatus: String? = "",
        isCanceledMovement: String? = "FALSE",
        vesselName: String? = "Vessel Name",
        foreDraft: String? = "1.2",
        aftDraft: String? = "1.2",
        from_stop_location_id: String? = UUID.randomUUID().toString(),
        from_stop_location: String = "Some Location",
        to_stop_location_id: String? = UUID.randomUUID().toString(),
        to_stop_location: String = "Some Location",
        movement_agency_code: String? = UUID.randomUUID().toString(),
        lineHandler: String? = "A random line handler",
        tugName: String? = "A random tug name",
        pilotNo: String? = "A random pilot number",
        cargo_function: String? = "OTHER",
        product_code: String? = UUID.randomUUID().toString(),
    ): Movement {
        return Movement(
            _id = _id,
            dataExportTime = dataExportTime,
            movementId = movementId,
            visitId = visitId,
            visitNumber = visitNumber,
            jobType = jobType,
            movement_status_type_id = movement_status_type_id,
            jobStatus = jobStatus,
            isCanceledMovement = isCanceledMovement,
            vesselIMO = vesselIMO,
            vesselName = vesselName,
            scheduledTime = scheduledTime,
            underwayTime = underwayTime,
            offTime = offTime,
            foreDraft = foreDraft,
            aftDraft = aftDraft,
            from_stop_location_id = from_stop_location_id,
            from_stop_location_code = from_stop_location_code,
            from_stop_location = from_stop_location,
            to_stop_location_id = to_stop_location_id,
            to_stop_location_code = to_stop_location_code,
            to_stop_location = to_stop_location,
            movement_agency = movement_agency,
            movement_agency_code = movement_agency_code,
            lineHandler = lineHandler,
            tugName = tugName,
            pilotNo = pilotNo,
            cargo_function = cargo_function,
            product_code = product_code,
        )
    }

    // 1) Test a Movement about a SEA to ANCHOR
    // Expected to get
    // a portcall
    // no visits!
    @Test
    fun Test_a_Movement_about_a_SEA_to_ANCHOR() {
        val exportTime = Date()
        val departureTime = fetchTimestamp(exportTime, 1, TimeUnit.HOURS)
        val arrivalTime = fetchTimestamp(exportTime, 2, TimeUnit.HOURS)
        val movement = getTestingMovement(
            from_stop_location_code = "SEA",
            to_stop_location_code = "ANCH",
            dataExportTime = exportTime,
            scheduledTime = exportTime,
            underwayTime = departureTime,
            offTime = arrivalTime,
        )
        val portcallId = "portcallId"
        whenever(portcallDataSource.generateId(eq(IDPREFIX_USCRP), any(), any())).thenReturn(portcallId)

        val portcall = corpusChristiService.convertToPortcall(movement, movement.vesselIMO)
        assertEquals(departureTime, portcall.startTime)
        assertEquals(departureTime, portcall.portAtaTime)
        assertNull(portcall.portAtdTime)
        assertEquals(PortcallStatus.INBOUND, portcall.status)
        // because visits anchorages are not processed!
        assertEquals(UpdateType.NOMINATION, portcall.startTimeType)
        assertEquals(
            setOfNotNull(movement.visitId, movement.visitNumber),
            portcall.portcallAlias.map { it.alias }.toSet(),
        )

        val portcallVisits = portcall.visits
        assertTrue(portcallVisits.isEmpty())
    }

    // 2) Test a Movement about a SEA to BERTH
    // Expected to get
    // a portcall
    // 1 berth visit!
    @Test
    fun Test_a_Movement_about_a_SEA_to_BERTH() {
        val exportTime = Date()
        val departureTime = fetchTimestamp(exportTime, 1, TimeUnit.HOURS)
        val arrivalTime = fetchTimestamp(exportTime, 2, TimeUnit.HOURS)
        val movement = getTestingMovement(
            from_stop_location_code = "SEA",
            to_stop_location_code = "MyBerth",
            to_stop_location = "My Berth",
            dataExportTime = exportTime,
            scheduledTime = exportTime,
            underwayTime = departureTime,
            offTime = arrivalTime,
        )
        val portcallId = "portcallId"
        whenever(portcallDataSource.generateId(eq(IDPREFIX_USCRP), any(), any())).thenReturn(portcallId)
        whenever(pomaService.getBerths(any(), eq(IDPREFIX_USCRP))).thenReturn(emptyArray<Berth>())
        val portcall = corpusChristiService.convertToPortcall(movement, movement.vesselIMO)
        assertEquals(departureTime, portcall.startTime)
        assertEquals(departureTime, portcall.portAtaTime)
        assertNull(portcall.portAtdTime)
        assertEquals(PortcallStatus.INBOUND, portcall.status)
        assertEquals(UpdateType.BERTH, portcall.startTimeType)
        assertEquals(
            setOfNotNull(movement.visitId, movement.visitNumber),
            portcall.portcallAlias.map { it.alias }.toSet(),
        )

        val portcallVisits = portcall.visits
        assertEquals(1, portcallVisits.size)
        assertEquals("My Berth", portcallVisits.first().berthName)
        assertEquals(arrivalTime, portcallVisits.first().berthAta)
        corpusChristiService.updatePortcall(listOf(portcall))
    }

    // 3) Test a Movement about a ANCHOR to BERTH
    // Expected to get
    // a portcall
    // 1 berth visit!
    @Test
    fun Test_a_Movement_about_a_ANCHOR_to_BERTH() {
        val exportTime = Date()
        val departureTime = fetchTimestamp(exportTime, 1, TimeUnit.HOURS)
        val arrivalTime = fetchTimestamp(exportTime, 2, TimeUnit.HOURS)
        val movement = getTestingMovement(
            from_stop_location_code = "ANCH",
            to_stop_location_code = "MyBerth",
            to_stop_location = "My Berth",
            dataExportTime = exportTime,
            scheduledTime = exportTime,
            underwayTime = departureTime,
            offTime = arrivalTime,
        )
        val portcallId = "portcallId"
        whenever(portcallDataSource.generateId(eq(IDPREFIX_USCRP), any(), any())).thenReturn(portcallId)
        whenever(pomaService.getBerths(any(), eq(IDPREFIX_USCRP))).thenReturn(emptyArray<Berth>())
        val portcall = corpusChristiService.convertToPortcall(movement, movement.vesselIMO)
        assertEquals(departureTime, portcall.startTime)
        assertEquals(departureTime, portcall.portAtaTime)
        assertNull(portcall.portAtdTime)
        assertEquals(PortcallStatus.INBOUND, portcall.status)
        assertEquals(UpdateType.BERTH, portcall.startTimeType)
        assertEquals(
            setOfNotNull(movement.visitId, movement.visitNumber),
            portcall.portcallAlias.map { it.alias }.toSet(),
        )
        val portcallVisits = portcall.visits
        assertEquals(1, portcallVisits.size)
        assertEquals("My Berth", portcallVisits.first().berthName)
        assertEquals(arrivalTime, portcallVisits.first().berthAta)
    }

    // 4) Test a Movement about a ANCHOR to SEA
    // Expected to get
    // a portcall
    // no visits!
    @Test
    fun Test_a_Movement_about_a_ANCHOR_to_SEA() {
        val exportTime = Date()
        val departureTime = fetchTimestamp(exportTime, 1, TimeUnit.HOURS)
        val arrivalTime = fetchTimestamp(exportTime, 2, TimeUnit.HOURS)
        val movement = getTestingMovement(
            from_stop_location_code = "ANCH",
            to_stop_location_code = "SEA",
            jobType = "Departure",
            dataExportTime = exportTime,
            scheduledTime = exportTime,
            underwayTime = departureTime,
            offTime = arrivalTime,
        )
        val portcallId = "portcallId"
        whenever(portcallDataSource.generateId(eq(IDPREFIX_USCRP), any(), any())).thenReturn(portcallId)

        val portcall = corpusChristiService.convertToPortcall(movement, movement.vesselIMO)
        assertEquals(exportTime, portcall.startTime)
        assertNull(portcall.portAtaTime)
        assertEquals(arrivalTime, portcall.portAtdTime)
        assertEquals(PortcallStatus.OUTBOUND, portcall.status)
        // because visits anchorages are not processed!
        assertEquals(UpdateType.NOMINATION, portcall.startTimeType)
        assertEquals(
            setOfNotNull(movement.visitId, movement.visitNumber),
            portcall.portcallAlias.map { it.alias }.toSet(),
        )

        val portcallVisits = portcall.visits
        assertTrue(portcallVisits.isEmpty())
    }

    // 5) Test a Movement about a BERTH to DIFFERENT BERTH
    // Expected to get
    // a portcall
    // 2 visits! one per each berth
    @Test
    fun Test_a_Movement_about_a_BERTH_to_different_BERTH() {
        val exportTime = Date()
        val departureTime = fetchTimestamp(exportTime, 1, TimeUnit.HOURS)
        val arrivalTime = fetchTimestamp(exportTime, 2, TimeUnit.HOURS)
        val movement = getTestingMovement(
            from_stop_location_code = "MyBerth 1",
            from_stop_location = "My Berth 1",
            to_stop_location_code = "MyBerth 2",
            to_stop_location = "My Berth 2",
            dataExportTime = exportTime,
            scheduledTime = exportTime,
            underwayTime = departureTime,
            offTime = arrivalTime,
        )
        val portcallId = "portcallId"
        whenever(portcallDataSource.generateId(eq(IDPREFIX_USCRP), any(), any())).thenReturn(portcallId)
        whenever(pomaService.getBerths(any(), eq(IDPREFIX_USCRP))).thenReturn(emptyArray<Berth>())
        val portcall = corpusChristiService.convertToPortcall(movement, movement.vesselIMO)
        assertEquals(departureTime, portcall.startTime)
        assertEquals(departureTime, portcall.portAtaTime)
        assertNull(portcall.portAtdTime)
        assertEquals(PortcallStatus.INBOUND, portcall.status)
        assertEquals(UpdateType.BERTH, portcall.startTimeType)
        assertEquals(
            setOfNotNull(movement.visitId, movement.visitNumber),
            portcall.portcallAlias.map { it.alias }.toSet(),
        )

        val portcallVisits = portcall.visits
        assertEquals(2, portcallVisits.size)
        val visit1 = portcallVisits[0]
        assertEquals("My Berth 1", visit1.berthName)
        assertEquals(departureTime, visit1.berthAtd)

        val visit2 = portcallVisits[1]
        assertEquals("My Berth 2", visit2.berthName)
        assertEquals(arrivalTime, visit2.berthAta)
    }

    // 6) Test a Movement about a BERTH to SAME BERTH
    // Expected to get an exception as it's ship rotation in the berth.
    @Test
    fun Test_a_Movement_about_a_BERTH_to_SAME_BERTH() {
        val exportTime = Date()
        val departureTime = fetchTimestamp(exportTime, 1, TimeUnit.HOURS)
        val arrivalTime = fetchTimestamp(exportTime, 2, TimeUnit.HOURS)
        val movement = getTestingMovement(
            from_stop_location_code = "MyBerth 1",
            from_stop_location = "My Berth 1",
            to_stop_location_code = "MyBerth 1",
            to_stop_location = "My Berth 1",
            dataExportTime = exportTime,
            scheduledTime = exportTime,
            underwayTime = departureTime,
            offTime = arrivalTime,
        )
        assertThrows<PreconditionException> {
            corpusChristiService.convertToPortcall(movement, movement.vesselIMO)
        }
    }

    // 6) Test a Movement about a BERTH to ANCHOR
    // Expected to get
    // a portcall
    // 1 visit!
    @Test
    fun Test_a_Movement_about_a_BERTH_to_ANCHOR() {
        val exportTime = Date()
        val departureTime = fetchTimestamp(exportTime, 1, TimeUnit.HOURS)
        val arrivalTime = fetchTimestamp(exportTime, 2, TimeUnit.HOURS)
        val movement = getTestingMovement(
            from_stop_location_code = "MyBerth",
            from_stop_location = "My Berth",
            to_stop_location_code = "ANCH",
            dataExportTime = exportTime,
            scheduledTime = exportTime,
            underwayTime = departureTime,
            offTime = arrivalTime,
        )
        val portcallId = "portcallId"
        whenever(portcallDataSource.generateId(eq(IDPREFIX_USCRP), any(), any())).thenReturn(portcallId)
        whenever(pomaService.getBerths(any(), eq(IDPREFIX_USCRP))).thenReturn(emptyArray<Berth>())
        val portcall = corpusChristiService.convertToPortcall(movement, movement.vesselIMO)
        assertEquals(departureTime, portcall.startTime)
        assertEquals(departureTime, portcall.portAtaTime)
        assertNull(portcall.portAtdTime)
        assertEquals(PortcallStatus.INBOUND, portcall.status)
        assertEquals(UpdateType.BERTH, portcall.startTimeType)
        assertEquals(
            setOfNotNull(movement.visitId, movement.visitNumber),
            portcall.portcallAlias.map { it.alias }.toSet(),
        )
        val portcallVisits = portcall.visits
        assertEquals(1, portcallVisits.size)
        assertEquals("My Berth", portcallVisits.first().berthName)
        assertEquals(exportTime, portcallVisits.first().berthEtd)
        assertEquals(departureTime, portcallVisits.first().berthAtd)
    }

    // 7) Test a Movement about a BERTH to SEA
    // Expected to get
    // a portcall
    // no visits!
    @Test
    fun Test_a_Movement_about_a_BERTH_to_SEA() {
        val exportTime = Date()
        val departureTime = fetchTimestamp(exportTime, 1, TimeUnit.HOURS)
        val arrivalTime = fetchTimestamp(exportTime, 2, TimeUnit.HOURS)
        val movement = getTestingMovement(
            from_stop_location_code = "MyBerth",
            from_stop_location = "My Berth",
            to_stop_location_code = "SEA",
            jobType = "Departure",
            dataExportTime = exportTime,
            scheduledTime = exportTime,
            underwayTime = departureTime,
            offTime = arrivalTime,
        )
        val portcallId = "portcallId"
        whenever(portcallDataSource.generateId(eq(IDPREFIX_USCRP), any(), any())).thenReturn(portcallId)
        whenever(pomaService.getBerths(any(), eq(IDPREFIX_USCRP))).thenReturn(emptyArray<Berth>())
        val portcall = corpusChristiService.convertToPortcall(movement, movement.vesselIMO)
        assertEquals(exportTime, portcall.startTime)
        assertNull(portcall.portAtaTime)
        assertEquals(arrivalTime, portcall.portAtdTime)
        assertEquals(PortcallStatus.OUTBOUND, portcall.status)
        assertEquals(UpdateType.BERTH, portcall.startTimeType)
        assertEquals(
            setOfNotNull(movement.visitId, movement.visitNumber),
            portcall.portcallAlias.map { it.alias }.toSet(),
        )
        val portcallVisits = portcall.visits
        assertEquals(1, portcallVisits.size)
        assertEquals("My Berth", portcallVisits.first().berthName)
        assertEquals(exportTime, portcallVisits.first().berthEtd)
        assertEquals(departureTime, portcallVisits.first().berthAtd)
    }

    // Here, a SEA->Berth movement with all times populated should send three events:
    // - A PortcallPlusAgentChangedEvent (before it was null)
    // - A berth PortcallPlusAtaEvent
    // - A PortcallPlusVisitsUpdateEvent event with a visit with the right times!
    @Test
    fun Test_a_Movement_about_a_SEA_to_BERTH_with_all_times_events_triggered_for_new_portcall() {
        val exportTime = Date()
        val departureTime = fetchTimestamp(exportTime, 1, TimeUnit.HOURS)
        val arrivalTime = fetchTimestamp(exportTime, 2, TimeUnit.HOURS)
        val movement = getTestingMovement(
            from_stop_location_code = "SEA",
            to_stop_location_code = "MyBerth",
            to_stop_location = "My Berth",
            dataExportTime = exportTime,
            scheduledTime = exportTime,
            underwayTime = departureTime,
            offTime = arrivalTime,
        )
        val portcallId = "portcallId"
        whenever(portcallDataSource.generateId(eq(IDPREFIX_USCRP), any(), any())).thenReturn(portcallId)
        whenever(pomaService.getBerths(any(), eq(IDPREFIX_USCRP))).thenReturn(emptyArray<Berth>())
        var capturedEvents: List<PortcallPlusEvent>? = null
        doAnswer { invocation ->
            // this is the actual manual capture!
            capturedEvents = invocation.callRealMethod() as List<PortcallPlusEvent>
            capturedEvents
        }.whenever(eventService).createEventsForNewPortcall(any<Portcall>())

        // this is the two methods in combination that we need to test
        val portcall = corpusChristiService.convertToPortcall(movement, movement.vesselIMO)
        corpusChristiService.updatePortcall(listOf(portcall))

        // Now the assertions
        assertEquals(departureTime, portcall.startTime)
        assertEquals(departureTime, portcall.portAtaTime)
        assertNull(portcall.portAtdTime)
        assertEquals(PortcallStatus.INBOUND, portcall.status)
        assertEquals(UpdateType.BERTH, portcall.startTimeType)
        assertEquals(
            setOfNotNull(movement.visitId, movement.visitNumber),
            portcall.portcallAlias.map { it.alias }.toSet(),
        )

        val portcallVisits = portcall.visits
        assertEquals(1, portcallVisits.size)
        assertEquals("My Berth", portcallVisits.first().berthName)
        assertEquals(arrivalTime, portcallVisits.first().berthAta)
        verify(eventService, times(1)).onNewPortcall(any())
        assertEquals(4, capturedEvents?.size)
        val agentChangedEvent = capturedEvents?.firstNotNullOfOrNull { it as? PortcallPlusAgentChangedEvent }
        assertNotNull(agentChangedEvent)
        assertEquals("A random agency", agentChangedEvent?.vesselAgent)
        val berthAtaEvent = capturedEvents?.firstNotNullOfOrNull { it as? PortcallPlusAtaEvent }
        assertNotNull(berthAtaEvent)
        assertEquals(AreaIdentifier.AreaType.BERTH, berthAtaEvent?.area?.type)
        assertEquals("My Berth", berthAtaEvent?.area?.name)
        assertEquals(arrivalTime.toInstant(), berthAtaEvent?.actualTime)
        assertEquals(IDPREFIX_USCRP, berthAtaEvent?.area?.unlocode)
        val visitsUpdateEvent = capturedEvents?.firstNotNullOfOrNull { it as? PortcallPlusVisitsUpdateEvent }
        assertNotNull(visitsUpdateEvent)
        assertEquals(AreaIdentifier.AreaType.PORT, visitsUpdateEvent?.port?.type)
        assertEquals(IDPREFIX_USCRP, visitsUpdateEvent?.port?.unlocode)
        assertEquals(1, visitsUpdateEvent?.visits?.size)
        val visit = visitsUpdateEvent?.visits?.firstOrNull()
        assertEquals(arrivalTime.toInstant(), visit?.berthAta)
        val portEtaEvent = capturedEvents?.firstNotNullOfOrNull { it as? PortcallPlusEtaEvent }
        assertNotNull(portEtaEvent)
        assertEquals(AreaIdentifier.AreaType.PORT, portEtaEvent?.area?.type)
        assertEquals(IDPREFIX_USCRP, portEtaEvent?.area?.name)
        assertEquals(departureTime.toInstant(), portEtaEvent?.predictedTime)
        assertEquals(IDPREFIX_USCRP, portEtaEvent?.area?.unlocode)
    }

    // Here, a SEA->Berth movement with all times populated should send two events:
    // - NO PortcallPlusAgentChangedEvent (They are the same)
    // - A berth PortcallPlusAtaEvent
    // - A PortcallPlusVisitsUpdateEvent event with a visit with the right times!
    @Test
    fun Test_a_Movement_about_a_SEA_to_BERTH_with_all_times_events_triggered_for_existing_portcall() {
        val exportTime = Date()
        val departureTime = fetchTimestamp(exportTime, 1, TimeUnit.HOURS)
        val arrivalTime = fetchTimestamp(exportTime, 2, TimeUnit.HOURS)
        val movement = getTestingMovement(
            from_stop_location_code = "SEA",
            to_stop_location_code = "MyBerth",
            to_stop_location = "My Berth",
            dataExportTime = exportTime,
            scheduledTime = exportTime,
            underwayTime = departureTime,
            offTime = arrivalTime,
        )
        val portcallId = "portcallId"
        whenever(portcallDataSource.generateId(eq(IDPREFIX_USCRP), any(), any())).thenReturn(portcallId)
        whenever(pomaService.getBerths(any(), eq(IDPREFIX_USCRP))).thenReturn(emptyArray<Berth>())
        var capturedEvents: List<PortcallPlusEvent>? = null
        doAnswer { invocation ->
            // this is the actual manual capture!
            capturedEvents = invocation.callRealMethod() as List<PortcallPlusEvent>
            capturedEvents
        }.whenever(eventService).createEventsForUpdatePortcall(any<Portcall>(), any<Portcall>())

        val previousVisitTime = fetchTimestamp(exportTime, -1, TimeUnit.DAYS)
        val existingPortcall = Portcall(
            portcallId = portcallId,
            portcallAlias = setOfNotNull(
                PortcallAlias(PortcallAliasName.CORPUS_CHRISTI, movement.visitNumber),
                movement.visitId?.let { PortcallAlias(PortcallAliasName.CORPUS_CHRISTI, it) },
            ),
            port = IDPREFIX_USCRP,
            imo = movement.vesselIMO,
            source = CORPUS_CHRISTI,
            // previous data had them 1 day later!
            portAtaTime = previousVisitTime,
            startTime = previousVisitTime,
            visits = listOf(
                PortcallVisit(
                    uniqueBerthId = "A previous Portcall visit",
                    berthName = "A previous berth visit name",
                    berthAta = previousVisitTime,
                ),
                PortcallVisit(
                    uniqueBerthId = movement.to_stop_location,
                    berthName = movement.to_stop_location,
                ),
            ),
        )
        val existingPortcallOneList = listOf(existingPortcall)
        whenever(portcallDataSource.searchByAlias(any(), eq(PortcallAliasName.CORPUS_CHRISTI))).thenReturn(existingPortcallOneList)
        whenever(portcallDataSource.get(any())).thenReturn(existingPortcallOneList)
        // this is the two methods in combination that we need to test
        val portcall = corpusChristiService.convertToPortcall(movement, movement.vesselIMO)
        corpusChristiService.updatePortcall(listOf(portcall))

        // Now the assertions
        arrivalTime
        // no changes in the next portcall startime and portAtaTime
        assertEquals(existingPortcall.startTime, portcall.startTime)
        assertEquals(existingPortcall.portAtaTime, portcall.portAtaTime)
        assertNull(portcall.portAtdTime)
        assertEquals(PortcallStatus.INBOUND, portcall.status)
        assertEquals(UpdateType.BERTH, portcall.startTimeType)
        assertEquals(
            setOfNotNull(movement.visitId, movement.visitNumber),
            portcall.portcallAlias.map { it.alias }.toSet(),
        )
        val portcallVisits = portcall.visits
        assertEquals(2, portcallVisits.size)
        val updatedVisit = portcallVisits[1]
        assertEquals("My Berth", updatedVisit.berthName)
        assertEquals(arrivalTime, updatedVisit.berthAta)
        verify(eventService, times(1)).onUpdatedPortcall(any(), any())
        assertEquals(2, capturedEvents?.size)
        val agentChangedEvent = capturedEvents?.firstNotNullOfOrNull { it as? PortcallPlusAgentChangedEvent }
        assertNotNull(agentChangedEvent)
        assertEquals("A random agency", agentChangedEvent?.vesselAgent)
        val visitsUpdateEvent = capturedEvents?.firstNotNullOfOrNull { it as? PortcallPlusVisitsUpdateEvent }
        assertNotNull(visitsUpdateEvent)
        assertEquals(AreaIdentifier.AreaType.PORT, visitsUpdateEvent?.port?.type)
        assertEquals(IDPREFIX_USCRP, visitsUpdateEvent?.port?.unlocode)
        assertEquals(2, visitsUpdateEvent?.visits?.size)
        val updatedVisitFromEvent = visitsUpdateEvent?.visits?.get(1)
        assertEquals(arrivalTime.toInstant(), updatedVisitFromEvent?.berthAta)
        // todo: Not sure if this should happen (portEtaEvent!=null) in this scenario. If it shouldn't, it should be fixed but it's out of the scope (PRP-2616)
        // luckily, the only scenario where it can happen is resetting the portcall startTime (when startTime gets a previous visit not computed til now.
        // See method Portcall.getUpdatedPortcall(...) (in SourceService.kt:221) and getStartTime(...) (in PortcallVisitUtils.kt:6)
        val portEtaEvent = capturedEvents?.firstNotNullOfOrNull { it as? PortcallPlusEtaEvent }
        assertNull(portEtaEvent)
    }

    // Here, a SEA->Berth movement with all times populated should send two events:
    // - NO PortcallPlusAgentChangedEvent (They are the same)
    // - A berth PortcallPlusAtaEvent
    // - A PortcallPlusVisitsUpdateEvent event with a visit with the right times!
    @Test
    fun Test_a_Movement_about_a_SEA_to_BERTH_with_all_times_events_triggered_for_existing_portcall_correct_portcall_ATA_and_startTime() {
        val exportTime = Date()
        val departureTime = fetchTimestamp(exportTime, 1, TimeUnit.HOURS)
        val arrivalTime = fetchTimestamp(exportTime, 2, TimeUnit.HOURS)
        val movement = getTestingMovement(
            from_stop_location_code = "SEA",
            to_stop_location_code = "MyBerth",
            to_stop_location = "My Berth",
            dataExportTime = exportTime,
            scheduledTime = exportTime,
            underwayTime = departureTime,
            offTime = arrivalTime,
        )
        val portcallId = "portcallId"
        whenever(portcallDataSource.generateId(eq(IDPREFIX_USCRP), any(), any())).thenReturn(portcallId)
        whenever(pomaService.getBerths(any(), eq(IDPREFIX_USCRP))).thenReturn(emptyArray<Berth>())
        var capturedEvents: List<PortcallPlusEvent>? = null
        doAnswer { invocation ->
            // this is the actual manual capture!
            capturedEvents = invocation.callRealMethod() as List<PortcallPlusEvent>
            capturedEvents
        }.whenever(eventService).createEventsForUpdatePortcall(any<Portcall>(), any<Portcall>())

        val existingPortcallOneList = listOf(
            Portcall(
                portcallId = portcallId,
                portcallAlias = setOfNotNull(
                    PortcallAlias(PortcallAliasName.CORPUS_CHRISTI, movement.visitNumber),
                    movement.visitId?.let { PortcallAlias(PortcallAliasName.CORPUS_CHRISTI, it) },
                ),
                port = IDPREFIX_USCRP,
                imo = movement.vesselIMO,
                source = CORPUS_CHRISTI,
                // previous data had them 1 day later!
                portAtaTime = fetchTimestamp(exportTime, 1, TimeUnit.DAYS),
                startTime = fetchTimestamp(exportTime, 1, TimeUnit.DAYS),
                visits = listOf(
                    PortcallVisit(
                        uniqueBerthId = movement.to_stop_location,
                        berthName = movement.to_stop_location,
                    ),
                ),
            ),
        )
        whenever(portcallDataSource.searchByAlias(any(), eq(PortcallAliasName.CORPUS_CHRISTI))).thenReturn(existingPortcallOneList)
        whenever(portcallDataSource.get(any())).thenReturn(existingPortcallOneList)
        // this is the two methods in combination that we need to test
        val portcall = corpusChristiService.convertToPortcall(movement, movement.vesselIMO)
        corpusChristiService.updatePortcall(listOf(portcall))

        // Now the assertions
        assertEquals(departureTime, portcall.startTime)
        assertEquals(departureTime, portcall.portAtaTime)
        assertNull(portcall.portAtdTime)
        assertEquals(PortcallStatus.INBOUND, portcall.status)
        assertEquals(UpdateType.BERTH, portcall.startTimeType)
        assertEquals(
            setOfNotNull(movement.visitId, movement.visitNumber),
            portcall.portcallAlias.map { it.alias }.toSet(),
        )
        val portcallVisits = portcall.visits
        assertEquals(1, portcallVisits.size)
        assertEquals("My Berth", portcallVisits.first().berthName)
        assertEquals(arrivalTime, portcallVisits.first().berthAta)
        verify(eventService, times(1)).onUpdatedPortcall(any(), any())
        assertEquals(3, capturedEvents?.size)
        val agentChangedEvent = capturedEvents?.firstNotNullOfOrNull { it as? PortcallPlusAgentChangedEvent }
        assertNotNull(agentChangedEvent)
        assertEquals("A random agency", agentChangedEvent?.vesselAgent)
        val visitsUpdateEvent = capturedEvents?.firstNotNullOfOrNull { it as? PortcallPlusVisitsUpdateEvent }
        assertNotNull(visitsUpdateEvent)
        assertEquals(AreaIdentifier.AreaType.PORT, visitsUpdateEvent?.port?.type)
        assertEquals(IDPREFIX_USCRP, visitsUpdateEvent?.port?.unlocode)
        assertEquals(1, visitsUpdateEvent?.visits?.size)
        val visit = visitsUpdateEvent?.visits?.firstOrNull()
        assertEquals(arrivalTime.toInstant(), visit?.berthAta)
        val portEtaEvent = capturedEvents?.firstNotNullOfOrNull { it as? PortcallPlusEtaEvent }
        assertNotNull(portEtaEvent)
        assertEquals(AreaIdentifier.AreaType.PORT, portEtaEvent?.area?.type)
        assertEquals(IDPREFIX_USCRP, portEtaEvent?.area?.name)
        assertEquals(departureTime.toInstant(), portEtaEvent?.predictedTime)
        assertEquals(IDPREFIX_USCRP, portEtaEvent?.area?.unlocode)
    }

    // Here, a SEA->Berth movement with all times populated should send two events:
    // - A PortcallPlusAgentChangedEvent (before it was null)
    // - no berth PortcallPlusAtaEvent as no offTime (currently! because the PC+ feature isn't created yet)
    // - A PortcallPlusVisitsUpdateEvent event with a visit with no times!
    @Test
    fun Test_a_Movement_about_a_SEA_to_BERTH_without_offTime_events_triggered_for_new_portcall() {
        val exportTime = Date()
        val departureTime = fetchTimestamp(exportTime, 1, TimeUnit.HOURS)
        val movement = getTestingMovement(
            from_stop_location_code = "SEA",
            to_stop_location_code = "MyBerth",
            to_stop_location = "My Berth",
            dataExportTime = exportTime,
            scheduledTime = exportTime,
            underwayTime = departureTime,
            offTime = null,
        )
        val portcallId = "portcallId"
        whenever(portcallDataSource.generateId(eq(IDPREFIX_USCRP), any(), any())).thenReturn(portcallId)
        whenever(pomaService.getBerths(any(), eq(IDPREFIX_USCRP))).thenReturn(emptyArray<Berth>())
        var capturedEvents: List<PortcallPlusEvent>? = null
        doAnswer { invocation ->
            // this is the actual manual capture!
            capturedEvents = invocation.callRealMethod() as List<PortcallPlusEvent>
            capturedEvents
        }.whenever(eventService).createEventsForNewPortcall(any<Portcall>())

        // this is the two methods in combination that we need to test
        val portcall = corpusChristiService.convertToPortcall(movement, movement.vesselIMO)
        corpusChristiService.updatePortcall(listOf(portcall))

        // Now the assertions
        assertEquals(departureTime, portcall.startTime)
        assertEquals(departureTime, portcall.portAtaTime)
        assertNull(portcall.portAtdTime)
        assertEquals(PortcallStatus.INBOUND, portcall.status)
        assertEquals(UpdateType.BERTH, portcall.startTimeType)
        assertEquals(
            setOfNotNull(movement.visitId, movement.visitNumber),
            portcall.portcallAlias.map { it.alias }.toSet(),
        )

        val portcallVisits = portcall.visits
        assertEquals(1, portcallVisits.size)
        assertEquals("My Berth", portcallVisits.first().berthName)
        assertNull(portcallVisits.first().berthAta)
        verify(eventService, times(1)).onNewPortcall(any())
        assertEquals(3, capturedEvents?.size)
        val agentChangedEvent = capturedEvents?.firstNotNullOfOrNull { it as? PortcallPlusAgentChangedEvent }
        assertNotNull(agentChangedEvent)
        assertEquals("A random agency", agentChangedEvent?.vesselAgent)
        val portAtaEvent = capturedEvents?.firstNotNullOfOrNull { it as? PortcallPlusAtaEvent }
        assertNull(portAtaEvent)
        val visitsUpdateEvent = capturedEvents?.firstNotNullOfOrNull { it as? PortcallPlusVisitsUpdateEvent }
        assertNotNull(visitsUpdateEvent)
        assertEquals(AreaIdentifier.AreaType.PORT, visitsUpdateEvent?.port?.type)
        assertEquals(IDPREFIX_USCRP, visitsUpdateEvent?.port?.unlocode)
        assertEquals(1, visitsUpdateEvent?.visits?.size)
        val visit = visitsUpdateEvent?.visits?.firstOrNull()
        assertNull(visit?.berthAta)
        val portEtaEvent = capturedEvents?.firstNotNullOfOrNull { it as? PortcallPlusEtaEvent }
        assertNotNull(portEtaEvent)
        assertEquals(AreaIdentifier.AreaType.PORT, portEtaEvent?.area?.type)
        assertEquals(IDPREFIX_USCRP, portEtaEvent?.area?.name)
        assertEquals(departureTime.toInstant(), portEtaEvent?.predictedTime)
        assertEquals(IDPREFIX_USCRP, portEtaEvent?.area?.unlocode)
    }

    // Here, a SEA->Berth movement with all times populated should send two events:
    // - A PortcallPlusAgentChangedEvent (before it was null)
    // - no berth PortcallPlusAtaEvent as no offTime (currently! because the PC+ feature isn't created yet)
    // - A PortcallPlusVisitsUpdateEvent event with a visit with no times!
    @Test
    fun Test_a_Movement_about_a_SEA_to_BERTH_without_offTime_events_triggered_for_existing_portcall() {
        val exportTime = Date()
        val departureTime = fetchTimestamp(exportTime, 1, TimeUnit.HOURS)
        val arrivalTime = null
        val movement = getTestingMovement(
            from_stop_location_code = "SEA",
            to_stop_location_code = "MyBerth",
            to_stop_location = "My Berth",
            dataExportTime = exportTime,
            scheduledTime = exportTime,
            underwayTime = departureTime,
            offTime = arrivalTime,
        )
        val portcallId = "portcallId"
        whenever(portcallDataSource.generateId(eq(IDPREFIX_USCRP), any(), any())).thenReturn(portcallId)
        whenever(pomaService.getBerths(any(), eq(IDPREFIX_USCRP))).thenReturn(emptyArray<Berth>())
        var capturedEvents: List<PortcallPlusEvent>? = null
        doAnswer { invocation ->
            // this is the actual manual capture!
            capturedEvents = invocation.callRealMethod() as List<PortcallPlusEvent>
            capturedEvents
        }.whenever(eventService).createEventsForUpdatePortcall(any<Portcall>(), any<Portcall>())

        val existingPortcall = Portcall(
            portcallId = portcallId,
            portcallAlias = setOfNotNull(
                PortcallAlias(PortcallAliasName.CORPUS_CHRISTI, movement.visitNumber),
                movement.visitId?.let { PortcallAlias(PortcallAliasName.CORPUS_CHRISTI, it) },
            ),
            port = IDPREFIX_USCRP,
            imo = movement.vesselIMO,
            source = CORPUS_CHRISTI,
            // previous data had them 1 day later!
            portAtaTime = fetchTimestamp(exportTime, 1, TimeUnit.DAYS),
            startTime = fetchTimestamp(exportTime, 1, TimeUnit.DAYS),
            visits = listOf(
                PortcallVisit(
                    uniqueBerthId = movement.to_stop_location,
                    berthName = movement.to_stop_location,
                ),
            ),
        )
        val existingPortcallOneList = listOf(existingPortcall)
        whenever(portcallDataSource.searchByAlias(any(), eq(PortcallAliasName.CORPUS_CHRISTI))).thenReturn(existingPortcallOneList)
        whenever(portcallDataSource.get(any())).thenReturn(existingPortcallOneList)

        // this is the two methods in combination that we need to test
        val portcall = corpusChristiService.convertToPortcall(movement, movement.vesselIMO)
        corpusChristiService.updatePortcall(listOf(portcall))

        // Now the assertions
        // no change!
        assertEquals(departureTime, portcall.startTime)
        assertEquals(departureTime, portcall.portAtaTime)
        assertNull(portcall.portAtdTime)
        assertEquals(PortcallStatus.INBOUND, portcall.status)
        assertEquals(UpdateType.BERTH, portcall.startTimeType)
        assertEquals(
            setOfNotNull(movement.visitId, movement.visitNumber),
            portcall.portcallAlias.map { it.alias }.toSet(),
        )

        val portcallVisits = portcall.visits
        assertEquals(1, portcallVisits.size)
        assertEquals("My Berth", portcallVisits.first().berthName)
        assertEquals(arrivalTime, portcallVisits.first().berthAta)
        verify(eventService, times(1)).onUpdatedPortcall(any(), any())
        assertEquals(3, capturedEvents?.size)
        val agentChangedEvent = capturedEvents?.firstNotNullOfOrNull { it as? PortcallPlusAgentChangedEvent }
        assertNotNull(agentChangedEvent)
        assertEquals("A random agency", agentChangedEvent?.vesselAgent)
        val portAtaEvent = capturedEvents?.firstNotNullOfOrNull { it as? PortcallPlusAtaEvent }
        assertNull(portAtaEvent)
        val visitsUpdateEvent = capturedEvents?.firstNotNullOfOrNull { it as? PortcallPlusVisitsUpdateEvent }
        assertNotNull(visitsUpdateEvent)
        assertEquals(AreaIdentifier.AreaType.PORT, visitsUpdateEvent?.port?.type)
        assertEquals(IDPREFIX_USCRP, visitsUpdateEvent?.port?.unlocode)
        assertEquals(1, visitsUpdateEvent?.visits?.size)
        val visit = visitsUpdateEvent?.visits?.firstOrNull()
        assertNull(visit?.berthAta)
        val portEtaEvent = capturedEvents?.firstNotNullOfOrNull { it as? PortcallPlusEtaEvent }
        assertEquals(AreaIdentifier.AreaType.PORT, portEtaEvent?.area?.type)
        assertEquals(IDPREFIX_USCRP, portEtaEvent?.area?.name)
        assertEquals(departureTime.toInstant(), portEtaEvent?.predictedTime)
        assertEquals(IDPREFIX_USCRP, portEtaEvent?.area?.unlocode)
    }

    @Test
    fun Test_a_Movement_about_a_SEA_to_BERTH_with_all_times_events_triggered_for_existing_portcall_startTimeChanged() {
        val currentTime = Date()
        val departureTime = fetchTimestamp(currentTime, 1, TimeUnit.HOURS)
        val arrivalTime = fetchTimestamp(currentTime, 2, TimeUnit.HOURS)
        val movement = getTestingMovement(
            from_stop_location_code = "SEA",
            to_stop_location_code = "MyBerth",
            to_stop_location = "My Berth",
            dataExportTime = currentTime,
            scheduledTime = currentTime,
            underwayTime = departureTime,
            offTime = arrivalTime,
        )
        val portcallId = "portcallId"
        whenever(portcallDataSource.generateId(eq(IDPREFIX_USCRP), any(), any())).thenReturn(portcallId)
        whenever(pomaService.getBerths(any(), eq(IDPREFIX_USCRP))).thenReturn(emptyArray<Berth>())
        var capturedEvents: List<PortcallPlusEvent>? = null
        doAnswer { invocation ->
            // this is the actual manual capture!
            capturedEvents = invocation.callRealMethod() as List<PortcallPlusEvent>
            capturedEvents
        }.whenever(eventService).createEventsForUpdatePortcall(any<Portcall>(), any<Portcall>())

        val previousVisitTime = fetchTimestamp(currentTime, 1, TimeUnit.DAYS)
        val existingPortcall = Portcall(
            portcallId = portcallId,
            portcallAlias = setOfNotNull(
                PortcallAlias(PortcallAliasName.CORPUS_CHRISTI, movement.visitNumber),
                movement.visitId?.let { PortcallAlias(PortcallAliasName.CORPUS_CHRISTI, it) },
            ),
            port = IDPREFIX_USCRP,
            imo = movement.vesselIMO,
            source = CORPUS_CHRISTI,
            // previous data had them 1 day later!
            portAtaTime = previousVisitTime,
            startTime = previousVisitTime,
            visits = listOf(
                PortcallVisit(
                    uniqueBerthId = "A previous Portcall visit",
                    berthName = "A previous visit berth name",
                    berthAta = previousVisitTime,
                ),
                PortcallVisit(
                    uniqueBerthId = movement.to_stop_location,
                    berthName = movement.to_stop_location,
                ),
            ),
        )
        val existingPortcallOneList = listOf(existingPortcall)
        whenever(portcallDataSource.searchByAlias(any(), eq(PortcallAliasName.CORPUS_CHRISTI))).thenReturn(existingPortcallOneList)
        whenever(portcallDataSource.get(any())).thenReturn(existingPortcallOneList)
        // this is the two methods in combination that we need to test
        val portcall = corpusChristiService.convertToPortcall(movement, movement.vesselIMO)
        corpusChristiService.updatePortcall(listOf(portcall))

        // Now the assertions
        arrivalTime
        // the new portcall startime has changed

        assertEquals(departureTime, portcall.startTime)
        assertEquals(departureTime, portcall.portAtaTime)
        assertNull(portcall.portAtdTime)
        assertEquals(PortcallStatus.INBOUND, portcall.status)
        assertEquals(UpdateType.BERTH, portcall.startTimeType)
        assertEquals(
            setOfNotNull(movement.visitId, movement.visitNumber),
            portcall.portcallAlias.map { it.alias }.toSet(),
        )
        val portcallVisits = portcall.visits
        assertEquals(2, portcallVisits.size)
        val addedVisit = portcallVisits[0]
        assertEquals("My Berth", addedVisit.berthName)
        assertEquals(arrivalTime, addedVisit.berthAta)
        verify(eventService, times(1)).onUpdatedPortcall(any(), any())
        assertEquals(3, capturedEvents?.size)
        val agentChangedEvent = capturedEvents?.firstNotNullOfOrNull { it as? PortcallPlusAgentChangedEvent }
        assertNotNull(agentChangedEvent)
        assertEquals("A random agency", agentChangedEvent?.vesselAgent)
        val visitsUpdateEvent = capturedEvents?.firstNotNullOfOrNull { it as? PortcallPlusVisitsUpdateEvent }
        assertNotNull(visitsUpdateEvent)
        assertEquals(AreaIdentifier.AreaType.PORT, visitsUpdateEvent?.port?.type)
        assertEquals(IDPREFIX_USCRP, visitsUpdateEvent?.port?.unlocode)
        assertEquals(2, visitsUpdateEvent?.visits?.size)
        val addedVisitFromEvent = visitsUpdateEvent?.visits?.get(0)
        assertEquals(arrivalTime.toInstant(), addedVisitFromEvent?.berthAta)
        val portEtaEvent = capturedEvents?.firstNotNullOfOrNull { it as? PortcallPlusEtaEvent }
        assertNotNull(portEtaEvent)
        assertEquals(AreaIdentifier.AreaType.PORT, portEtaEvent?.area?.type)
        assertEquals(IDPREFIX_USCRP, portEtaEvent?.area?.name)
        assertEquals(departureTime.toInstant(), portEtaEvent?.predictedTime)
        assertEquals(IDPREFIX_USCRP, portEtaEvent?.area?.unlocode)
    }

    @Test
    fun Test_a_Movement_about_a_SEA_to_BERTH_with_all_times_events_triggered_for_existing_portcall_startTimeDidntChange() {
        val currentTime = Date()
        val departureTime = fetchTimestamp(currentTime, 1, TimeUnit.HOURS)
        val arrivalTime = fetchTimestamp(currentTime, 2, TimeUnit.HOURS)
        val movement = getTestingMovement(
            from_stop_location_code = "SEA",
            to_stop_location_code = "MyBerth",
            to_stop_location = "My Berth",
            dataExportTime = currentTime,
            scheduledTime = currentTime,
            underwayTime = departureTime,
            offTime = arrivalTime,
        )
        val portcallId = "portcallId"
        whenever(portcallDataSource.generateId(eq(IDPREFIX_USCRP), any(), any())).thenReturn(portcallId)
        whenever(pomaService.getBerths(any(), eq(IDPREFIX_USCRP))).thenReturn(emptyArray<Berth>())
        var capturedEvents: List<PortcallPlusEvent>? = null
        doAnswer { invocation ->
            // this is the actual manual capture!
            capturedEvents = invocation.callRealMethod() as List<PortcallPlusEvent>
            capturedEvents
        }.whenever(eventService).createEventsForUpdatePortcall(any<Portcall>(), any<Portcall>())

        val previousVisitTime = fetchTimestamp(currentTime, -1, TimeUnit.DAYS)
        val existingPortcall = Portcall(
            portcallId = portcallId,
            portcallAlias = setOfNotNull(
                PortcallAlias(PortcallAliasName.CORPUS_CHRISTI, movement.visitNumber),
                movement.visitId?.let { PortcallAlias(PortcallAliasName.CORPUS_CHRISTI, it) },
            ),
            port = IDPREFIX_USCRP,
            imo = movement.vesselIMO,
            source = CORPUS_CHRISTI,
            // previous data had them 1 day later!
            portAtaTime = previousVisitTime,
            startTime = previousVisitTime,
            visits = listOf(
                PortcallVisit(
                    uniqueBerthId = "A previous Portcall visit",
                    berthName = "A previous visit berth name",
                    berthAta = previousVisitTime,
                ),
                PortcallVisit(
                    uniqueBerthId = movement.to_stop_location,
                    berthName = movement.to_stop_location,
                ),
            ),
        )
        val existingPortcallOneList = listOf(existingPortcall)
        whenever(portcallDataSource.searchByAlias(any(), eq(PortcallAliasName.CORPUS_CHRISTI))).thenReturn(existingPortcallOneList)
        whenever(portcallDataSource.get(any())).thenReturn(existingPortcallOneList)
        // this is the two methods in combination that we need to test
        val portcall = corpusChristiService.convertToPortcall(movement, movement.vesselIMO)
        corpusChristiService.updatePortcall(listOf(portcall))

        assertEquals(existingPortcall.startTime, portcall.startTime)
        assertEquals(existingPortcall.portAtaTime, portcall.portAtaTime)
        assertNull(portcall.portAtdTime)
        assertEquals(PortcallStatus.INBOUND, portcall.status)
        assertEquals(UpdateType.BERTH, portcall.startTimeType)
        assertEquals(
            setOfNotNull(movement.visitId, movement.visitNumber),
            portcall.portcallAlias.map { it.alias }.toSet(),
        )
        val portcallVisits = portcall.visits
        assertEquals(2, portcallVisits.size)
        val addedVisit = portcallVisits[1]
        assertEquals("My Berth", addedVisit.berthName)
        assertEquals(arrivalTime, addedVisit.berthAta)
        verify(eventService, times(1)).onUpdatedPortcall(any(), any())
        assertEquals(2, capturedEvents?.size)
        val agentChangedEvent = capturedEvents?.firstNotNullOfOrNull { it as? PortcallPlusAgentChangedEvent }
        assertNotNull(agentChangedEvent)
        assertEquals("A random agency", agentChangedEvent?.vesselAgent)
        val visitsUpdateEvent = capturedEvents?.firstNotNullOfOrNull { it as? PortcallPlusVisitsUpdateEvent }
        assertNotNull(visitsUpdateEvent)
        assertEquals(AreaIdentifier.AreaType.PORT, visitsUpdateEvent?.port?.type)
        assertEquals(IDPREFIX_USCRP, visitsUpdateEvent?.port?.unlocode)
        assertEquals(2, visitsUpdateEvent?.visits?.size)
        val addedVisitFromEvent = visitsUpdateEvent?.visits?.get(1)
        assertEquals(arrivalTime.toInstant(), addedVisitFromEvent?.berthAta)
        val portEtaEvent = capturedEvents?.firstNotNullOfOrNull { it as? PortcallPlusEtaEvent }
        assertNull(portEtaEvent)
    }

    @Test
    fun Test_a_Portcall_where_berth_gets_replaced_by_same_movementId() {
        val currentTime = Date()
        val departureTime = fetchTimestamp(currentTime, 1, TimeUnit.HOURS)
        val newArrivalTime = fetchTimestamp(currentTime, 2, TimeUnit.HOURS)
        val movementId = "movementId-to-get-berth-changed"
        val movement = getTestingMovement(
            movementId = movementId,
            from_stop_location_code = "SEA",
            to_stop_location_code = "A different berth",
            to_stop_location = "Corrected berth",
            dataExportTime = currentTime,
            scheduledTime = currentTime,
            underwayTime = departureTime,
            offTime = newArrivalTime,
        )

        val portcallId = "portcallId"
        whenever(portcallDataSource.generateId(eq(IDPREFIX_USCRP), any(), any())).thenReturn(portcallId)
        whenever(pomaService.getBerths(any(), eq(IDPREFIX_USCRP))).thenReturn(emptyArray<Berth>())
        var capturedEvents: List<PortcallPlusEvent>? = null
        doAnswer { invocation ->
            // this is the actual manual capture!
            capturedEvents = invocation.callRealMethod() as List<PortcallPlusEvent>
            capturedEvents
        }.whenever(eventService).createEventsForUpdatePortcall(any<Portcall>(), any<Portcall>())

        val firstBerthATA = fetchTimestamp(currentTime, -2, TimeUnit.DAYS)
        val previousVisitTime = fetchTimestamp(currentTime, -1, TimeUnit.DAYS)
        val existingPortcall = Portcall(
            portcallId = portcallId,
            portcallAlias = setOfNotNull(
                PortcallAlias(PortcallAliasName.CORPUS_CHRISTI, movement.visitNumber),
                movement.visitId?.let { PortcallAlias(PortcallAliasName.CORPUS_CHRISTI, it) },
            ),
            port = IDPREFIX_USCRP,
            imo = movement.vesselIMO,
            source = CORPUS_CHRISTI,
            // previous data had them 1 day later!
            portAtaTime = firstBerthATA,
            startTime = firstBerthATA,
            visits = listOf(
                PortcallVisit(
                    uniqueBerthId = "A previous Portcall visit 1",
                    berthName = "A previous visit berth name",
                    berthAta = firstBerthATA,
                ),
                PortcallVisit(
                    arrivalMovementId = movementId, // it's identified by the arrivalMovementId
                    uniqueBerthId = "A previous Portcall visit 2",
                    berthName = "Berth to get replaced",
                    berthAta = previousVisitTime,
                ),
            ),
        )
        val existingPortcallOneList = listOf(existingPortcall)
        whenever(portcallDataSource.searchByAlias(any(), eq(PortcallAliasName.CORPUS_CHRISTI))).thenReturn(existingPortcallOneList)
        whenever(portcallDataSource.get(any())).thenReturn(existingPortcallOneList)
        // this is the two methods in combination that we need to test
        val portcall = corpusChristiService.convertToPortcall(movement, movement.vesselIMO)
        corpusChristiService.updatePortcall(listOf(portcall))

        assertEquals(existingPortcall.startTime, portcall.startTime)
        assertEquals(existingPortcall.portAtaTime, portcall.portAtaTime)
        val portcallVisits = portcall.visits
        assertEquals(2, portcallVisits.size) // not three! as the movementId is the same
        val modifiedVisit = portcallVisits[1]
        assertEquals("Corrected berth", modifiedVisit.berthName)
        assertEquals(newArrivalTime, modifiedVisit.berthAta)
        assertTrue(
            portcallVisits.none { it.berthName == "Berth to get replaced" },
            "Berth 1 shoudn't appear in the visits, because it's replaced by Berth 2.",
        )
    }

    @Test
    fun Test_a_Portcall_where_berth_gets_added_with_different_movementId() {
        val currentTime = Date()
        val departureTime = fetchTimestamp(currentTime, 1, TimeUnit.HOURS)
        val newArrivalTime = fetchTimestamp(currentTime, 2, TimeUnit.HOURS)
        val movement = getTestingMovement(
            movementId = "movementId2",
            from_stop_location_code = "SEA",
            to_stop_location_code = "A different berth",
            to_stop_location = "Corrected berth",
            dataExportTime = currentTime,
            scheduledTime = currentTime,
            underwayTime = departureTime,
            offTime = newArrivalTime,
        )

        val portcallId = "portcallId"
        whenever(portcallDataSource.generateId(eq(IDPREFIX_USCRP), any(), any())).thenReturn(portcallId)
        whenever(pomaService.getBerths(any(), eq(IDPREFIX_USCRP))).thenReturn(emptyArray<Berth>())
        var capturedEvents: List<PortcallPlusEvent>? = null
        doAnswer { invocation ->
            // this is the actual manual capture!
            capturedEvents = invocation.callRealMethod() as List<PortcallPlusEvent>
            capturedEvents
        }.whenever(eventService).createEventsForUpdatePortcall(any<Portcall>(), any<Portcall>())

        val firstBerthATA = fetchTimestamp(currentTime, -2, TimeUnit.DAYS)
        val previousVisitTime = fetchTimestamp(currentTime, -1, TimeUnit.DAYS)
        val existingPortcall = Portcall(
            portcallId = portcallId,
            portcallAlias = setOfNotNull(
                PortcallAlias(PortcallAliasName.CORPUS_CHRISTI, movement.visitNumber),
                movement.visitId?.let { PortcallAlias(PortcallAliasName.CORPUS_CHRISTI, it) },
            ),
            port = IDPREFIX_USCRP,
            imo = movement.vesselIMO,
            source = CORPUS_CHRISTI,
            // previous data had them 1 day later!
            portAtaTime = firstBerthATA,
            startTime = firstBerthATA,
            visits = listOf(
                PortcallVisit(
                    uniqueBerthId = "A previous Portcall visit",
                    berthName = "A previous visit berth name 1",
                    berthAta = firstBerthATA,
                ),
                PortcallVisit(
                    uniqueBerthId = "A previous Portcall visit",
                    berthName = "A previous visit berth name 2",
                    berthAta = previousVisitTime,
                ),
            ),
        )
        val existingPortcallOneList = listOf(existingPortcall)
        whenever(portcallDataSource.searchByAlias(any(), eq(PortcallAliasName.CORPUS_CHRISTI))).thenReturn(existingPortcallOneList)
        whenever(portcallDataSource.get(any())).thenReturn(existingPortcallOneList)
        // this is the two methods in combination that we need to test
        val portcall = corpusChristiService.convertToPortcall(movement, movement.vesselIMO)
        corpusChristiService.updatePortcall(listOf(portcall))

        assertEquals(existingPortcall.startTime, portcall.startTime)
        assertEquals(existingPortcall.portAtaTime, portcall.portAtaTime)
        val portcallVisits = portcall.visits
        val firstUpdatedVisit = portcall.visits[0]
        val firstExistingVisit = existingPortcall.visits[0]
        assertEquals(firstExistingVisit, firstUpdatedVisit)
        val secondUpdatedVisit = portcall.visits[1]
        val secondExistingVisit = existingPortcall.visits[1]
        assertEquals(secondExistingVisit, secondUpdatedVisit)
        assertEquals(3, portcallVisits.size) // not three! as the movementId is the same
        val modifiedVisit = portcallVisits[2]
        assertEquals("Corrected berth", modifiedVisit.berthName)
        assertEquals(newArrivalTime, modifiedVisit.berthAta)
    }

    @Test
    fun three_movements_SEA_BERTH1_BERTH2_SEA_result_in_2_visits() {
        val sea = "sea"
        val berth1 = "BERTH_1"
        val berth2 = "BERTH_2"
        val seaToBerth1Movement = getTestingMovement(
            from_stop_location_code = sea,
            from_stop_location_id = sea,
            from_stop_location = sea,
            to_stop_location_code = berth1,
            to_stop_location_id = berth1,
            to_stop_location = berth1,
        )
        val berth1ToBerth2Movement = getTestingMovement(
            from_stop_location_code = berth1,
            from_stop_location_id = berth1,
            from_stop_location = berth1,
            to_stop_location_code = berth2,
            to_stop_location_id = berth2,
            to_stop_location = berth2,
        )
        val berth2ToSeaMovement = getTestingMovement(
            from_stop_location_code = berth2,
            from_stop_location_id = berth2,
            from_stop_location = berth2,
            to_stop_location_code = sea,
            to_stop_location_id = sea,
            to_stop_location = sea,
        )
        val movementSequence = listOf(
            seaToBerth1Movement,
            berth1ToBerth2Movement,
            berth2ToSeaMovement,
        )
        val pomaBerth1 = createTestBerth(_id = berth1, uniqueId = berth1, name = berth1, terminalName = "TERMINAL_1")
        val pomaBerth2 = createTestBerth(_id = berth2, uniqueId = berth2, name = berth2, terminalName = "TERMINAL_2")
        whenever(pomaService.getBerths(eq(berth1), eq(IDPREFIX_USCRP))).thenReturn(arrayOf(pomaBerth1))
        whenever(pomaService.getBerths(eq(berth2), eq(IDPREFIX_USCRP))).thenReturn(arrayOf(pomaBerth2))

        val portcallId = "portcallId"
        whenever(portcallDataSource.generateId(eq(IDPREFIX_USCRP), any(), any())).thenReturn(portcallId)
        // no initial portcall
        whenever(portcallDataSource.searchByAlias(any(), eq(PortcallAliasName.CORPUS_CHRISTI))).thenReturn(emptyList())
        whenever(portcallDataSource.get(any())).thenReturn(emptyList())
        var portcall: Portcall? = null
        movementSequence.forEach { itMovement ->
            portcall = corpusChristiService.convertToPortcall(itMovement, itMovement.vesselIMO)
            // Let's update the mocked portcall for the next iteration!
            corpusChristiService.updatePortcall(listOf(portcall!!))
            val existingOnePortcallList = listOf(portcall!!)
            `when`(portcallDataSource.searchByAlias(any(), eq(PortcallAliasName.CORPUS_CHRISTI)))
                .thenReturn(existingOnePortcallList)
            whenever(portcallDataSource.get(any())).thenReturn(existingOnePortcallList)
        }
        val visits = portcall!!.visits
        assertEquals(2, visits.size)
        assertEquals(berth1, visits[0].berthName)
        assertEquals(berth2, visits[1].berthName)
    }

    @Test
    fun four_movement_objects_SEA_cancelled_BERTH1_BERTH2_BERTH3_SEA_result_in_2_visits() {
        val sea = "sea"
        val berth1 = "BERTH_1"
        val berth2 = "BERTH_2"
        val berth3 = "BERTH_3"
        val movementIdThatGetsCorrected = "movementId"
        // this movement will be
        val seaToBerth1Movement = getTestingMovement(
            movementId = movementIdThatGetsCorrected,
            from_stop_location_code = sea,
            from_stop_location_id = sea,
            from_stop_location = sea,
            to_stop_location_code = berth1,
            to_stop_location_id = berth1,
            to_stop_location = berth1,
        )
        val seaToBerth2Movement_correction = getTestingMovement(
            movementId = movementIdThatGetsCorrected,
            from_stop_location_code = sea,
            from_stop_location_id = sea,
            from_stop_location = sea,
            to_stop_location_code = berth2,
            to_stop_location_id = berth2,
            to_stop_location = berth2,
        )
        val berth2ToBerth3Movement = getTestingMovement(
            from_stop_location_code = berth2,
            from_stop_location_id = berth2,
            from_stop_location = berth2,
            to_stop_location_code = berth3,
            to_stop_location_id = berth3,
            to_stop_location = berth3,
        )
        val berth3ToSeaMovement = getTestingMovement(
            from_stop_location_code = berth3,
            from_stop_location_id = berth3,
            from_stop_location = berth3,
            to_stop_location_code = sea,
            to_stop_location_id = sea,
            to_stop_location = sea,
        )
        val movementSequence = listOf(
            seaToBerth1Movement,
            seaToBerth2Movement_correction,
            berth2ToBerth3Movement,
            berth3ToSeaMovement,
        )
        val pomaBerth1 = createTestBerth(_id = berth1, uniqueId = berth1, name = berth1, terminalName = "TERMINAL_1")
        val pomaBerth2 = createTestBerth(_id = berth2, uniqueId = berth2, name = berth2, terminalName = "TERMINAL_2")
        val pomaBerth3 = createTestBerth(_id = berth3, uniqueId = berth3, name = berth3, terminalName = "TERMINAL_3")
        whenever(pomaService.getBerths(eq(berth1), eq(IDPREFIX_USCRP))).thenReturn(arrayOf(pomaBerth1))
        whenever(pomaService.getBerths(eq(berth2), eq(IDPREFIX_USCRP))).thenReturn(arrayOf(pomaBerth2))
        whenever(pomaService.getBerths(eq(berth3), eq(IDPREFIX_USCRP))).thenReturn(arrayOf(pomaBerth3))

        val portcallId = "portcallId"
        whenever(portcallDataSource.generateId(eq(IDPREFIX_USCRP), any(), any())).thenReturn(portcallId)
        // no initial portcall
        whenever(portcallDataSource.searchByAlias(any(), eq(PortcallAliasName.CORPUS_CHRISTI))).thenReturn(emptyList())
        whenever(portcallDataSource.get(any())).thenReturn(emptyList())
        var portcall: Portcall? = null
        movementSequence.forEach { itMovement ->
            portcall = corpusChristiService.convertToPortcall(itMovement, itMovement.vesselIMO)
            // Let's update the mocked portcall for the next iteration!
            corpusChristiService.updatePortcall(listOf(portcall!!))
            val existingOnePortcallList = listOf(portcall!!)
            `when`(portcallDataSource.searchByAlias(any(), eq(PortcallAliasName.CORPUS_CHRISTI)))
                .thenReturn(existingOnePortcallList)
            whenever(portcallDataSource.get(any())).thenReturn(existingOnePortcallList)
        }
        val visits = portcall!!.visits
        assertEquals(2, visits.size)
        assertEquals(berth2, visits[0].berthName)
        assertEquals(berth3, visits[1].berthName)
        assertTrue(
            visits.none { it.berthName == berth1 },
            "Berth 1 shoudn't appear in the visits, because it's replaced by Berth 2.",
        )
    }
}
