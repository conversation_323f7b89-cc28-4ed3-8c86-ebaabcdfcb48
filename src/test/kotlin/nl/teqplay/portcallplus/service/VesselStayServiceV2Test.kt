package nl.teqplay.portcallplus.service

import com.fasterxml.jackson.module.kotlin.readValue
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import nl.teqplay.poma.api.v1.Berth
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallAlias
import nl.teqplay.portcallplus.api.model.PortcallAliasName
import nl.teqplay.portcallplus.api.model.PortcallStatus
import nl.teqplay.portcallplus.api.model.PortcallVisit
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.LIS_SCRAPPER
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.NXTPORT_V2
import nl.teqplay.portcallplus.api.model.UpdateType
import nl.teqplay.portcallplus.datasource.ActivityDataSource
import nl.teqplay.portcallplus.datasource.MTurkHITDataSource
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.datasource.ScheduledTaskRunDataSource
import nl.teqplay.portcallplus.datasource.ServiceFetchCounterDataSource
import nl.teqplay.portcallplus.model.ScheduledTaskTypeFieldsProxy
import nl.teqplay.portcallplus.model.service.StayV2
import nl.teqplay.portcallplus.objectMapper
import nl.teqplay.portcallplus.properties.MTurkProperties
import nl.teqplay.portcallplus.properties.NxtPortV2Properties
import nl.teqplay.portcallplus.service.common.EventService
import nl.teqplay.portcallplus.service.external.MTurkService
import nl.teqplay.portcallplus.service.external.nxtport.PortOfAntwerpBrugesService
import nl.teqplay.portcallplus.service.external.nxtport.VesselStayServiceV2
import nl.teqplay.portcallplus.service.internal.PoMaService
import nl.teqplay.portcallplus.utils.MTurkUtilsTest
import nl.teqplay.portcallplus.utils.PortOfAntwerpBrugesUtilsTest
import nl.teqplay.portcallplus.utils.TestHelper
import nl.teqplay.portcallplus.utils.fetchMillisTrimmedTimestamp
import nl.teqplay.portcallplus.utils.fetchTimestamp
import nl.teqplay.portcallplus.utils.getProperBerthName
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.contains
import org.hamcrest.Matchers.`is`
import org.hamcrest.Matchers.not
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito.anyString
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import org.mockito.kotlin.spy
import org.mockito.kotlin.whenever
import org.springframework.boot.test.system.OutputCaptureExtension
import java.util.Date
import java.util.concurrent.TimeUnit

@ExtendWith(MockitoExtension::class)
@ExtendWith(OutputCaptureExtension::class)
class VesselStayServiceV2Test {
    @Mock
    lateinit var scheduledTaskRunDataSource: ScheduledTaskRunDataSource

    @Mock
    lateinit var activityDataSource: ActivityDataSource

    @Mock
    lateinit var serviceFetchCounterDataSource: ServiceFetchCounterDataSource

    @Mock
    lateinit var portcallDataSource: PortcallDataSource

    val mTurkService = mockk<MTurkService>()

    @Mock
    private lateinit var scheduledTaskTypeFieldsProxy: ScheduledTaskTypeFieldsProxy

    @Mock
    lateinit var config: NxtPortV2Properties

    @Mock
    lateinit var mTurkProperties: MTurkProperties

    private lateinit var vesselStayService: VesselStayServiceV2

    private val localHostUrl = "https://notfound"

    @Mock
    lateinit var mTurkHITDataSource: MTurkHITDataSource

    @Mock
    private lateinit var eventService: EventService

    @Mock
    private lateinit var portOfAntwerpBrugesService: PortOfAntwerpBrugesService

    private val pomaServiceMock = mock<PoMaService> {
        whenever(it.getBerths(anyOrNull(), anyOrNull())).thenReturn(emptyArray<Berth>())
    }

    @BeforeEach
    fun before() {
        whenever(config.enable).thenReturn(false)
        whenever(config.allowOtherSourcePortcallUpdate).thenReturn(true)
        whenever(config.loginUrl).thenReturn("")
        whenever(config.url).thenReturn("")
        whenever(config.subscriptionKey).thenReturn("")
        whenever(config.userName).thenReturn("")
        whenever(config.password).thenReturn("")
        whenever(config.clientId).thenReturn("")
        whenever(config.clientSecret).thenReturn("")
        whenever(config.newPortcallIntervalInDays).thenReturn(2)
        whenever(mTurkProperties.disposeAfterExpiring).thenReturn(true)
        coEvery { mTurkService.isMTurkEnabled() }.coAnswers { false }

        vesselStayService = spy(
            VesselStayServiceV2(
                scheduledTaskRunDataSource, portcallDataSource, mTurkService, activityDataSource,
                serviceFetchCounterDataSource, config, mTurkProperties, scheduledTaskTypeFieldsProxy, mock(), pomaServiceMock, mock(),
                portOfAntwerpBrugesService,
            ),
        )
    }

    @Test
    fun `test convert stayV2 to Portcall info`() {
        val imo = "9297151"
        val portcallId = "V236869"
        val teqplayPortcallId = "1"
        val eta = fetchMillisTrimmedTimestamp(Date())

        val berth = "BEANR119289BS277"
        val properBerthName = "S277"
        val stay = getStayV2(
            imo = imo,
            portcallId = portcallId,
            eta = eta,
            berthArrival = berth,
            berthDeparture = berth,
        )
        whenever(portcallDataSource.generateId("BEANR", imo, eta)).thenReturn(teqplayPortcallId)
        `when`(portcallDataSource.getVesselAgentsByFormattedKey()).thenReturn(emptyMap())
        val portcall = vesselStayService.convertToPortcall(stay, stay.imo!!, NXTPORT_V2)

        assertThat(portcall.startTime, `is`(eta))
        assertThat(portcall.portcallId, `is`(teqplayPortcallId))
        assertThat(portcall.portcallAlias, contains(PortcallAlias(PortcallAliasName.NXTPORT, portcallId)))
        assertThat(portcall.imo, `is`(imo))
        assertThat(portcall.originUnlocode, `is`("NLRTM"))
        assertThat(portcall.destinationUnlocode, `is`("ESBIO"))
        assertThat(portcall.visits.size, `is`(1))
        val firstVisit = portcall.visits.first()
        assertThat(firstVisit.berthName, `is`(properBerthName))
        assertThat(firstVisit.visitType, `is`(UpdateType.BERTH))
        assertThat(firstVisit.berthEta, `is`(eta))
    }

    @Test
    fun `test convert stayV2 with different berths to Portcall info`() {
        val imo = "9297151"
        val portcallId = "V236869"
        val teqplayPortcallId = "1"
        val eta = fetchMillisTrimmedTimestamp(Date())
        val berthArrival = "123"
        val berthDeparture = "BEANRS234"
        val stay = getStayV2(imo, portcallId, eta, berthArrival, berthDeparture)
        whenever(portcallDataSource.generateId("BEANR", imo, eta)).thenReturn(teqplayPortcallId)
        `when`(portcallDataSource.getVesselAgentsByFormattedKey()).thenReturn(emptyMap())
        val portcall = vesselStayService.convertToPortcall(stay, imo, NXTPORT_V2)

        assertThat(portcall.startTime, `is`(eta))
        assertThat(portcall.portcallId, `is`(teqplayPortcallId))
        assertThat(portcall.portcallAlias, contains(PortcallAlias(PortcallAliasName.NXTPORT, portcallId)))
        assertThat(portcall.imo, `is`(imo))
        assertThat(portcall.originUnlocode, `is`("NLRTM"))
        assertThat(portcall.destinationUnlocode, `is`("ESBIO"))

        assertThat(portcall.visits.size, `is`(2))

        val firstVisit = portcall.visits.first()
        assertThat(firstVisit.berthName, `is`(berthArrival.getProperBerthName()))
        assertThat(firstVisit.berthEta, `is`(eta))
        assertThat(firstVisit.visitType, `is`(UpdateType.BERTH))

        val lastVisit = portcall.visits.last()
        assertThat(lastVisit.berthName, `is`(berthDeparture.getProperBerthName()))
        assertThat(lastVisit.visitType, `is`(UpdateType.NOMINATION))
    }

    @Test
    fun `test if visits are placed last when no eta is seen`() {
        val imo = "9297151"
        val portcallId = "V236869"
        val eta = fetchMillisTrimmedTimestamp(Date())
        val berthArrival = "123"
        val berthDeparture = "234"
        val stay = getStayV2(imo, portcallId, eta, berthArrival, berthDeparture)
        var portcall = TestHelper.getTestPortcall(
            port = Portcall.IDPREFIX_ANR,
            portcallId = stay.stayNumber!!,
            visits = listOf(
                PortcallVisit(eta, berthName = berthArrival),
            ),
        )

        `when`(portcallDataSource.searchByAlias(stay.stayNumber!!, PortcallAliasName.NXTPORT))
            .thenReturn(listOf(portcall))
        `when`(portcallDataSource.getVesselAgentsByFormattedKey()).thenReturn(emptyMap())
        portcall = vesselStayService.convertToPortcall(stay, imo, NXTPORT_V2)

        assertThat(portcall.visits.size, `is`(2))
        val firstVisit = portcall.visits.first()
        assertThat(firstVisit.berthName, `is`(berthArrival.getProperBerthName()))
        assertThat(firstVisit.berthEta, `is`(eta))
        assertThat(firstVisit.visitType, `is`(UpdateType.BERTH))

        val lastVisit = portcall.visits.last()
        assertThat(lastVisit.berthName, `is`(berthDeparture.getProperBerthName()))
        assertThat(lastVisit.visitType, `is`(UpdateType.NOMINATION))
    }

    private fun getStayV2(
        imo: String = "9297151",
        portcallId: String = "V236869",
        eta: Date = fetchTimestamp("2021-05-18T19:08:00Z"),
        berthArrival: String = "277",
        berthDeparture: String = "277",
    ): StayV2 {
        return objectMapper.readValue(
            "{\n" +
                "        \"ship\": {\n" +
                "            \"ship_name\": \"SOFIE THERESA\",\n" +
                "            \"imo_number\": \"$imo\",\n" +
                "            \"call_sign\": \"OUQV2\",\n" +
                "            \"ship_type\": \"CHTAN\"\n" +
                "        },\n" +
                "        \"stay_number\": \"$portcallId\",\n" +
                "        \"previous_port_of_call\": \"NLRTM\",\n" +
                "        \"next_port_of_call\": \"ESBIO\",\n" +
                "        \"cbs_entry_point\": \"000SB\",\n" +
                "        \"cbs_entry_point_eta\": \"${fetchTimestamp(eta)}\",\n" +
                "        \"berth_at_arrival\": \"$berthArrival\",\n" +
                "        \"berth_at_departure\": \"$berthDeparture\"\n" +
                "    }",
        )
    }

    @Test
    fun `test if alias lookup of portcall fails but nearest IMO and date works and updating a portcall`() {
        val imo = "9297151"
        val portcallId = "V236869"
        val eta = fetchMillisTrimmedTimestamp(Date())
        val berthArrival = "123"
        val berthDeparture = "234"
        val stay = getStayV2(imo, portcallId, eta, berthArrival, berthDeparture)
        var portcall = TestHelper.getTestPortcall(
            port = Portcall.IDPREFIX_ANR,
            portcallId = stay.stayNumber!!,
            visits = listOf(
                PortcallVisit(eta, berthName = berthArrival),
            ),
        )
        // The updated portcall should be overwriting the source but keep the status of the existing portcall
        val portcallNew = portcall.copy(status = PortcallStatus.OUTBOUND, source = LIS_SCRAPPER)
        `when`(portcallDataSource.searchByAlias(stay.stayNumber!!, PortcallAliasName.NXTPORT))
            .thenReturn(emptyList())
        `when`(portcallDataSource.getNearestByImoAndDateInterval(imo, Portcall.IDPREFIX_ANR, eta, TimeUnit.DAYS.toMillis(2)))
            .thenReturn(portcallNew)
        `when`(portcallDataSource.getVesselAgentsByFormattedKey()).thenReturn(emptyMap())
        portcall = vesselStayService.convertToPortcall(stay, imo, NXTPORT_V2)
        assertThat(portcall.visits.size, `is`(2))
        val firstVisit = portcall.visits.first()
        assertThat(firstVisit.berthName, `is`(berthArrival))
        assertThat(firstVisit.berthEta, `is`(eta))
        assertThat(firstVisit.visitType, `is`(UpdateType.BERTH))
        // An updated portcall should have no change in status and source as NXTPORT_V2
        assertThat(portcall.status.name, `is`(PortcallStatus.OUTBOUND.toString()))
        assertThat(portcall.source, `is`(NXTPORT_V2))
    }

    @Test
    fun `test if alias lookup and nearest imo and port of portcall fails and is creating a new portcall`() {
        val imo = "9297151"
        val portcallId = "V236869"
        val now = Date()
        val eta = fetchMillisTrimmedTimestamp(now)
        val berthArrival = "123"
        val berthDeparture = "234"
        val stay = getStayV2(imo, portcallId, eta, berthArrival, berthDeparture)
        `when`(portcallDataSource.generateId(any(), any(), any()))
            .thenReturn(portcallId)
        `when`(portcallDataSource.searchByAlias(stay.stayNumber!!, PortcallAliasName.NXTPORT))
            .thenReturn(emptyList())
        `when`(portcallDataSource.getNearestByImoAndDateInterval(imo, Portcall.IDPREFIX_ANR, eta, TimeUnit.DAYS.toMillis(2)))
            .thenReturn(null)
        `when`(portcallDataSource.getVesselAgentsByFormattedKey()).thenReturn(emptyMap())
        val portcall = vesselStayService.convertToPortcall(stay, imo, NXTPORT_V2)
        assertThat(portcall.portcallId, `is`(portcallId))
        assertThat(portcall.imo, `is`(imo))
        // A new portcall should have an inbound status and source as NXTPORT_V2
        assertThat(portcall.status.name, `is`(PortcallStatus.INBOUND.toString()))
        assertThat(portcall.source, `is`(NXTPORT_V2))
    }

    /**
     * Test that a portcall's create operation when:
     * a) NO agent was obtained from portOfAntwerp site,
     * b) NO HITs existed for that ship imo and stayNumber
     * Results in:
     * 1) CREATES a HIT
     */
    @Test
    fun `create case 0-0`() {
        val imo = "9297151"
        val portcallId = "V236869"
        val stay = getStayV2(imo, portcallId)
        `when`(portcallDataSource.searchByAlias(stay.stayNumber!!, PortcallAliasName.NXTPORT)).thenReturn(emptyList())
        `when`(portcallDataSource.generateId(any(), any(), any())).thenReturn(portcallId)
        // mturk is enabled
        coEvery { mTurkService.isMTurkEnabled() }.coAnswers { true }
        // no agent will be detected
        val testResponse = PortOfAntwerpBrugesUtilsTest.getTestResponse(imoNumber = "9297152")
        doReturn(testResponse)
            .`when`(portOfAntwerpBrugesService).getStay(anyString(), anyString())
        // no hit for the ship-alias is found
        coEvery { mTurkService.getHITByImoAndPortcallAlias(any(), any()) }.coAnswers { null }
        `when`(portcallDataSource.getVesselAgentsByFormattedKey()).thenReturn(emptyMap())
        vesselStayService.convertToPortcall(stay, imo, NXTPORT_V2)
        // Verify a new HIT is created
        coVerify(timeout = 10L, exactly = 1) { mTurkService.handleHITRequest(any()) }
    }

    /**
     * Test that a portcall's create operation when:
     * a) NO agent was obtained from portOfAntwerp site,
     * b) A HIT already EXISTED for that ship imo and stayNumber
     * Results in:
     * 1) DOESN'T create a HIT
     */
    @Test
    fun `create case 0-1`() {
        val imo = "9297151"
        val portcallId = "V236869"
        val stay = getStayV2(imo, portcallId)
        `when`(portcallDataSource.searchByAlias(stay.stayNumber!!, PortcallAliasName.NXTPORT)).thenReturn(emptyList())
        `when`(portcallDataSource.generateId(any(), any(), any())).thenReturn(portcallId)
        // mturk is enabled
        coEvery { mTurkService.isMTurkEnabled() }.coAnswers { true }
        // no agent will be detected
        val testResponse = PortOfAntwerpBrugesUtilsTest.getTestResponse(imoNumber = "9297152")
        doReturn(testResponse)
            .`when`(portOfAntwerpBrugesService).getStay(anyString(), anyString())
        // an existed hit for the ship-alias is found
        val existingHIT = MTurkUtilsTest.getMTurkHITDetails(imo, portcallId)
        coEvery { mTurkService.getHITByImoAndPortcallAlias(any(), any()) }.coAnswers { existingHIT }
        `when`(portcallDataSource.getVesselAgentsByFormattedKey()).thenReturn(emptyMap())
        vesselStayService.convertToPortcall(stay, imo, NXTPORT_V2)
        // Verify no new HIT is created
        coVerify(exactly = 0) { mTurkService.handleHITRequest(any()) }
    }

    /**
     * Test that a portcall's create operation
     * a) A VALID agent was obtained from portOfAntwerp site,
     * b) NO HITs existed for that ship imo and stayNumber
     * Results in:
     * 1) Not creating any HIT, because the agent is obtained
     */
    @Test
    fun `create case 1-0`() {
        val imo = "9297151"
        val portcallId = "V236869"
        val stay = getStayV2(imo, portcallId)
        `when`(portcallDataSource.searchByAlias(stay.stayNumber!!, PortcallAliasName.NXTPORT)).thenReturn(emptyList())
        `when`(portcallDataSource.generateId(any(), any(), any())).thenReturn(portcallId)
        // mturk is enabled
        coEvery { mTurkService.isMTurkEnabled() }.coAnswers { true }
        // a valid agent will be detected
        val testResponse = PortOfAntwerpBrugesUtilsTest.getTestResponse(
            imoNumber = imo,
            stayDetail = listOf(PortOfAntwerpBrugesUtilsTest.getTestResponseStay(stayId = portcallId, agentName = "TEST new agent name")),
        )
        doReturn(testResponse)
            .`when`(portOfAntwerpBrugesService).getStay(anyString(), anyString())
        // no hit for the ship-alias is found
        coEvery { mTurkService.getHITByImoAndPortcallAlias(any(), any()) }.coAnswers { null }
        `when`(portcallDataSource.getVesselAgentsByFormattedKey()).thenReturn(emptyMap())
        vesselStayService.convertToPortcall(stay, imo, NXTPORT_V2)
        // Verify a new HIT is created
        coVerify(exactly = 0) { mTurkService.handleHITRequest(any()) }
    }

    /**
     * Test that a portcall's create operation
     * a) A VALID agent was obtained from portOfAntwerp site,
     * b) A HIT already EXISTED for that ship imo and stayNumber
     * Results in:
     * 1) No hit is created
     * 2) processing existing assignments
     * 3) expiring the HIT
     * 4) deleting the HIT
     */
    @Test
    fun `create case 1-1`() {
        val imo = "9297151"
        val portcallId = "V236869"
        val stay = getStayV2(imo, portcallId)
        `when`(portcallDataSource.searchByAlias(stay.stayNumber!!, PortcallAliasName.NXTPORT)).thenReturn(emptyList())
        `when`(portcallDataSource.generateId(any(), any(), any())).thenReturn(portcallId)
        // mturk is enabled
        coEvery { mTurkService.isMTurkEnabled() }.coAnswers { true }
        // a valid agent will be detected
        val testResponse = PortOfAntwerpBrugesUtilsTest.getTestResponse(
            imoNumber = imo,
            stayDetail = listOf(PortOfAntwerpBrugesUtilsTest.getTestResponseStay(stayId = portcallId, agentName = "TEST new agent name")),
        )
        doReturn(testResponse)
            .`when`(portOfAntwerpBrugesService).getStay(anyString(), anyString())
        // an existed hit for the ship-alias is found
        val existingHIT = MTurkUtilsTest.getMTurkHITDetails(imo, portcallId)
        coEvery { mTurkService.getHITByImoAndPortcallAlias(any(), any()) }.coAnswers { existingHIT }
        // mock calls to mTurkService expected calls, so we can continue verifying
        coEvery { mTurkService.processAssignments(any(), any()) }.coAnswers { existingHIT to "TEST new agent name" }
        `when`(portcallDataSource.getVesselAgentsByFormattedKey()).thenReturn(mapOf("TESTagentname" to "Test agent name"))
        coEvery { mTurkService.expireHIT(any()) }.coAnswers { }
        coEvery { mTurkService.deleteHIT(any()) }.coAnswers { }
        coEvery { mTurkService.updateHitDetails(any()) }.coAnswers { existingHIT.copy(expired = true) }
        vesselStayService.convertToPortcall(stay, imo, NXTPORT_V2)
        // Verify no hit is created
        coVerify(exactly = 0) { mTurkService.handleHITRequest(any()) }
        // Verify existing hit gets its assignment processed
        coVerify(exactly = 1) { mTurkService.processAssignments(any(), any()) }
        // Verify hit is expired
        coVerify(exactly = 1) { mTurkService.expireHIT(any()) }
        // Verify hit is deleted
        coVerify(exactly = 1) { mTurkService.deleteHIT(any()) }
        // Verify hit is updated
        coVerify(exactly = 1) { mTurkService.updateHitDetails(any()) }
    }

    /** 0-0-0
     * Test that a portcall's update operation when:
     * a) NO agent was obtained from portOfAntwerp site,
     * b) NO agent in existing Portcall
     * c) NO HITs existed for that ship imo and stayNumber
     * Results in:
     * 1) CREATES a HIT
     */
    @Test
    fun `update case 0-0-0`() {
        val imo = "9297151"
        val portcallId = "V236869"
        val stay = getStayV2(imo, portcallId)
        // mturk is enabled
        coEvery { mTurkService.isMTurkEnabled() }.coAnswers { true }
        // no agent will be detected
        val testResponse = PortOfAntwerpBrugesUtilsTest.getTestResponse(imoNumber = "9297152")
        doReturn(testResponse)
            .`when`(portOfAntwerpBrugesService).getStay(anyString(), anyString())
        // existing portcall without valid agent
        val portcall = TestHelper.getTestPortcall(
            port = Portcall.IDPREFIX_ANR,
            portcallId = stay.stayNumber!!,
            imo = imo,
            source = NXTPORT_V2,
            vesselAgent = null,
        )
        `when`(portcallDataSource.searchByAlias(stay.stayNumber!!, PortcallAliasName.NXTPORT))
            .thenReturn(listOf(portcall))
        // no hit for the ship-alias is found
        coEvery { mTurkService.getHITByImoAndPortcallAlias(any(), any()) }.coAnswers { null }
        `when`(portcallDataSource.getVesselAgentsByFormattedKey()).thenReturn(emptyMap())
        vesselStayService.convertToPortcall(stay, imo, NXTPORT_V2)
        // Verify a new HIT is created
        coVerify(timeout = 10L, exactly = 1) { mTurkService.handleHITRequest(any()) }
    }

    /** 0-0-1
     * Test that a portcall's update operation when:
     * a) NO agent was obtained from portOfAntwerp site,
     * b) NO agent in existing Portcall
     * c) A HIT already EXISTED for that ship imo and stayNumber
     * Results in:
     * 1) Not creating any HIT, the HIT exists already
     */
    @Test
    fun `update case 0-0-1`() {
        val imo = "9297151"
        val portcallId = "V236869"
        val stay = getStayV2(imo, portcallId)
        // mturk is enabled
        coEvery { mTurkService.isMTurkEnabled() }.coAnswers { true }
        // no agent will be detected
        val testResponse = PortOfAntwerpBrugesUtilsTest.getTestResponse(imoNumber = "9297152")
        doReturn(testResponse)
            .`when`(portOfAntwerpBrugesService).getStay(anyString(), anyString())
        // existing portcall without valid agent
        val portcall = TestHelper.getTestPortcall(
            port = Portcall.IDPREFIX_ANR,
            portcallId = stay.stayNumber!!,
            imo = imo,
            source = NXTPORT_V2,
            vesselAgent = null,
        )
        `when`(portcallDataSource.searchByAlias(stay.stayNumber!!, PortcallAliasName.NXTPORT))
            .thenReturn(listOf(portcall))
        // an existed hit for the ship-alias is found
        val existingHIT = MTurkUtilsTest.getMTurkHITDetails(imo, portcallId)
        coEvery { mTurkService.getHITByImoAndPortcallAlias(any(), any()) }.coAnswers { existingHIT }
        `when`(portcallDataSource.getVesselAgentsByFormattedKey()).thenReturn(emptyMap())
        vesselStayService.convertToPortcall(stay, imo, NXTPORT_V2)
        // Verify no HIT is created
        coVerify(exactly = 0) { mTurkService.handleHITRequest(any()) }
    }

    /** 0-1-0
     * Test that a portcall's update operation when:
     * a) NO agent was obtained from portOfAntwerp site,
     * b) VALID agent in existing Portcall
     * c) NO HITs existed for that ship imo and stayNumber
     * Results in:
     * 1) Not creating any HIT, the portcall has an agent
     */
    @Test
    fun `update case 0-1-0`() {
        val imo = "9297151"
        val portcallId = "V236869"
        val stay = getStayV2(imo, portcallId)
        // mturk is enabled
        coEvery { mTurkService.isMTurkEnabled() }.coAnswers { true }
        // no agent will be detected
        val testResponse = PortOfAntwerpBrugesUtilsTest.getTestResponse(imoNumber = "9297152")
        doReturn(testResponse)
            .`when`(portOfAntwerpBrugesService).getStay(anyString(), anyString())
        // existing portcall without valid agent
        val portcall = TestHelper.getTestPortcall(
            port = Portcall.IDPREFIX_ANR,
            portcallId = stay.stayNumber!!,
            imo = imo,
            source = NXTPORT_V2,
            vesselAgent = "A valid Agent",
        )
        `when`(portcallDataSource.searchByAlias(stay.stayNumber!!, PortcallAliasName.NXTPORT))
            .thenReturn(listOf(portcall))
        // no hit for the ship-alias is found
        coEvery { mTurkService.getHITByImoAndPortcallAlias(any(), any()) }.coAnswers { null }
        `when`(portcallDataSource.getVesselAgentsByFormattedKey()).thenReturn(emptyMap())
        vesselStayService.convertToPortcall(stay, imo, NXTPORT_V2)
        // Verify a new HIT is created
        coVerify(exactly = 0) { mTurkService.handleHITRequest(any()) }
    }

    /** 0-1-1
     * Test that a portcall's update operation when:
     * a) NO agent was obtained from portOfAntwerp site,
     * b) VALID agent in existing Portcall
     * c) A HIT already EXISTED for that ship imo and stayNumber
     * Results in:
     * 1) No hit is created
     * 2) processing existing assignments
     * 3) expiring the HIT
     * 4) deleting the HIT
     */
    @Test
    fun `update case 0-1-1`() {
        val imo = "9297151"
        val portcallId = "V236869"
        val stay = getStayV2(imo, portcallId)
        // mturk is enabled
        coEvery { mTurkService.isMTurkEnabled() }.coAnswers { true }
        // no agent will be detected
        val testResponse = PortOfAntwerpBrugesUtilsTest.getTestResponse(imoNumber = "9297152")
        doReturn(testResponse)
            .`when`(portOfAntwerpBrugesService).getStay(anyString(), anyString())
        // existing portcall without valid agent
        val portcall = TestHelper.getTestPortcall(
            port = Portcall.IDPREFIX_ANR,
            portcallId = stay.stayNumber!!,
            imo = imo,
            source = NXTPORT_V2,
            vesselAgent = "A valid Agent",
        )
        `when`(portcallDataSource.searchByAlias(stay.stayNumber!!, PortcallAliasName.NXTPORT))
            .thenReturn(listOf(portcall))
        // an existed hit for the ship-alias is found
        val existingHIT = MTurkUtilsTest.getMTurkHITDetails(imo, portcallId)
        coEvery { mTurkService.getHITByImoAndPortcallAlias(any(), any()) }.coAnswers { existingHIT }
        // mock calls to mTurkService expected calls, so we can continue verifying
        coEvery { mTurkService.processAssignments(any(), any()) }.coAnswers { existingHIT to "TEST new agent name" }
        `when`(portcallDataSource.getVesselAgentsByFormattedKey()).thenReturn(mapOf("TESTagentname" to "Test agent name"))
        coEvery { mTurkService.expireHIT(any()) }.coAnswers { }
        coEvery { mTurkService.deleteHIT(any()) }.coAnswers { }
        coEvery { mTurkService.updateHitDetails(any()) }.coAnswers { existingHIT.copy(expired = true) }
        vesselStayService.convertToPortcall(stay, imo, NXTPORT_V2)
        // Verify no hit is created
        coVerify(exactly = 0) { mTurkService.handleHITRequest(any()) }
        // Verify existing hit gets its assignment processed
        coVerify(exactly = 1) { mTurkService.processAssignments(any(), any()) }
        // Verify hit is expired
        coVerify(exactly = 1) { mTurkService.expireHIT(any()) }
        // Verify hit is deleted
        coVerify(exactly = 1) { mTurkService.deleteHIT(any()) }
        // Verify hit is updated
        coVerify(exactly = 1) { mTurkService.updateHitDetails(any()) }
    }

    /** 1-0-0
     * Test that a portcall's update operation when:
     * a) A VALID agent was obtained from portOfAntwerp site,
     * b) NO agent in existing Portcall
     * c) NO HITs existed for that ship imo and stayNumber
     * Results in:
     * 1) Not creating any HIT, because the agent is obtained
     */
    @Test
    fun `update case 1-0-0`() {
        val imo = "9297151"
        val portcallId = "V236869"
        val stay = getStayV2(imo, portcallId)
        // mturk is enabled
        coEvery { mTurkService.isMTurkEnabled() }.coAnswers { true }
        // a valid agent will be detected
        val testResponse = PortOfAntwerpBrugesUtilsTest.getTestResponse(
            imoNumber = imo,
            stayDetail = listOf(PortOfAntwerpBrugesUtilsTest.getTestResponseStay(stayId = portcallId, agentName = "TEST new agent name")),
        )
        doReturn(testResponse)
            .`when`(portOfAntwerpBrugesService).getStay(anyString(), anyString())
        // existing portcall without valid agent
        val portcall = TestHelper.getTestPortcall(
            port = Portcall.IDPREFIX_ANR,
            portcallId = stay.stayNumber!!,
            imo = imo,
            source = NXTPORT_V2,
            vesselAgent = null,
        )
        `when`(portcallDataSource.searchByAlias(stay.stayNumber!!, PortcallAliasName.NXTPORT))
            .thenReturn(listOf(portcall))
        // no hit for the ship-alias is found
        coEvery { mTurkService.getHITByImoAndPortcallAlias(any(), any()) }.coAnswers { null }
        `when`(portcallDataSource.getVesselAgentsByFormattedKey()).thenReturn(mapOf("TESTagentname" to "Test agent name"))
        vesselStayService.convertToPortcall(stay, imo, NXTPORT_V2)
        // Verify a no HIT is created
        coVerify(exactly = 0) { mTurkService.handleHITRequest(any()) }
    }

    /** 1-0-1
     * Test that a portcall's update operation when:
     * a) A VALID agent was obtained from portOfAntwerp site,
     * b) NO agent in existing Portcall
     * c) A HIT already EXISTED for that ship imo and stayNumber
     * Results in:
     * 1) No hit is created
     * 2) processing existing assignments
     * 3) expiring the HIT
     * 4) deleting the HIT
     */
    @Test
    fun `update case 1-0-1`() {
        val imo = "9297151"
        val portcallId = "V236869"
        val stay = getStayV2(imo, portcallId)
        // mturk is enabled
        coEvery { mTurkService.isMTurkEnabled() }.coAnswers { true }
        // a valid agent will be detected
        val testResponse = PortOfAntwerpBrugesUtilsTest.getTestResponse(
            imoNumber = imo,
            stayDetail = listOf(PortOfAntwerpBrugesUtilsTest.getTestResponseStay(stayId = portcallId, agentName = "TEST new agent name")),
        )
        doReturn(testResponse)
            .`when`(portOfAntwerpBrugesService).getStay(anyString(), anyString())
        // existing portcall without valid agent
        val portcall = TestHelper.getTestPortcall(
            port = Portcall.IDPREFIX_ANR,
            portcallId = stay.stayNumber!!,
            imo = imo,
            source = NXTPORT_V2,
            vesselAgent = null,
        )
        `when`(portcallDataSource.searchByAlias(stay.stayNumber!!, PortcallAliasName.NXTPORT))
            .thenReturn(listOf(portcall))
        // an existed hit for the ship-alias is found
        val existingHIT = MTurkUtilsTest.getMTurkHITDetails(imo, portcallId)
        coEvery { mTurkService.getHITByImoAndPortcallAlias(any(), any()) }.coAnswers { existingHIT }
        // mock calls to mTurkService expected calls, so we can continue verifying
        coEvery { mTurkService.processAssignments(any(), any()) }.coAnswers { existingHIT to "TEST new agent name" }
        `when`(portcallDataSource.getVesselAgentsByFormattedKey()).thenReturn(mapOf("TESTagentname" to "Test agent name"))
        coEvery { mTurkService.expireHIT(any()) }.coAnswers { }
        coEvery { mTurkService.deleteHIT(any()) }.coAnswers { }
        coEvery { mTurkService.updateHitDetails(any()) }.coAnswers { existingHIT.copy(expired = true) }
        vesselStayService.convertToPortcall(stay, imo, NXTPORT_V2)
        // Verify no hit is created
        coVerify(exactly = 0) { mTurkService.handleHITRequest(any()) }
        // Verify existing hit gets its assignment processed
        coVerify(exactly = 1) { mTurkService.processAssignments(any(), any()) }
        // Verify hit is expired
        coVerify(exactly = 1) { mTurkService.expireHIT(any()) }
        // Verify hit is deleted
        coVerify(exactly = 1) { mTurkService.deleteHIT(any()) }
        // Verify hit is updated
        coVerify(exactly = 1) { mTurkService.updateHitDetails(any()) }
    }

    /** 1-1-0
     * Test that a portcall's update operation when:
     * a) A VALID agent was obtained from portOfAntwerp site,
     * b) VALID agent in existing Portcall
     * c) NO HITs existed for that ship imo and stayNumber
     * Results in:
     * 1) Not creating any HIT, because the agent is obtained and no HITs existed
     */
    @Test
    fun `update case 1-1-0`() {
        val imo = "9297151"
        val portcallId = "V236869"
        val stay = getStayV2(imo, portcallId)
        // mturk is enabled
        coEvery { mTurkService.isMTurkEnabled() }.coAnswers { true }
        // a valid agent will be detected
        val testResponse = PortOfAntwerpBrugesUtilsTest.getTestResponse(
            imoNumber = imo,
            stayDetail = listOf(PortOfAntwerpBrugesUtilsTest.getTestResponseStay(stayId = portcallId, agentName = "TEST new agent name")),
        )
        doReturn(testResponse)
            .`when`(portOfAntwerpBrugesService).getStay(anyString(), anyString())
        // existing portcall without valid agent
        val portcall = TestHelper.getTestPortcall(
            port = Portcall.IDPREFIX_ANR,
            portcallId = stay.stayNumber!!,
            imo = imo,
            source = NXTPORT_V2,
            vesselAgent = "A valid Agent",
        )
        `when`(portcallDataSource.searchByAlias(stay.stayNumber!!, PortcallAliasName.NXTPORT))
            .thenReturn(listOf(portcall))
        // no hit for the ship-alias is found
        coEvery { mTurkService.getHITByImoAndPortcallAlias(any(), any()) }.coAnswers { null }
        `when`(portcallDataSource.getVesselAgentsByFormattedKey()).thenReturn(emptyMap())
        vesselStayService.convertToPortcall(stay, imo, NXTPORT_V2)
        // Verify a new HIT is created
        coVerify(exactly = 0) { mTurkService.handleHITRequest(any()) }
    }

    /** 1-1-1
     * Test that a portcall's update operation when:
     * a) A VALID agent was obtained from portOfAntwerp site,
     * b) VALID agent in existing Portcall (irrelevant here! but covering the case)
     * c) A HIT already EXISTED for that ship imo and stayNumber
     * Results in:
     * 1) No hit is created
     * 2) processing existing assignments
     * 3) expiring the HIT
     * 4) deleting the HIT
     */
    @Test
    fun `update case 1-1-1`() {
        val imo = "9297151"
        val portcallId = "V236869"
        val stay = getStayV2(imo, portcallId)
        // mturk is enabled
        coEvery { mTurkService.isMTurkEnabled() }.coAnswers { true }
        // a valid agent will be detected
        val testResponse = PortOfAntwerpBrugesUtilsTest.getTestResponse(
            imoNumber = imo,
            stayDetail = listOf(PortOfAntwerpBrugesUtilsTest.getTestResponseStay(stayId = portcallId, agentName = "TEST new agent name")),
        )
        doReturn(testResponse)
            .`when`(portOfAntwerpBrugesService).getStay(anyString(), anyString())
        // existing portcall without valid agent
        val portcall = TestHelper.getTestPortcall(
            port = Portcall.IDPREFIX_ANR,
            portcallId = stay.stayNumber!!,
            imo = imo,
            source = NXTPORT_V2,
            vesselAgent = "A valid Agent",
        )
        `when`(portcallDataSource.searchByAlias(stay.stayNumber!!, PortcallAliasName.NXTPORT))
            .thenReturn(listOf(portcall))
        // an existed hit for the ship-alias is found
        val existingHIT = MTurkUtilsTest.getMTurkHITDetails(imo, portcallId)
        coEvery { mTurkService.getHITByImoAndPortcallAlias(any(), any()) }.coAnswers { existingHIT }
        // mock calls to mTurkService expected calls, so we can continue verifying
        coEvery { mTurkService.processAssignments(any(), any()) }.coAnswers { existingHIT to "TEST new agent name" }
        `when`(portcallDataSource.getVesselAgentsByFormattedKey()).thenReturn(mapOf("TESTagentname" to "Test agent name"))
        coEvery { mTurkService.expireHIT(any()) }.coAnswers { }
        coEvery { mTurkService.deleteHIT(any()) }.coAnswers { }
        coEvery { mTurkService.updateHitDetails(any()) }.coAnswers { existingHIT.copy(expired = true) }
        vesselStayService.convertToPortcall(stay, imo, NXTPORT_V2)
        // Verify no hit is created
        coVerify(exactly = 0) { mTurkService.handleHITRequest(any()) }
        // Verify existing hit gets its assignment processed
        coVerify(exactly = 1) { mTurkService.processAssignments(any(), any()) }
        // Verify hit is expired
        coVerify(exactly = 1) { mTurkService.expireHIT(any()) }
        // Verify hit is deleted
        coVerify(exactly = 1) { mTurkService.deleteHIT(any()) }
        // Verify hit is updated
        coVerify(exactly = 1) { mTurkService.updateHitDetails(any()) }
    }

    @Test
    fun `test scraping method on a new portcall creation`() {
        val imo = "9297151"
        val portcallId = "V236869"
        val eta = fetchMillisTrimmedTimestamp(Date())
        val berthArrival = "123"
        val berthDeparture = "234"
        val stay = getStayV2(imo, portcallId, eta, berthArrival, berthDeparture)
        val portcall = TestHelper.getTestPortcall(
            port = Portcall.IDPREFIX_ANR,
            portcallId = stay.stayNumber!!,
            imo = imo,
            source = NXTPORT_V2,
        )
        `when`(portcallDataSource.searchByAlias(stay.stayNumber!!, PortcallAliasName.NXTPORT))
            .thenReturn(listOf(portcall))
        val testResponse = PortOfAntwerpBrugesUtilsTest.getTestResponse(
            imoNumber = "9297151",
            stayDetail = listOf(PortOfAntwerpBrugesUtilsTest.getTestResponseStay(stayId = portcallId, agentName = "TEST agent name")),
        )
        doReturn(testResponse)
            .`when`(portOfAntwerpBrugesService).getStay(anyString(), anyString())
        `when`(portcallDataSource.getVesselAgentsByFormattedKey()).thenReturn(mapOf("TESTagentname" to "Test agent name"))
        val updatedPortcall = vesselStayService.convertToPortcall(stay, imo, NXTPORT_V2)
        assertThat(updatedPortcall.vesselAgent, `is`("TEST agent name"))
    }

    @Test
    fun `test scraping method is still called on a existing portcall when vesselAgent is already set and it gets updated`() {
        val imo = "9297151"
        val portcallId = "V236869"
        val eta = fetchMillisTrimmedTimestamp(Date())
        val berthArrival = "123"
        val berthDeparture = "234"
        val stay = getStayV2(imo, portcallId, eta, berthArrival, berthDeparture)
        val portcall = TestHelper.getTestPortcall(
            port = Portcall.IDPREFIX_ANR,
            portcallId = stay.stayNumber!!,
            imo = imo,
            source = NXTPORT_V2,
            vesselAgent = "Wrong agent name",
        )
        `when`(portcallDataSource.searchByAlias(stay.stayNumber!!, PortcallAliasName.NXTPORT))
            .thenReturn(listOf(portcall))
        val testResponse = PortOfAntwerpBrugesUtilsTest.getTestResponse(
            imoNumber = "9297151",
            stayDetail = listOf(PortOfAntwerpBrugesUtilsTest.getTestResponseStay(stayId = portcallId, agentName = "TEST agent name")),
        )
        doReturn(testResponse)
            .`when`(portOfAntwerpBrugesService).getStay(anyString(), anyString())
        `when`(portcallDataSource.getVesselAgentsByFormattedKey()).thenReturn(mapOf("TESTagentname" to "Test agent name"))
        val updatedPortcall = vesselStayService.convertToPortcall(stay, imo, NXTPORT_V2)
        assertThat(updatedPortcall.vesselAgent, `is`(not("Wrong agent name")))
    }

    @Test
    fun `test scraping method on a new portcall creation with a nonexisting agent and checking if the new agent will be set on the portcall`() {
        val imo = "9297151"
        val portcallId = "V236869"
        val eta = fetchMillisTrimmedTimestamp(Date())
        val berthArrival = "123"
        val berthDeparture = "234"
        val stay = getStayV2(imo, portcallId, eta, berthArrival, berthDeparture)
        val portcall = TestHelper.getTestPortcall(
            port = Portcall.IDPREFIX_ANR,
            portcallId = stay.stayNumber!!,
            imo = imo,
            source = NXTPORT_V2,
        )
        `when`(portcallDataSource.searchByAlias(stay.stayNumber!!, PortcallAliasName.NXTPORT))
            .thenReturn(listOf(portcall))
        val testResponse = PortOfAntwerpBrugesUtilsTest.getTestResponse(
            imoNumber = "9297151",
            stayDetail = listOf(PortOfAntwerpBrugesUtilsTest.getTestResponseStay(stayId = portcallId, agentName = "TEST new agent name")),
        )
        doReturn(testResponse)
            .`when`(portOfAntwerpBrugesService).getStay(anyString(), anyString())
        `when`(portcallDataSource.getVesselAgentsByFormattedKey()).thenReturn(mapOf("TESTagentname" to "Test agent name"))
        val updatedPortcall = vesselStayService.convertToPortcall(stay, imo, NXTPORT_V2)
        assertThat(updatedPortcall.vesselAgent, `is`("TEST new agent name"))
    }

    @Test
    fun `test scraping method on invalid connection but still continuing with CREATING portcall`() {
        val imo = "9297151"
        val portcallId = "V236869"
        val teqplayPortcallId = "1"
        val eta = fetchMillisTrimmedTimestamp(Date())
        val berthArrival = "123"
        val berthDeparture = "234"
        val stay = getStayV2(imo, portcallId, eta, berthArrival, berthDeparture)
        `when`(portcallDataSource.searchByAlias(stay.stayNumber!!, PortcallAliasName.NXTPORT))
            .thenReturn(emptyList())
        `when`(portcallDataSource.getNearestByImoAndDateInterval(imo, Portcall.IDPREFIX_ANR, eta, TimeUnit.DAYS.toMillis(2)))
            .thenReturn(null)
        whenever(portcallDataSource.generateId("BEANR", imo, eta)).thenReturn(teqplayPortcallId)
        `when`(portcallDataSource.getVesselAgentsByFormattedKey()).thenReturn(emptyMap())
        val createdPortcall = vesselStayService.convertToPortcall(stay, imo, NXTPORT_V2)
        // By not mocking vesselStayServiceV2.getPortOfAntwerpBrugesStayInfoByImo(),
        // the get in the method will fail due to url being mocked as "https://notfound"
        // simply, the portcall is created updated with no issue.
        assertEquals(createdPortcall.vesselAgent, null)
    }

    @Test
    fun `test scraping method on invalid connection but still continuing with UPDATING portcall`() {
        val imo = "9297151"
        val portcallId = "V236869"
        val eta = fetchMillisTrimmedTimestamp(Date())
        val berthArrival = "123"
        val berthDeparture = "234"
        val stay = getStayV2(imo, portcallId, eta, berthArrival, berthDeparture)
        val portcall = TestHelper.getTestPortcall(
            port = Portcall.IDPREFIX_ANR,
            portcallId = stay.stayNumber!!,
            imo = imo,
            source = NXTPORT_V2,
        )
        `when`(portcallDataSource.searchByAlias(stay.stayNumber!!, PortcallAliasName.NXTPORT))
            .thenReturn(listOf(portcall))
        `when`(portcallDataSource.getVesselAgentsByFormattedKey()).thenReturn(emptyMap())
        val updatedPortcall = vesselStayService.convertToPortcall(stay, imo, NXTPORT_V2)
        // By not mocking vesselStayServiceV2.getPortOfAntwerpBrugesStayInfoByImo(),
        // the get in the method will fail due to url being mocked as "https://notfound"
        // simply, the portcall is created updated with no issue.
        assertEquals(updatedPortcall.vesselAgent, null)
    }

    @Test
    fun `test when handling mturk in VesselStayService, not processing existing HIT if it is expired`() {
        // context
        val scrappedVesselAgent = "A valid scrapped agent"
        val imo = "1234567"
        val alias = "any alias"
        val portcallId = "any portcallId"
        val vesselAgentsByFormattedKey = mapOf("key" to "value")
        val expired = true
        // mocks
        val existingHIT = MTurkUtilsTest.getMTurkHITDetails(imo, portcallId, expired = expired)
        coEvery { mTurkService.getHITByImoAndPortcallAlias(any(), any()) }.coAnswers { existingHIT }
        // method to test
        vesselStayService.handleMTurk(scrappedVesselAgent, imo, alias, portcallId, vesselAgentsByFormattedKey)
        // assertions
        coVerify(exactly = 0) { mTurkService.processAssignments(any(), any()) }
        coVerify(exactly = 0) { mTurkService.expireHIT(any()) }
        coVerify(exactly = 0) { mTurkService.deleteHIT(any()) }
        coVerify(exactly = 0) { mTurkService.updateHitDetails(any()) }
    }

    @Test
    fun `test when handling mturk in VesselStayService, processing existing HIT if it is NOT expired`() {
        // context
        val scrappedVesselAgent = "A valid scrapped agent"
        val imo = "1234567"
        val alias = "any alias"
        val portcallId = "any portcallId"
        val vesselAgentsByFormattedKey = mapOf("key" to "value")
        val expired = false
        // mocks
        val existingHIT = MTurkUtilsTest.getMTurkHITDetails(imo, portcallId, expired = expired)
        coEvery { mTurkService.getHITByImoAndPortcallAlias(any(), any()) }.coAnswers { existingHIT }
        coEvery { mTurkService.processAssignments(any(), any()) }.coAnswers { existingHIT to "TEST new agent name" }
        coEvery { mTurkService.expireHIT(any()) }.coAnswers { }
        coEvery { mTurkService.deleteHIT(any()) }.coAnswers { }
        coEvery { mTurkService.updateHitDetails(any()) }.coAnswers { existingHIT.copy(expired = true) }
        // method to test
        vesselStayService.handleMTurk(scrappedVesselAgent, imo, alias, portcallId, vesselAgentsByFormattedKey)
        // assertions
        coVerify(exactly = 1) { mTurkService.processAssignments(any(), any()) }
        coVerify(exactly = 1) { mTurkService.expireHIT(any()) }
        coVerify(exactly = 1) { mTurkService.deleteHIT(any()) }
        coVerify(exactly = 1) { mTurkService.updateHitDetails(any()) }
    }
}
