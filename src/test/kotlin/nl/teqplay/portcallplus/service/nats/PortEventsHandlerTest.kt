package nl.teqplay.portcallplus.service.nats

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallStatus
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.UpdateType
import nl.teqplay.portcallplus.config.ObjectMapperConfiguration
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.properties.PortEventsProperties
import nl.teqplay.portcallplus.service.internal.CsiService
import nl.teqplay.portcallplus.service.internal.nats.PortEventsHandler
import nl.teqplay.portcallplus.utils.fetchTimestamp
import nl.teqplay.portcallplus.utils.getShipRegister
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.`is`
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertDoesNotThrow
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito.anyString
import org.mockito.Mockito.never
import org.mockito.Mockito.spy
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.eq
import java.util.Date
import java.util.concurrent.TimeUnit

@ExtendWith(MockitoExtension::class)
class PortEventsHandlerTest {
    private lateinit var portEventsHandler: PortEventsHandler

    @Mock
    lateinit var portcallDataSource: PortcallDataSource

    @Mock
    lateinit var csiService: CsiService

    val objectMapper = ObjectMapperConfiguration().objectMapper()

    private val config = PortEventsProperties(listOf("USCRP"))

    @BeforeEach
    fun before() {
        portEventsHandler = PortEventsHandler(portcallDataSource, csiService, config)
    }

    @Test
    fun testProcessPortAta() {
        val imo = "123"
        val port = "a"
        val dateTime = Date()
        `when`(portcallDataSource.getNearestByImoAndDate(eq(imo), eq(port), eq(dateTime), anyOrNull(), anyOrNull(), eq(false), anyOrNull()))
            .thenReturn(Portcall("x", emptySet(), port, imo, ScheduledTaskType.UNKNOWN, dateTime, UpdateType.PORT, PortcallStatus.INBOUND))
        var updatedPortcall = portEventsHandler.processAta(imo, port, dateTime.time)
        assertNotNull(updatedPortcall)
        assertThat(updatedPortcall?.status, `is`(PortcallStatus.INBOUND))
        assertThat(updatedPortcall?.portAtaTime, `is`(dateTime))

        // trigger another port.ata event after 2 hours
        val updateAta = fetchTimestamp(dateTime, 2, TimeUnit.DAYS)
        `when`(
            portcallDataSource.getNearestByImoAndDate(eq(imo), eq(port), eq(updateAta), anyOrNull(), anyOrNull(), eq(false), anyOrNull()),
        )
            .thenReturn(updatedPortcall)
        updatedPortcall = portEventsHandler.processAta(imo, port, updateAta.time)
        assertNotNull(updatedPortcall)
        assertThat(updatedPortcall?.status, `is`(PortcallStatus.INBOUND))
        // assert that portAtd or startTime has not changed
        assertThat(updatedPortcall?.portAtaTime, `is`(dateTime))
        assertThat(updatedPortcall?.startTime, `is`(dateTime))

        val imo2 = ""
        val notUpdatedPortcall = portEventsHandler.processAta(imo2, port, dateTime.time)
        assertNull(notUpdatedPortcall)
    }

    @Test
    fun testProcessPortAtd() {
        val imo = "123"
        val port = "a"
        val portAtd = Date()
        val fromDate = fetchTimestamp(Date(), -1, TimeUnit.DAYS)
        `when`(portcallDataSource.getNearestByImoAndDate(imo, port, portAtd, fromDate, portAtd, false, false))
            .thenReturn(
                Portcall("x", emptySet(), port, imo, ScheduledTaskType.UNKNOWN, portAtd, UpdateType.NOMINATION, PortcallStatus.INBOUND, null),
            )

        var updatedPortcall = portEventsHandler.processAtd(imo, port, portAtd.time, fromDate)
        assertNotNull(updatedPortcall)
        assertThat(updatedPortcall?.status, `is`(PortcallStatus.OUTBOUND))
        assertThat(updatedPortcall?.portAtdTime, `is`(portAtd))
        assertNull(updatedPortcall?.endTime)

        // trigger another port.atd event after 2 hours
        val updateAtd = fetchTimestamp(portAtd, 2, TimeUnit.DAYS)
        `when`(portcallDataSource.getNearestByImoAndDate(imo, port, updateAtd, fromDate, updateAtd, false, false))
            .thenReturn(updatedPortcall)

        updatedPortcall = portEventsHandler.processAtd(imo, port, updateAtd.time, fromDate)
        assertNotNull(updatedPortcall)
        assertThat(updatedPortcall?.status, `is`(PortcallStatus.OUTBOUND))
        // assert that portAtd time has not changed
        assertThat(updatedPortcall?.portAtdTime, `is`(portAtd))
        // but the endTime is updated with the most recent one
        assertNull(updatedPortcall?.endTime)

        val imo2 = ""
        val notUpdatedPortcall = portEventsHandler.processAtd(imo2, port, portAtd.time)
        assertNull(notUpdatedPortcall)
    }

    @Test
    fun `test if USCRP port is in list and ATA being updated`() {
        assertTrue(portEventsHandler.portEventsPorts.contains("USCRP"))
        assertFalse(portEventsHandler.portEventsPorts.contains("NLTEST"))

        val testEvent = objectMapper.readResource<AreaStartEvent>("portevents/ais-engine/event_start.json")
        val newDate = Date()
        `when`(
            portcallDataSource.getNearestByImoAndDate(
                eq("123"),
                eq("USCRP"),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                eq(false),
                anyOrNull(),
            ),
        )
            .thenReturn(
                Portcall("x", emptySet(), "USCRP", "123", ScheduledTaskType.UNKNOWN, newDate, UpdateType.PORT, PortcallStatus.INBOUND),
            )
        `when`(csiService.getShipByMmsi(anyString())).thenReturn(getShipRegister(mmsi = "367534690", name = "test ship", imo = "123"))
        assertDoesNotThrow { portEventsHandler.onMessage(testEvent) }
    }

    @Test
    fun `test if USCRP port is in list and ATD being updated`() {
        assertTrue(portEventsHandler.portEventsPorts.contains("USCRP"))
        assertFalse(portEventsHandler.portEventsPorts.contains("NLTEST"))

        val testEvent = objectMapper.readResource<AreaStartEvent>("portevents/ais-engine/event_end.json")
        val portAtd = Date()
        val fromDate = fetchTimestamp(Date(), -1, TimeUnit.DAYS)
        `when`(
            portcallDataSource.getNearestByImoAndDate(
                eq("123"),
                eq("USCRP"),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                eq(false),
                anyOrNull(),
            ),
        )
            .thenReturn(
                Portcall("x", emptySet(), "USCRP", "123", ScheduledTaskType.UNKNOWN, portAtd, UpdateType.PORT, PortcallStatus.INBOUND),
            )
        `when`(csiService.getShipByMmsi(anyString())).thenReturn(getShipRegister(mmsi = "367534690", name = "test ship", imo = "123"))
        assertDoesNotThrow { portEventsHandler.onMessage(testEvent) }
    }

    @Test
    fun `test if portEvent message is not processed`() {
        assertFalse(portEventsHandler.portEventsPorts.contains("BRTUB"))

        val testEvent = objectMapper.readResource<AreaStartEvent>("portevents/ais-engine/event_test.json")

        val imo = "12"
        val port = "BRTUB"
        val portAtd = Date()
        val portEventsQueueHandlerMock = spy(portEventsHandler)
        assertDoesNotThrow { portEventsQueueHandlerMock.onMessage(testEvent) }
        verify(portEventsQueueHandlerMock, never()).processAtd(imo, port, 1680159299000)
        verify(portEventsQueueHandlerMock, never()).processAtd(imo, port, 1680159299000, portAtd)
    }
}

inline fun <reified T> ObjectMapper.readResource(filename: String): T {
    val url = javaClass.classLoader.getResource(filename) ?: Assertions.fail("can't load $filename")
    return readValue<T>(url)
}
