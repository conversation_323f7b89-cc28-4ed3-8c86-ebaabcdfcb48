package nl.teqplay.portcallplus.service.internal

import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.Portcall.Companion.IDPREFIX_ANR
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.utils.TestHelper
import nl.teqplay.portcallplus.utils.mapper
import nl.teqplay.skeleton.common.config.NatsProperties
import nl.teqplay.skeleton.nats.NatsClientBuilder
import nl.teqplay.skeleton.nats.NatsClientMock
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.clearInvocations
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.Duration
import java.time.Instant
import java.time.YearMonth
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit
import java.util.Date
import java.util.stream.Stream
import nl.teqplay.platform.model.Portcall as PlatformPortcall

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class NatsRequestReplyServiceTest {
    private val testPortcallEventTime = YearMonth.now()
        .atDay(1)
        .atStartOfDay()
        .toInstant(ZoneOffset.UTC)

    private val natsClientBuilder = mock<NatsClientBuilder>()
    private val config = mock<NatsProperties>()
    private val platformService = mock<PlatformService>()
    private val portcallDataSource = mock<PortcallDataSource>()

    private val natsRequestReplyService = NatsRequestReplyService(
        natsClientBuilder,
        config,
        mapper,
        platformService,
        portcallDataSource,
    )

    private val natsClientMock = NatsClientMock()
    private val requestReplyContext = natsClientMock.requestReply("portreporter-monitor", NatsClientMock.metricRegistry)

    private val expectedPortcallId = "portcallId"
    private val mockedPortcall = mock<Portcall>().apply {
        whenever(portcallId).thenReturn(expectedPortcallId)
    }
    private val expectedPlatformPortcallId = "platformPortcallId"
    private val mockedPlatformPortcall = mock<PlatformPortcall>().apply {
        whenever(portcallId).thenReturn(expectedPlatformPortcallId)
    }

    private val subject = "portreporter-monitor.request.v1.identifiers"

    data class RequestReplyTestData(
        val message: String,
        val request: Any,
        val mock: () -> Unit = {},
        val output: NatsRequestReplyService.Metadata?,
    )

    @ParameterizedTest
    @MethodSource("requestReplyTestData")
    fun `request-reply`(data: RequestReplyTestData) {
        clearInvocations(natsClientBuilder, config, portcallDataSource)
        whenever(natsClientBuilder.requestReply(any(), any())).thenReturn(requestReplyContext)
        natsRequestReplyService.init()
        verify(natsClientBuilder, times(1)).requestReply(any(), eq("portcallplus"))

        data.mock()
        val response = requestReplyContext.requestOne(
            subject = subject,
            timeout = Duration.ZERO,
            request = data.request,
            headers = null,
            serializer = mapper::writeValueAsBytes,
            deserializer = { bytes, _ -> mapper.readValue<NatsRequestReplyService.Metadata>(bytes) },
        )
        assertEquals(data.output, response, data.message)
    }

    private fun requestReplyTestData() =
        Stream.of(
            RequestReplyTestData(
                message = "empty input, empty output",
                request = NatsRequestReplyService.RequestMetadataByImoAndPort(
                    imo = null,
                    port = null,
                    time = null,
                    from = null,
                    to = null,
                    allowFuturePortcalls = null,
                ),
                output = NatsRequestReplyService.Metadata(null),
            ),
            RequestReplyTestData(
                message = "invalid input, no output",
                request = "random",
                output = null,
            ),
            RequestReplyTestData(
                message = "only IMO, empty output",
                request = NatsRequestReplyService.RequestMetadataByImoAndPort(
                    imo = "imo",
                    port = null,
                    time = null,
                    from = null,
                    to = null,
                    allowFuturePortcalls = null,
                ),
                output = NatsRequestReplyService.Metadata(null),
            ),
            RequestReplyTestData(
                message = "IMO+time, returns ID",
                request = NatsRequestReplyService.RequestMetadataByImoAndPort(
                    imo = "imo",
                    port = null,
                    time = testPortcallEventTime,
                    from = null,
                    to = null,
                    allowFuturePortcalls = true,
                ),
                mock = {
                    whenever(
                        portcallDataSource.getNearestByImoAndDate(
                            shipImo = eq("imo"),
                            port = anyOrNull(),
                            estimatedTime = eq(Date.from(testPortcallEventTime)),
                            // both relative to estimatedTime
                            intervalFrom = eq(Date.from(testPortcallEventTime.minus(365, ChronoUnit.DAYS))),
                            intervalTo = eq(null),
                            finished = eq(false),
                            allowFuturePortcalls = eq(true),
                        ),
                    ).thenReturn(mockedPortcall)
                },
                output = NatsRequestReplyService.Metadata(portcallId = expectedPortcallId),
            ),
            RequestReplyTestData(
                message = "IMO+time+port, returns ID",
                request = NatsRequestReplyService.RequestMetadataByImoAndPort(
                    imo = "imo",
                    port = "USHOU",
                    time = testPortcallEventTime,
                    from = null,
                    to = null,
                    allowFuturePortcalls = true,
                ),
                mock = {
                    whenever(
                        portcallDataSource.getNearestByImoAndDate(
                            shipImo = eq("imo"),
                            port = eq("USHOU"),
                            estimatedTime = eq(Date.from(testPortcallEventTime)),
                            // both relative to estimatedTime
                            intervalFrom = eq(Date.from(testPortcallEventTime.minus(365, ChronoUnit.DAYS))),
                            intervalTo = eq(null),
                            finished = eq(false),
                            allowFuturePortcalls = eq(true),
                        ),
                    ).thenReturn(mockedPortcall)
                },
                output = NatsRequestReplyService.Metadata(portcallId = expectedPortcallId),
            ),
            RequestReplyTestData(
                message = "IMO+time+port (NLRTM), returns ID via platform",
                request = NatsRequestReplyService.RequestMetadataByImoAndPort(
                    imo = "imo",
                    port = "NLRTM",
                    time = testPortcallEventTime,
                    from = null,
                    to = null,
                    allowFuturePortcalls = true,
                ),
                mock = {
                    whenever(
                        platformService.getCurrentPortcallByImo(
                            imo = eq("imo"),
                            port = eq("NLRTM"),
                        ),
                    ).thenReturn(mockedPlatformPortcall)
                },
                output = NatsRequestReplyService.Metadata(portcallId = expectedPlatformPortcallId),
            ),
            RequestReplyTestData(
                message = "IMO+time+port (NLAMS), returns ID via platform",
                request = NatsRequestReplyService.RequestMetadataByImoAndPort(
                    imo = "imo",
                    port = "NLAMS",
                    time = testPortcallEventTime,
                    from = null,
                    to = null,
                    allowFuturePortcalls = true,
                ),
                mock = {
                    whenever(
                        platformService.getCurrentPortcallByImo(
                            imo = eq("imo"),
                            port = eq("NLAMS"),
                        ),
                    ).thenReturn(mockedPlatformPortcall)
                },
                output = NatsRequestReplyService.Metadata(portcallId = expectedPlatformPortcallId),
            ),
            RequestReplyTestData(
                message = "no active portcall, find finished",
                request = NatsRequestReplyService.RequestMetadataByImoAndPort(
                    imo = "imo",
                    port = "USHOU",
                    time = testPortcallEventTime,
                    from = null,
                    to = null,
                    allowFuturePortcalls = true,
                ),
                mock = {
                    whenever(
                        portcallDataSource.getNearestByImoAndDate(
                            any(),
                            anyOrNull(),
                            anyOrNull(),
                            anyOrNull(),
                            anyOrNull(),
                            anyOrNull(),
                            any(),
                        ),
                    )
                        .thenReturn(null)
                    whenever(
                        portcallDataSource.getNearestByImoAndDate(
                            shipImo = eq("imo"),
                            port = eq("USHOU"),
                            estimatedTime = eq(Date.from(testPortcallEventTime)),
                            intervalFrom = eq(Date.from(testPortcallEventTime.minus(Duration.ofDays(30)))),
                            intervalTo = eq(Date.from(testPortcallEventTime)),
                            finished = eq(true),
                            allowFuturePortcalls = eq(true),
                        ),
                    ).thenReturn(mockedPortcall)
                },
                output = NatsRequestReplyService.Metadata(portcallId = expectedPortcallId),
            ),
        )

    private fun portcallTimes() =
        Stream.of(
            // Already ongoing portcall normal behaviour
            Arguments.of(
                Instant.now().minus(10, ChronoUnit.DAYS),
                Instant.now().minus(1, ChronoUnit.DAYS),
                Instant.now().plus(10, ChronoUnit.DAYS),
            ),
            // Ongoing portcall we only know future start time of berth visit
            Arguments.of(
                Instant.now().minus(10, ChronoUnit.DAYS),
                Instant.now().plus(1, ChronoUnit.DAYS),
                Instant.now().plus(10, ChronoUnit.DAYS),
            ),
            // Long ongoing portcall where new portcall is closer in time but should match old portcall
            Arguments.of(
                Instant.now().minus(100, ChronoUnit.DAYS),
                Instant.now().minus(90, ChronoUnit.DAYS),
                Instant.now().plus(10, ChronoUnit.DAYS),
            ),
        )

    @ParameterizedTest
    @MethodSource("portcallTimes")
    fun `should return portcall id of current when getting portcall from database`(
        oldTime: Instant,
        currentTime: Instant,
        futureTime: Instant,
    ) {
        val oldPortcallStartTime = Date.from(oldTime)
        val currentPortcallStartTime = Date.from(currentTime)
        val futurePortcallStartTime = Date.from(futureTime)

        val oldPortcall = TestHelper.getTestPortcall(portcallId = "1", startTime = oldPortcallStartTime)
            .copy(portAtaTime = oldPortcallStartTime)
        val currentPortcall = TestHelper.getTestPortcall(portcallId = "2", startTime = currentPortcallStartTime)
            .copy(portAtaTime = currentPortcallStartTime)
        val futurePortcall = TestHelper.getTestPortcall(portcallId = "3", startTime = futurePortcallStartTime)

        whenever(
            portcallDataSource.getByFiltered(anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull()),
        )
            .thenReturn(sortedSetOf(oldPortcall, currentPortcall, futurePortcall))
        whenever(portcallDataSource.getNearestByImoAndDate(any(), anyOrNull(), any(), anyOrNull(), anyOrNull(), anyOrNull(), any()))
            .thenCallRealMethod()

        val now = Instant.now()

        val request = NatsRequestReplyService.RequestMetadataByImoAndPort(
            imo = "001",
            port = IDPREFIX_ANR,
            time = now,
            from = null,
            to = null,
            allowFuturePortcalls = false,
        )
        val result = natsRequestReplyService.findPortcall(imo = "001", port = IDPREFIX_ANR, now, request)
        val expected = currentPortcall.portcallId

        assertEquals(expected, result)
    }

    @Test
    fun `should return portcall id of ongoing when future portcall has no port ata`() {
        val currentPortcallStartTime = Date.from(Instant.now().minus(10, ChronoUnit.DAYS))
        val futurePortcallStartTime = Date.from(Instant.now().plus(1, ChronoUnit.DAYS))

        val currentPortcall = TestHelper.getTestPortcall(portcallId = "1", startTime = currentPortcallStartTime)
            .copy(portAtaTime = currentPortcallStartTime)
        val futurePortcall = TestHelper.getTestPortcall(portcallId = "2", startTime = futurePortcallStartTime)

        whenever(
            portcallDataSource.getByFiltered(anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull()),
        )
            .thenReturn(sortedSetOf(currentPortcall, futurePortcall))
        whenever(portcallDataSource.getNearestByImoAndDate(any(), anyOrNull(), any(), anyOrNull(), anyOrNull(), anyOrNull(), any()))
            .thenCallRealMethod()

        val now = Instant.now()

        val request = NatsRequestReplyService.RequestMetadataByImoAndPort(
            imo = "001",
            port = IDPREFIX_ANR,
            time = now,
            from = null,
            to = null,
            allowFuturePortcalls = false,
        )
        val result = natsRequestReplyService.findPortcall(imo = "001", port = IDPREFIX_ANR, now, request)
        val expected = currentPortcall.portcallId

        assertEquals(expected, result)
    }
}
