package nl.teqplay.portcallplus.service.internal

import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.shiphistory.model.AisCurrentMessage
import nl.teqplay.csi.model.ship.info.ShipRegisterInfo
import nl.teqplay.csi.model.ship.info.component.ShipAdministration
import nl.teqplay.csi.model.ship.info.component.ShipCommunication
import nl.teqplay.csi.model.ship.info.component.ShipDimensions
import nl.teqplay.csi.model.ship.info.component.ShipIdentifiers
import nl.teqplay.csi.model.ship.info.component.ShipRole
import nl.teqplay.csi.model.ship.info.component.ShipScores
import nl.teqplay.csi.model.ship.info.component.ShipSpecification
import nl.teqplay.csi.model.ship.info.component.ShipTypes
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.properties.PortCallEndFallbackProperties
import nl.teqplay.portcallplus.service.common.EventService
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.poma.client.PomaInfrastructureClient
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension
import java.time.Instant
import java.util.Date

@ExtendWith(MockitoExtension::class)
class PortcallEndFallbackTest {
    @Mock
    lateinit var portcallDataSource: PortcallDataSource

    @Mock
    lateinit var eventService: EventService

    @Mock
    lateinit var csiService: CsiService

    @Mock
    lateinit var platformService: PlatformService

    @Mock
    lateinit var pomaClient: PomaInfrastructureClient

    private val config = PortCallEndFallbackProperties(0)

    lateinit var portcallEndFallback: PortcallEndFallback

    @BeforeEach
    fun setup() {
        portcallEndFallback = PortcallEndFallback(
            portcallDataSource, eventService, csiService, platformService, pomaClient, config,
        )
    }

    @Test
    fun `Given an old bunkership portcal that is older than 1 month, epect it to be closed`() {
        val unfineshedPortcall =
            Portcall(
                portcallId = "",
                port = "SGSIN",
                imo = "9859636",
                startTime = Date.from(Instant.now().minusSeconds(60 * 60 * 24 * 31L)),
            )
        val shipInfo = ShipRegisterInfo(
            identifiers = ShipIdentifiers(mmsi = "123456789", imo = null, callSign = null, eni = null, name = null),
            types = ShipTypes(
                role = ShipRole.BUNKER,
            ),
            dimensions = ShipDimensions(),
            administration = ShipAdministration(null, null, null, null, null, null, null, null, null),
            specification = ShipSpecification(),
            communication = ShipCommunication(
                null,
            ),
            scores = ShipScores(null, null),
        )

        val aisCurrentMessage = AisCurrentMessage(
            AisMessage(mmsi = 1, messageTime = Instant.now(), sources = setOf(""), location = Location(1.0, 1.0)),
            null,
        )

        `when`(csiService.getShipByImo(unfineshedPortcall.imo)).thenReturn(shipInfo)
        `when`(platformService.getCurrentByMmsi(shipInfo.identifiers.mmsi!!)).thenReturn(aisCurrentMessage)
        assertEquals(true, portcallEndFallback.shouldPortcallBeEnded(unfineshedPortcall))
    }
}
