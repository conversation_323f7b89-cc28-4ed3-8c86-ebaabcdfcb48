package nl.teqplay.portcallplus.service

import com.google.common.io.Resources
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.portcallplus.PreconditionException
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallStatus
import nl.teqplay.portcallplus.api.model.PortcallVisit
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.UpdateType
import nl.teqplay.portcallplus.datasource.ActivityDataSource
import nl.teqplay.portcallplus.datasource.EnigmaDataSource
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.datasource.ScheduledTaskRunDataSource
import nl.teqplay.portcallplus.datasource.ServiceFetchCounterDataSource
import nl.teqplay.portcallplus.model.ScheduledTaskTypeFieldsProxy
import nl.teqplay.portcallplus.model.service.EnigmaTravel
import nl.teqplay.portcallplus.properties.EnigmaProperties
import nl.teqplay.portcallplus.service.external.ENIGMA_DATE_FORMAT
import nl.teqplay.portcallplus.service.external.ENIGMA_TIMEZONE
import nl.teqplay.portcallplus.service.external.EnigmaScraperService
import nl.teqplay.portcallplus.service.internal.PoMaService
import nl.teqplay.portcallplus.utils.fetchTimestamp
import nl.teqplay.skeleton.nats.NatsProducerStream
import org.jsoup.Jsoup
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.ArgumentMatchers.anyBoolean
import org.mockito.ArgumentMatchers.anyString
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import java.io.File
import java.time.Duration
import java.util.concurrent.TimeUnit

@ExtendWith(MockitoExtension::class)
internal class EnigmaScraperServiceTest {
    @Mock
    lateinit var enigmaDataSource: EnigmaDataSource

    @Mock
    lateinit var serviceFetchCounterDataSource: ServiceFetchCounterDataSource

    @Mock
    lateinit var scheduledTaskRunDataSource: ScheduledTaskRunDataSource

    @Mock
    lateinit var activityDataSource: ActivityDataSource

    @Mock
    lateinit var portcallDataSource: PortcallDataSource

    @Mock
    private lateinit var scheduledTaskTypeFieldsProxy: ScheduledTaskTypeFieldsProxy

    @Bean
    @Primary
    fun eventStreamService(producer: NatsProducerStream<Event>): EventStreamService = mock()

    private val config = EnigmaProperties(false, false, Duration.ZERO, 0L)

    lateinit var enigmaScraperService: EnigmaScraperService

    private val pomaServiceMock = mock<PoMaService>()

    @BeforeEach
    fun before() {
        whenever(pomaServiceMock.getBerths(any(), anyOrNull())).thenReturn(emptyArray())
        enigmaScraperService = EnigmaScraperService(
            enigmaDataSource, scheduledTaskRunDataSource, activityDataSource,
            serviceFetchCounterDataSource, portcallDataSource, config, scheduledTaskTypeFieldsProxy, mock(), pomaServiceMock, mock(),
        )
    }

    /**
     * Test the fetchTravels of EnigmaScraperService
     * The document contains duplicate ships for now we overwrite them with the latest in the list
     */
    @Test
    fun testFetchTravels() {
        val document = Jsoup.parse(
            File(Resources.getResource("enigma/opvaarten.html").file),
            "UTF-8",
            "https://lis.loodswezen.be/Lis/VerwachteReizen.aspx",
        )
        assertTrue(document != null)
        var enigmaUpdates = enigmaScraperService.fetchTravels(document, true)
        assertEquals(76, enigmaUpdates.size)
        assertEquals("01-10-2019 12:24", enigmaUpdates.first { it.shipImo == "9718777" }.orderTime)

        enigmaUpdates = enigmaScraperService.fetchTravels(document, true)
        assertEquals(76, enigmaUpdates.size)
    }

    /**
     * Test scenario where
     * a EnigmaTravels comes in and no portcall exists yet
     */
    @Test
    fun convertToIncomingPortcallTest() {
        val imo = "9254687"
        val date = "07-01-2020 15:40"
        val serviceModel = EnigmaTravel(
            "Immingham (GB)",
            "Terneuzen (NL)",
            "B GAS NEPTUNE",
            imo,
            date,
            "BRBW",
            "Vopak Agencies Terneuzen",
            Portcall.IDPREFIX_TNZ,
        )
        val convertedDate = fetchTimestamp(serviceModel.orderTime, ENIGMA_DATE_FORMAT, ENIGMA_TIMEZONE)
        Mockito.`when`(
            portcallDataSource.getNearestByImoAndDateInterval(
                serviceModel.shipImo,
                serviceModel.port,
                fetchTimestamp(serviceModel.orderTime, ENIGMA_DATE_FORMAT, ENIGMA_TIMEZONE),
                enigmaScraperService.NEW_PORTCALL_INTERVAL,
            ),
        )
            .thenReturn(null)
        Mockito.`when`(
            portcallDataSource.getNearestByImoAndDate(
                anyString(),
                anyString(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyBoolean(),
                anyBoolean(),
            ),
        )
            .thenReturn(null)
        Mockito.`when`(portcallDataSource.generateId(anyString(), anyString(), anyOrNull()))
            .thenReturn("portcallId")
        val portcall = enigmaScraperService.convertToPortcall(serviceModel, imo, ScheduledTaskType.ENIGMA_SCRAPER_INCOMING)
        assertNotNull(portcall)
        assertEquals(imo, portcall.imo)
        assertEquals(1, portcall.visits.size)
        assertEquals(convertedDate, portcall.visits.first().berthEta)
        assertEquals(serviceModel.berth, portcall.visits.first().berthName)
    }

    /**
     * Test scenario where
     * a EnigmaTravels comes in and there exists a portcall with the same berth
     * should change the eta
     */
    @Test
    fun convertToIncomingPortcallWithExistingPortcallTest() {
        val imo = "9254687"
        val date = "07-01-2020 15:40"
        val serviceModel = EnigmaTravel(
            "Immingham (GB)",
            "Terneuzen (NL)",
            "B GAS NEPTUNE",
            imo,
            date,
            "BRBW",
            "Vopak Agencies Terneuzen",
            Portcall.IDPREFIX_TNZ,
        )
        val convertedDate = fetchTimestamp(serviceModel.orderTime, ENIGMA_DATE_FORMAT, ENIGMA_TIMEZONE)
        val startTime = fetchTimestamp(convertedDate, -1, TimeUnit.HOURS)
        val existingPortcall = Portcall(
            portcallId = "x",
            portcallAlias = emptySet(),
            port = Portcall.IDPREFIX_TNZ,
            imo = imo,
            source = ScheduledTaskType.ENIGMA_SCRAPER_INCOMING,
            startTime = startTime,
            startTimeType = UpdateType.BERTH,
            status = PortcallStatus.INBOUND,
            visits = listOf(
                PortcallVisit(
                    berthEta = startTime,
                    berthEtd = fetchTimestamp(startTime, 2, TimeUnit.HOURS),
                    berthName = "BRBW",
                ),
            ),
        )
        Mockito.`when`(
            portcallDataSource.getNearestByImoAndDateInterval(
                serviceModel.shipImo,
                serviceModel.port,
                fetchTimestamp(serviceModel.orderTime, ENIGMA_DATE_FORMAT, ENIGMA_TIMEZONE),
                enigmaScraperService.NEW_PORTCALL_INTERVAL,
            ),
        )
            .thenReturn(existingPortcall)
        Mockito.`when`(
            portcallDataSource.getNearestByImoAndDate(
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyBoolean(),
                anyBoolean(),
            ),
        )
            .thenReturn(null)
        val portcall = enigmaScraperService.convertToPortcall(serviceModel, imo, ScheduledTaskType.ENIGMA_SCRAPER_INCOMING)
        assertNotNull(portcall)
        assertEquals(imo, portcall.imo)
        assertEquals(1, portcall.visits.size)
        assertEquals(convertedDate, portcall.visits.first().berthEta)
        assertEquals(serviceModel.berth, portcall.visits.first().berthName)
        assertEquals(convertedDate, portcall.startTime)
    }

    /**
     * Test scenario where
     * a EnigmaTravels comes in and there exists a portcall with a different berth
     */
    @Test
    fun convertToIncomingPortcallWithExistingPortcallTest2() {
        val imo = "9254687"
        val date = "07-01-2020 15:40"
        val serviceModel = EnigmaTravel(
            "Immingham (GB)",
            "Terneuzen (NL)",
            "B GAS NEPTUNE",
            imo,
            date,
            "BRBW",
            "Vopak Agencies Terneuzen",
            Portcall.IDPREFIX_TNZ,
        )
        val convertedDate = fetchTimestamp(serviceModel.orderTime, ENIGMA_DATE_FORMAT, ENIGMA_TIMEZONE)
        val previousPortcallStartTime = fetchTimestamp(convertedDate, -1, TimeUnit.HOURS)
        val existingPortcall = Portcall(
            portcallId = "x",
            portcallAlias = emptySet(),
            port = Portcall.IDPREFIX_TNZ,
            imo = imo,
            source = ScheduledTaskType.ENIGMA_SCRAPER_INCOMING,
            startTime = previousPortcallStartTime,
            startTimeType = UpdateType.BERTH,
            status = PortcallStatus.INBOUND,
            visits = listOf(
                PortcallVisit(
                    berthEta = previousPortcallStartTime,
                    berthEtd = fetchTimestamp(previousPortcallStartTime, 2, TimeUnit.HOURS),
                    berthName = "AAAA",
                ),
            ),
        )
        Mockito.`when`(
            portcallDataSource.getNearestByImoAndDateInterval(
                serviceModel.shipImo,
                serviceModel.port,
                fetchTimestamp(serviceModel.orderTime, ENIGMA_DATE_FORMAT, ENIGMA_TIMEZONE),
                enigmaScraperService.NEW_PORTCALL_INTERVAL,
            ),
        )
            .thenReturn(existingPortcall)
        Mockito.`when`(
            portcallDataSource.getNearestByImoAndDate(
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyBoolean(),
                anyBoolean(),
            ),
        )
            .thenReturn(existingPortcall)
        val portcall = enigmaScraperService.convertToPortcall(serviceModel, imo, ScheduledTaskType.ENIGMA_SCRAPER_INCOMING)
        assertNotNull(portcall)
        assertEquals(imo, portcall.imo)
        assertEquals(2, portcall.visits.size)
        assertEquals(convertedDate, portcall.visits.last().berthEta)
        assertEquals(serviceModel.berth, portcall.visits.last().berthName)
        assertEquals(existingPortcall.startTime, portcall.startTime)
    }

    /**
     * Test scenario where
     * a EnigmaTravels comes in and there exists a portcall with the same berth and the ship is already in the port
     */
    @Test
    fun convertToIncomingPortcallWithExistingPortcallTest3() {
        val imo = "9254687"
        val date = "07-01-2020 15:40"
        val serviceModel = EnigmaTravel(
            "Immingham (GB)",
            "Terneuzen (NL)",
            "B GAS NEPTUNE",
            imo,
            date,
            "BRBW",
            "Vopak Agencies Terneuzen",
            Portcall.IDPREFIX_TNZ,
        )
        val convertedDate = fetchTimestamp(serviceModel.orderTime, ENIGMA_DATE_FORMAT, ENIGMA_TIMEZONE)
        val previousPortcallStartTime = fetchTimestamp(convertedDate, -1, TimeUnit.HOURS)
        val portAta = fetchTimestamp(previousPortcallStartTime, -1, TimeUnit.HOURS)
        val existingPortcall = Portcall(
            portcallId = "testPortcallId",
            portcallAlias = emptySet(),
            port = Portcall.IDPREFIX_TNZ,
            imo = imo,
            source = ScheduledTaskType.ENIGMA_SCRAPER_INCOMING,
            startTime = previousPortcallStartTime,
            startTimeType = UpdateType.BERTH,
            status = PortcallStatus.INBOUND,
            portAtaTime = portAta,
            visits = listOf(
                PortcallVisit(
                    berthEta = previousPortcallStartTime,
                    berthEtd = fetchTimestamp(previousPortcallStartTime, 2, TimeUnit.HOURS),
                    berthName = "BRBW",
                ),
            ),
        )
        Mockito.`when`(
            portcallDataSource.getNearestByImoAndDate(
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyBoolean(),
                anyBoolean(),
            ),
        )
            .thenReturn(existingPortcall)

        assertEquals(1, existingPortcall.visits.size)
        assertEquals(previousPortcallStartTime, existingPortcall.visits.last().berthEta)
        assertEquals(serviceModel.berth, existingPortcall.visits.last().berthName)

        // should not return a portcall.
        // The current portcall of 9254687 already has a visit with BRBW in it, postponing adding the visit till B GAS NEPTUNE leaves NLTNZ
        org.assertj.core.api.Assertions.assertThatExceptionOfType(PreconditionException::class.java)
            .isThrownBy { enigmaScraperService.convertToPortcall(serviceModel, imo, ScheduledTaskType.ENIGMA_SCRAPER_INCOMING) }
            .withMessage(
                "[${existingPortcall.portcallId}]:Portcall of ${existingPortcall.imo} " +
                    "already has a visit with BRBW in it, postponing adding the visit till B GAS NEPTUNE leaves NLTNZ",
            )
    }

    /**
     * Test scenario where
     * a EnigmaTravels comes in and there exists a portcall with a different berth and the ship is already in the port
     */
    @Test
    fun convertToIncomingPortcallWithExistingPortcallTest4() {
        val imo = "9254687"
        val date = "07-01-2020 15:40"
        val serviceModel = EnigmaTravel(
            "Immingham (GB)",
            "Terneuzen (NL)",
            "B GAS NEPTUNE",
            imo,
            date,
            "BRBW",
            "Vopak Agencies Terneuzen",
            Portcall.IDPREFIX_TNZ,
        )
        val convertedDate = fetchTimestamp(serviceModel.orderTime, ENIGMA_DATE_FORMAT, ENIGMA_TIMEZONE)
        val previousPortcallStartTime = fetchTimestamp(convertedDate, -1, TimeUnit.HOURS)
        val portAta = fetchTimestamp(previousPortcallStartTime, -1, TimeUnit.HOURS)
        val existingPortcall = Portcall(
            portcallId = "x",
            portcallAlias = emptySet(),
            port = Portcall.IDPREFIX_TNZ,
            imo = imo,
            source = ScheduledTaskType.ENIGMA_SCRAPER_INCOMING,
            startTime = previousPortcallStartTime,
            startTimeType = UpdateType.BERTH,
            status = PortcallStatus.INBOUND,
            portAtaTime = portAta,
            visits = listOf(
                PortcallVisit(
                    berthEta = previousPortcallStartTime,
                    berthEtd = fetchTimestamp(previousPortcallStartTime, 2, TimeUnit.HOURS),
                    berthName = "AAAA",
                ),
            ),
        )
        Mockito.`when`(
            portcallDataSource.getNearestByImoAndDate(
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyBoolean(),
                anyBoolean(),
            ),
        )
            .thenReturn(existingPortcall)

        val portcall = enigmaScraperService.convertToPortcall(serviceModel, imo, ScheduledTaskType.ENIGMA_SCRAPER_INCOMING)
        assertNotNull(portcall)
        assertEquals(2, portcall.visits.size)
        assertEquals(convertedDate, portcall.visits.last().berthEta)
        assertEquals(serviceModel.berth, portcall.visits.last().berthName)
    }

    /**
     * Test scenario where
     * a EnigmaTravels comes in of a berth that is next in the visits (Shifting) while the ship is in the port
     */
    @Test
    fun convertToIncomingPortcallWithExistingPortcallTest5() {
        val imo = "9254687"
        val date = "07-01-2020 15:40"
        val serviceModel = EnigmaTravel(
            "Immingham (GB)",
            "Terneuzen (NL)",
            "B GAS NEPTUNE",
            imo,
            date,
            "BRBW",
            "Vopak Agencies Terneuzen",
            Portcall.IDPREFIX_TNZ,
        )
        val convertedDate = fetchTimestamp(serviceModel.orderTime, ENIGMA_DATE_FORMAT, ENIGMA_TIMEZONE)
        val previousPortcallStartTime = fetchTimestamp(convertedDate, -1, TimeUnit.HOURS)
        val portAta = fetchTimestamp(previousPortcallStartTime, -1, TimeUnit.HOURS)
        val existingPortcall = Portcall(
            portcallId = "x",
            portcallAlias = emptySet(),
            port = Portcall.IDPREFIX_TNZ,
            imo = imo,
            source = ScheduledTaskType.ENIGMA_SCRAPER_INCOMING,
            startTime = previousPortcallStartTime,
            startTimeType = UpdateType.BERTH,
            status = PortcallStatus.INBOUND,
            portAtaTime = portAta,
            visits = listOf(
                PortcallVisit(
                    berthEta = previousPortcallStartTime,
                    berthEtd = fetchTimestamp(previousPortcallStartTime, 1, TimeUnit.HOURS),
                    berthName = "AAAA",
                ),
                PortcallVisit(
                    berthEta = fetchTimestamp(previousPortcallStartTime, 65, TimeUnit.MINUTES),
                    berthName = "BRBW",
                ),
            ),
        )
        Mockito.`when`(
            portcallDataSource.getNearestByImoAndDate(
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyBoolean(),
                anyBoolean(),
            ),
        )
            .thenReturn(existingPortcall)

        val portcall = enigmaScraperService.convertToPortcall(serviceModel, imo, ScheduledTaskType.ENIGMA_SCRAPER_INCOMING)
        assertNotNull(portcall)
        assertEquals(2, portcall.visits.size)
        assertEquals(convertedDate, portcall.visits.last().berthEta)
        assertEquals(serviceModel.berth, portcall.visits.last().berthName)
    }

    /**
     * Test scenario where
     * a EnigmaTravels comes in of a berth that is next in the visits (Shifting) while the ship is in the port and it has another visit after
     */
    @Test
    fun convertToIncomingPortcallWithExistingPortcallTest6() {
        val imo = "9254687"
        val date = "07-01-2020 15:40"
        val serviceModel = EnigmaTravel(
            "Immingham (GB)",
            "Terneuzen (NL)",
            "B GAS NEPTUNE",
            imo,
            date,
            "BBBB",
            "Vopak Agencies Terneuzen",
            Portcall.IDPREFIX_TNZ,
        )
        val convertedDate = fetchTimestamp(serviceModel.orderTime, ENIGMA_DATE_FORMAT, ENIGMA_TIMEZONE)
        val previousPortcallStartTime = fetchTimestamp(convertedDate, -1, TimeUnit.HOURS)
        val portAta = fetchTimestamp(previousPortcallStartTime, -1, TimeUnit.HOURS)
        val existingPortcall = Portcall(
            portcallId = "x",
            portcallAlias = emptySet(),
            port = Portcall.IDPREFIX_TNZ,
            imo = imo,
            source = ScheduledTaskType.ENIGMA_SCRAPER_INCOMING,
            startTime = previousPortcallStartTime,
            startTimeType = UpdateType.BERTH,
            status = PortcallStatus.INBOUND,
            portAtaTime = portAta,
            visits = listOf(
                PortcallVisit(
                    berthEta = previousPortcallStartTime,
                    berthEtd = fetchTimestamp(previousPortcallStartTime, 20, TimeUnit.MINUTES),
                    berthName = "AAAA",
                ),
                PortcallVisit(
                    berthEta = fetchTimestamp(previousPortcallStartTime, 65, TimeUnit.MINUTES),
                    berthName = "BBBB",
                ),
                PortcallVisit(
                    berthEta = fetchTimestamp(previousPortcallStartTime, 5, TimeUnit.HOURS),
                    berthEtd = fetchTimestamp(previousPortcallStartTime, 6, TimeUnit.HOURS),
                    berthName = "CCCC",
                ),
            ),
        )
        Mockito.`when`(
            portcallDataSource.getNearestByImoAndDate(
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyBoolean(),
                anyBoolean(),
            ),
        )
            .thenReturn(existingPortcall)

        val portcall = enigmaScraperService.convertToPortcall(serviceModel, imo, ScheduledTaskType.ENIGMA_SCRAPER_INCOMING)
        assertNotNull(portcall)
        assertEquals(3, portcall.visits.size)
        assertEquals(convertedDate, portcall.visits[1].berthEta)
        assertEquals(serviceModel.berth, portcall.visits[1].berthName)
    }

    /**
     * Test scenario where
     * a EnigmaTravels comes is outgoing and there is no portcall yet
     */
    @Test
    fun convertToOutgoingPortcall() {
        val imo = "9254687"
        val date = "07-01-2020 15:40"
        val serviceModel = EnigmaTravel(
            "Immingham (GB)",
            "Terneuzen (NL)",
            "B GAS NEPTUNE",
            imo,
            date,
            "BRBW",
            "Vopak Agencies Terneuzen",
            Portcall.IDPREFIX_TNZ,
        )
        Mockito.`when`(
            portcallDataSource.getNearestByImoAndDate(
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyBoolean(),
                anyBoolean(),
            ),
        )
            .thenReturn(null)

        // should not return a portcall as no portcall should be found for an OUTBOUND travel
        org.assertj.core.api.Assertions.assertThatExceptionOfType(PreconditionException::class.java)
            .isThrownBy { enigmaScraperService.convertToPortcall(serviceModel, imo, ScheduledTaskType.ENIGMA_SCRAPER_OUTGOING) }
            .withMessage("[$imo]:No portcall or no change in eventTime. Ignoring etd ${serviceModel.orderTime}")
    }

    /**
     * Test scenario where
     * a EnigmaTravels comes is outgoing and there is a portcall with a different berth
     */
    @Test
    fun convertToOutgoingPortcall2() {
        val imo = "9254687"
        val date = "07-01-2020 15:40"
        val serviceModel = EnigmaTravel(
            "Immingham (GB)",
            "Terneuzen (NL)",
            "B GAS NEPTUNE",
            imo,
            date,
            "BRBW",
            "Vopak Agencies Terneuzen",
            Portcall.IDPREFIX_TNZ,
        )

        val convertedDate = fetchTimestamp(serviceModel.orderTime, ENIGMA_DATE_FORMAT, ENIGMA_TIMEZONE)
        val previousPortcallStartTime = fetchTimestamp(convertedDate, -1, TimeUnit.HOURS)
        val existingPortcall = Portcall(
            portcallId = "x",
            portcallAlias = emptySet(),
            port = Portcall.IDPREFIX_TNZ,
            imo = imo,
            source = ScheduledTaskType.ENIGMA_SCRAPER_OUTGOING,
            startTime = previousPortcallStartTime,
            startTimeType = UpdateType.BERTH,
            status = PortcallStatus.INBOUND,
            visits = listOf(PortcallVisit(berthEta = previousPortcallStartTime, berthName = "AAAA")),
        )
        Mockito.`when`(
            portcallDataSource.getNearestByImoAndDate(
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyBoolean(),
                anyBoolean(),
            ),
        )
            .thenReturn(existingPortcall)

        val portcall = enigmaScraperService.convertToPortcall(serviceModel, imo, ScheduledTaskType.ENIGMA_SCRAPER_OUTGOING)
        assertNotNull(portcall)
        assertEquals(2, portcall.visits.size)
        assertNull(portcall.visits.first().berthEtd)
    }

    /**
     * Test scenario where
     * a EnigmaTravels comes is outgoing and there is a portcall with the same berth
     */
    @Test
    fun convertToOutgoingPortcall3() {
        val imo = "9254687"
        val date = "07-01-2020 15:40"
        val serviceModel = EnigmaTravel(
            "Immingham (GB)",
            "Terneuzen (NL)",
            "B GAS NEPTUNE",
            imo,
            date,
            "BRBW",
            "Vopak Agencies Terneuzen",
            Portcall.IDPREFIX_TNZ,
        )

        val convertedDate = fetchTimestamp(serviceModel.orderTime, ENIGMA_DATE_FORMAT, ENIGMA_TIMEZONE)
        val previousPortcallStartTime = fetchTimestamp(convertedDate, -1, TimeUnit.HOURS)
        val existingPortcall = Portcall(
            portcallId = "x",
            portcallAlias = emptySet(),
            port = Portcall.IDPREFIX_TNZ,
            imo = imo,
            source = ScheduledTaskType.ENIGMA_SCRAPER_OUTGOING,
            startTime = previousPortcallStartTime,
            startTimeType = UpdateType.BERTH,
            status = PortcallStatus.INBOUND,
            visits = listOf(PortcallVisit(berthEta = previousPortcallStartTime, berthName = "BRBW")),
        )
        Mockito.`when`(
            portcallDataSource.getNearestByImoAndDate(
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyBoolean(),
                anyBoolean(),
            ),
        )
            .thenReturn(existingPortcall)

        val portcall = enigmaScraperService.convertToPortcall(serviceModel, imo, ScheduledTaskType.ENIGMA_SCRAPER_OUTGOING)
        assertNotNull(portcall)
        assertEquals(1, portcall.visits.size)
        assertEquals(convertedDate, portcall.visits.first().berthEtd)
    }
}
