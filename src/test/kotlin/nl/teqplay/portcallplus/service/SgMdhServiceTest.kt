package nl.teqplay.portcallplus.service

import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import nl.teqplay.poma.api.v1.Berth
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallPurpose
import nl.teqplay.portcallplus.api.model.PortcallVisit
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.UpdateType
import nl.teqplay.portcallplus.datasource.ActivityDataSource
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.datasource.ScheduledTaskRunDataSource
import nl.teqplay.portcallplus.datasource.ServiceFetchCounterDataSource
import nl.teqplay.portcallplus.model.LocationMapping
import nl.teqplay.portcallplus.model.ScheduledTaskTypeFieldsProxy
import nl.teqplay.portcallplus.model.data.ServiceFetchCounter
import nl.teqplay.portcallplus.model.data.sgmdh.SgMdhVessel
import nl.teqplay.portcallplus.model.service.PortcallUpdateResult
import nl.teqplay.portcallplus.model.service.SgMdhArrivalDeclaration
import nl.teqplay.portcallplus.model.service.SgMdhDueToArrive
import nl.teqplay.portcallplus.model.service.SgMdhDueToDepart
import nl.teqplay.portcallplus.objectMapper
import nl.teqplay.portcallplus.properties.SgmdhProperties
import nl.teqplay.portcallplus.service.external.SgMdhService
import nl.teqplay.portcallplus.service.external.getVisitType
import nl.teqplay.portcallplus.service.internal.LocationMappingService
import nl.teqplay.portcallplus.service.internal.PoMaService
import nl.teqplay.portcallplus.utils.fetchTimestamp
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.`is`
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.Duration
import java.time.Instant
import java.util.Date
import java.util.concurrent.TimeUnit

@ExtendWith(MockitoExtension::class)
class SgMdhServiceTest {
    @Mock
    lateinit var serviceFetchCounterDataSource: ServiceFetchCounterDataSource

    @Mock
    lateinit var portcallDataSource: PortcallDataSource

    @Mock
    lateinit var scheduledTaskRunDataSource: ScheduledTaskRunDataSource

    @Mock
    lateinit var activityDataSource: ActivityDataSource

    @Mock
    private lateinit var scheduledTaskTypeFieldsProxy: ScheduledTaskTypeFieldsProxy

    private val config = SgmdhProperties(false, false, Duration.ZERO, "", 0, "", 0, 0L, 0L, 0L)

    private lateinit var sgMdhService: SgMdhService

    private val pomaServiceMock = mock<PoMaService>()

    val locationMappingServiceMock = mock<LocationMappingService>().apply {
        whenever(getOrCreateEmptyLocation(any(), any(), any())).thenAnswer {
                it ->
            LocationMapping(it.getArgument(0), it.getArgument(1), it.getArgument(2), null)
        }
        whenever(getPomaBerthForLocation(any())).thenReturn(null)
    }

    @BeforeEach
    fun before() {
        whenever(pomaServiceMock.getBerths(anyOrNull(), anyOrNull())).thenReturn(emptyArray<Berth>())
        sgMdhService = SgMdhService(
            scheduledTaskRunDataSource, activityDataSource, serviceFetchCounterDataSource,
            portcallDataSource, objectMapper, config, scheduledTaskTypeFieldsProxy, mock(), pomaServiceMock, mock(), locationMappingServiceMock,
        )
    }

    @Test
    fun createOrUpdateIncomingPortcallTest() {
        // sgsin ignores the millis, which makes our mocks unstable. So we ignore it too by converting the currentDate twice
        val startDate = getSgsinNormalizedCurrentTime()
        val vessel = SgMdhVessel("test", "", 123, "NL")

        `when`(portcallDataSource.generateId(Portcall.IDPREFIX_SGSIN, vessel.imoNumber.toString(), startDate))
            .thenReturn("id")

        // trigger a due to arrive message and assert that a new portcallId is generated
        val portcall1 = sgMdhService.convertToPortcall(
            SgMdhDueToArrive(
                vessel,
                sgMdhService.fetchSGSINTimestamp(startDate),
                "",
                "BERTH1",
            ),
            vessel.imoNumber.toString(),
            ScheduledTaskType.SG_MDH_DUE_TO_ARRIVE,
        )
        assertNotNull(portcall1)

        val updatedEta = fetchTimestamp(startDate, 1, TimeUnit.DAYS)

        // mock that our datasource should return the above portcall
        `when`(
            portcallDataSource.getNearestByImoAndDateInterval(
                vessel.imoNumber.toString(),
                Portcall.IDPREFIX_SGSIN,
                updatedEta,
                sgMdhService.NEW_PORTCALL_INTERVAL,
                null,
            ),
        ).thenReturn(portcall1)
        // trigger the call again with updated eta
        val portcall2 = sgMdhService.convertToPortcall(
            SgMdhDueToArrive(
                vessel,
                sgMdhService.fetchSGSINTimestamp(updatedEta),
                "",
                "BERTH1",
            ),
            vessel.imoNumber.toString(),
            ScheduledTaskType.SG_MDH_DUE_TO_ARRIVE,
        )

        assertNotNull(portcall1)
        assertNotNull(portcall2)
        assertThat(portcall2.portcallId, `is`(portcall1.portcallId))
        // check that the eta is updated in SGSIN format (ignoring milli seconds)
        assertThat(portcall2.startTime, `is`(updatedEta))

        // set the ata as one hour before the eta
        val ata = fetchTimestamp(updatedEta, -1, TimeUnit.HOURS)
        `when`(
            portcallDataSource.getNearestByImoAndDateInterval(
                vessel.imoNumber.toString(),
                Portcall.IDPREFIX_SGSIN,
                ata,
                sgMdhService.NEW_PORTCALL_INTERVAL,
                null,
            ),
        ).thenReturn(portcall2)

        // covert an arrival declaration event and check that it updates the existing portcall
        val portcall3 = sgMdhService.convertToPortcall(
            SgMdhArrivalDeclaration(
                vessel,
                "BERTH1",
                "",
                "N,Y,N,N,N,N,N,N,N,",
                "Vopak",
                sgMdhService.fetchSGSINTimestamp(ata),
            ),
            vessel.imoNumber.toString(),
            ScheduledTaskType.SG_MDH_VISIT_ARRIVAL_DECLARATION,
        )

        // check if the ata is updated and not new portcall is created
        assertNotNull(portcall3)
        assertThat(portcall2.portcallId, `is`(portcall3.portcallId))
        assertThat(portcall3.visits.first().berthAta, `is`(ata))
        assertThat(portcall3.vesselAgent, `is`("Vopak"))
        assertNotNull(portcall3.purpose)
        assertTrue(portcall3.purpose.contains(PortcallPurpose.PASSENGERS))
    }

    /**
     * Test to check if an SgMdhArrivalDeclaration without an existing portcall, creates a new portcall with
     * agent information included.
     */
    @Test
    fun createPortcallOnArrivalDeclaration() {
        val ata = getSgsinNormalizedCurrentTime()
        val vessel = SgMdhVessel("test", "", 123, "NL")
        // mock that no portcalls are returned
        `when`(
            portcallDataSource.getNearestByImoAndDateInterval(
                vessel.imoNumber.toString(),
                Portcall.IDPREFIX_SGSIN,
                ata,
                sgMdhService.NEW_PORTCALL_INTERVAL,
                null,
            ),
        ).thenReturn(null)
        `when`(portcallDataSource.generateId(Portcall.IDPREFIX_SGSIN, vessel.imoNumber.toString(), ata))
            .thenReturn("id")

        val portcall = sgMdhService.convertToPortcall(
            SgMdhArrivalDeclaration(
                vessel,
                "BERTH1",
                "",
                "",
                "Vopak",
                sgMdhService.fetchSGSINTimestamp(ata),
            ),
            vessel.imoNumber.toString(),
            ScheduledTaskType.SG_MDH_VISIT_ARRIVAL_DECLARATION,
        )
        assertNotNull(portcall)
        assertEquals("Vopak", portcall.vesselAgent, "Expected vessel agent to be Vopak")
    }

    /**
     * Validate correct locationNames are mapped to the correct update type.
     */
    @Test
    fun getCorrectVisitType() {
        val locationNamesMap: Map<String, UpdateType> = mapOf(
            "WESTERN PETRO ANCH B" to UpdateType.ANCHORAGE,
            "SGSIN_PILOTAREA_PEBGB" to UpdateType.PILOTBOARDINGPLACE,
            "Vopak Banyan OBV 6" to UpdateType.BERTH,
            "CHEVRON PENJURU TERM 2" to UpdateType.BERTH,
            "OIL TANKSTORE 6 EAST" to UpdateType.BERTH,
            "ESSO BERTH 5" to UpdateType.BERTH,
            "PASIR PANJANG BERTH 32" to UpdateType.BERTH,
        )

        locationNamesMap.forEach { it ->
            assertEquals(getVisitType(it.key), it.value)
        }
    }

    /**
     * SGSIN ignores the millis, which makes our mocks unstable. So we ignore it too by converting the currentDate twice
     */
    private fun getSgsinNormalizedCurrentTime(): Date {
        return sgMdhService.fetchSGSINTimestamp(sgMdhService.fetchSGSINTimestamp(Date()))
    }

    @Test
    fun `test if serviceFetchCounter is being updated`() {
        val portcallVisitOne = PortcallVisit(
            berthEta = Date.from(Instant.ofEpochMilli(1731765600000)),
            berthName = "test1",
        )
        val portcallVisitTwo = PortcallVisit(
            berthEta = Date.from(Instant.ofEpochMilli(1731765800000)),
            berthName = "test2",
        )
        val portcallVisitThree = PortcallVisit(
            berthEta = Date.from(Instant.ofEpochMilli(1731765900000)),
            berthName = "test3",
        )
        val currentVisits = listOf(
            portcallVisitOne,
            portcallVisitTwo,
            portcallVisitThree,
        )
        // Note: the berthEta is changed for the updated visits
        val updatedVisits = listOf(
            portcallVisitOne,
            portcallVisitThree.copy(berthEta = Date.from(Instant.ofEpochMilli(1731766000000))),
            portcallVisitTwo,
        )

        val currentPortcall = Portcall(
            portcallId = "testPortcallId",
            port = "testPort",
            imo = "testImo",
            startTime = Date.from(Instant.ofEpochMilli(1682899200000)),
            vesselAgent = "testAgent",
            visits = currentVisits,
        )
        val updatedPortcall = currentPortcall.copy(
            visits = updatedVisits,
        )

        val serviceFetchCounter = ServiceFetchCounter(timestamp = Date(), taskType = ScheduledTaskType.SG_MDH_VISIT_ARRIVAL_DECLARATION)
        assert(serviceFetchCounter.berthUpdate.isEmpty())
        sgMdhService.updateCounter(currentPortcall, updatedPortcall, Date(), serviceFetchCounter)
        assert(serviceFetchCounter.berthUpdate.isNotEmpty())
    }

    @Test
    fun `test if serviceFetchCounter is not updated when unsorted visits are the same `() {
        val portcallVisitOne = PortcallVisit(
            berthEta = Date.from(Instant.ofEpochMilli(1731765600000)),
            berthName = "test1",
        )
        val portcallVisitTwo = PortcallVisit(
            berthEta = Date.from(Instant.ofEpochMilli(1731765800000)),
            berthName = "test2",
        )
        val portcallVisitThree = PortcallVisit(
            berthEta = Date.from(Instant.ofEpochMilli(1731765900000)),
            berthName = "test3",
        )
        val currentVisits = listOf(
            portcallVisitOne,
            portcallVisitTwo,
            portcallVisitThree,
        )
        val updatedVisits = listOf(
            portcallVisitOne,
            portcallVisitThree,
            portcallVisitTwo,
        )

        val currentPortcall = Portcall(
            portcallId = "testPortcallId",
            port = "testPort",
            imo = "testImo",
            startTime = Date.from(Instant.ofEpochMilli(1682899200000)),
            vesselAgent = "testAgent",
            visits = currentVisits,
        )
        val updatedPortcall = currentPortcall.copy(
            visits = updatedVisits,
        )

        val serviceFetchCounter = ServiceFetchCounter(timestamp = Date(), taskType = ScheduledTaskType.SG_MDH_VISIT_ARRIVAL_DECLARATION)
        assert(serviceFetchCounter.berthUpdate.isEmpty())
        sgMdhService.updateCounter(currentPortcall, updatedPortcall, Date(), serviceFetchCounter)
        assert(serviceFetchCounter.berthUpdate.isEmpty())
    }

    @Test
    fun `test if serviceFetchCounter is not updated when visits are the same `() {
        val portcallVisitOne = PortcallVisit(
            berthEta = Date.from(Instant.ofEpochMilli(1731765600000)),
            berthName = "test1",
        )
        val portcallVisitTwo = PortcallVisit(
            berthEta = Date.from(Instant.ofEpochMilli(1731765800000)),
            berthName = "test2",
        )
        val portcallVisitThree = PortcallVisit(
            berthEta = Date.from(Instant.ofEpochMilli(1731765900000)),
            berthName = "test3",
        )
        val currentVisits = listOf(
            portcallVisitOne,
            portcallVisitTwo,
            portcallVisitThree,
        )

        val currentPortcall = Portcall(
            portcallId = "testPortcallId",
            port = "testPort",
            imo = "testImo",
            startTime = Date.from(Instant.ofEpochMilli(1682899200000)),
            vesselAgent = "testAgent",
            visits = currentVisits,
        )
        val updatedPortcall = currentPortcall.copy(
            visits = currentVisits,
        )

        val serviceFetchCounter = ServiceFetchCounter(timestamp = Date(), taskType = ScheduledTaskType.SG_MDH_VISIT_ARRIVAL_DECLARATION)
        assert(serviceFetchCounter.berthUpdate.isEmpty())
        sgMdhService.updateCounter(currentPortcall, updatedPortcall, Date(), serviceFetchCounter)
        assert(serviceFetchCounter.berthUpdate.isEmpty())
    }

    /**
     * Test to check if an SgMdhArrivalDeclaration after receiving the same input with the same berth ata that it would send out an event by checking
     * the updated portcall, in this case we'll be checking the etd that came from another thread update that is running the same time on the same portcall
     * This simulation is the same as in practice where we have multiple sub tasks of SG_MDH running updates on portcalls
     * There could be scenario's of the wrong subtask sending out an update event which is correct but it should've been the other subtask responsibility
     * See below for what subtask is responsible for what event
     * arrivals should only be sending out ata events out
     * dueToDepart should be responsible for sending out etd events
     * dueToArrive is responsible for sending out eta events
     */
    @Test
    fun runMultipleScheduledTaskOnSamePortCall() {
        val now = Instant.now()
        val ata = sgMdhService.fetchSGSINTimestamp(sgMdhService.fetchSGSINTimestamp(Date.from(now)))
        val etd = sgMdhService.fetchSGSINTimestamp(sgMdhService.fetchSGSINTimestamp(Date.from(now.plusSeconds(10))))
        val vessel = SgMdhVessel("test", "", 123, "NL")
        `when`(portcallDataSource.generateId(Portcall.IDPREFIX_SGSIN, vessel.imoNumber.toString(), ata))
            .thenReturn("id")
        // Create a portcall with an arrival declaration that we'll be updating
        val portcallExisting = sgMdhService.convertToPortcall(
            SgMdhArrivalDeclaration(
                vessel,
                "BERTH1",
                "",
                "",
                "Vopak",
                sgMdhService.fetchSGSINTimestamp(ata),
            ),
            vessel.imoNumber.toString(),
            ScheduledTaskType.SG_MDH_VISIT_ARRIVAL_DECLARATION,
        )
        `when`(
            portcallDataSource.getNearestByImoAndDateInterval(
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
            ),
        ).thenReturn(portcallExisting)
        runBlocking {
            launch {
                // Simulating scheduled task ArrivalDeclaration should update the ATA
                val portcall = sgMdhService.convertToPortcall(
                    SgMdhArrivalDeclaration(
                        vessel,
                        "BERTH1",
                        "",
                        "",
                        "Vopak",
                        sgMdhService.fetchSGSINTimestamp(ata),
                    ),
                    vessel.imoNumber.toString(),
                    ScheduledTaskType.SG_MDH_VISIT_ARRIVAL_DECLARATION,
                )
                // This confirms the convertedPortcall has no berthEtd
                assert(portcall.visits.first().berthEtd == null)
                // Delay it by 2 seconds as this should be enough time for another thread to update the same portcall
                delay(2000L)
                with(sgMdhService) {
                    val response: List<PortcallUpdateResult> = updatePortcall(listOf(portcall))
                    // Check if the portcall has a etd after
                    // This subtask is delayed by line 368 for 2 seconds, so next launch block at line 384 will run first
                    // sending out any events that this subtask should've been responsible for
                    val currentPortcall = response.first().currentPortcall
                    val updatedPortcall = response.first().updatedPortcall
                    assert(currentPortcall != null)
                    assert(currentPortcall?.visits?.first()?.berthEtd == etd)
                    assert(updatedPortcall?.visits?.first()?.berthEtd == null)
                    assert(currentPortcall?.portcallId == portcall.portcallId)
                    assert(updatedPortcall?.portcallId == portcall.portcallId)
                }
            }
            // DueToDepart updating same portcall at same time
            launch {
                // Simulating scheduled task DueToArrive and this should update the portcall with the ETD
                val portcallDueToDepart = sgMdhService.convertToPortcall(
                    SgMdhDueToDepart(
                        vessel,
                        sgMdhService.fetchSGSINTimestamp(etd),
                    ),
                    vessel.imoNumber.toString(),
                    ScheduledTaskType.SG_MDH_DUE_TO_DEPART,
                )
                assert(portcallDueToDepart.visits.first().berthEtd == etd)
                `when`(portcallDataSource.get(listOf(portcallDueToDepart.portcallId)))
                    .thenReturn(listOf(portcallDueToDepart))
            }
        }
    }
}
