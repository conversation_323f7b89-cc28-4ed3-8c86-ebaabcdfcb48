package nl.teqplay.portcallplus.service

import nl.teqplay.poma.api.v1.Berth
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.model.LocationMapping
import nl.teqplay.portcallplus.model.ScheduledTaskTypeFieldsProxy
import nl.teqplay.portcallplus.model.data.sgmdh.SgMdhVessel
import nl.teqplay.portcallplus.model.service.SgMdhDueToArrive
import nl.teqplay.portcallplus.objectMapper
import nl.teqplay.portcallplus.properties.DigitrafficProperties
import nl.teqplay.portcallplus.service.external.DigitrafficService
import nl.teqplay.portcallplus.service.external.SgMdhService
import nl.teqplay.portcallplus.service.internal.LocationMappingService
import nl.teqplay.portcallplus.service.internal.PoMaService
import nl.teqplay.portcallplus.utils.fetchMillisTrimmedTimestamp
import nl.teqplay.portcallplus.utils.fetchTimestamp
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.`is`
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.Duration
import java.util.Date
import java.util.concurrent.TimeUnit

/**
 * Intention of this case is to test services which update the same portcall.
 * E.g. [HarborLightsService] and [DigitrafficService] both running in USHOU
 */
@ExtendWith(MockitoExtension::class)
class CrossServiceIT {
    @Mock
    lateinit var portcallDataSource: PortcallDataSource

    private val config = DigitrafficProperties(
        enable = true,
        allowOtherSourcePortcallUpdate = true,
        url = "",
        cycleDuration = Duration.ofDays(1),
        lastPortcallUpdate = Duration.ofDays(1),
        newPortcallInterval = 0L,
    )

    private val sgmdhConfig = nl.teqplay.portcallplus.properties.SgmdhProperties(
        enable = true,
        allowOtherSourcePortcallUpdate = true,
        cycleDuration = Duration.ofDays(1),
        token = "dummy-token",
        dueToArriveData = 1,
        arrivalDeclarationPastNHours = "1",
        dueToDepartData = 1,
        newPortcallInterval = 0L,
        portcallExitInterval = 0L,
        ferryPortcallInterval = 0L,
    )

    private lateinit var digitrafficService: DigitrafficService
    private lateinit var sgMdhService: SgMdhService

    @Mock
    private lateinit var scheduledTaskTypeFieldsProxy: ScheduledTaskTypeFieldsProxy

    private val pomaServiceMock = mock<PoMaService> {
        whenever(it.getBerths(anyOrNull(), anyOrNull())).thenReturn(emptyArray<Berth>())
    }

    @BeforeEach
    fun before() {
        val locationMappingServiceMock = mock<LocationMappingService>().apply {
            whenever(getOrCreateEmptyLocation(any(), any(), any())).thenAnswer { it -> LocationMapping(it.getArgument(0), it.getArgument(1), it.getArgument(2), null) }
        }

        digitrafficService = DigitrafficService(
            mock(), mock(), mock(), portcallDataSource, mock(),
            objectMapper, config, scheduledTaskTypeFieldsProxy, mock(), pomaServiceMock, mock(),
        )
        sgMdhService = SgMdhService(
            mock(), mock(), mock(), portcallDataSource, objectMapper, sgmdhConfig, scheduledTaskTypeFieldsProxy, mock(), pomaServiceMock, mock(), locationMappingServiceMock,
        )
    }

    @Test
    fun `test that a berth eta is kept when sgmdh updates existing Nomination data`() {
        val imo = "9479979"
        val nominationBerth = "Jetty 1"
        val nominationPlannedArrival = fetchMillisTrimmedTimestamp(Date())
        val digitrafficPortcall = nl.teqplay.portcallplus.model.service.DigitrafficPortcall(
            agentInfo = emptyList(),
            arrivalWithCargo = false,
            certificateEndDate = null,
            certificateIssuer = "",
            certificateStartDate = null,
            currentSecurityLevel = null,
            customsReference = "",
            discharge = null,
            domesticTrafficArrival = false,
            domesticTrafficDeparture = false,
            forwarderNameArrival = "",
            forwarderNameDeparture = "",
            freeTextArrival = "",
            freeTextDeparture = "",
            imoInformation = emptyList(),
            imoLloyds = null,
            managementNameArrival = "",
            managementNameDeparture = "",
            mmsi = 0,
            nationality = "",
            nextPort = "",
            notLoading = false,
            portAreaDetails = listOf(
                nl.teqplay.portcallplus.model.data.digitraffic.PortAreaDetails(
                    arrivalDraught = 0,
                    ata = null,
                    ataSource = null,
                    ataTimestamp = null,
                    atd = null,
                    atdSource = null,
                    atdTimestamp = null,
                    berthCode = null,
                    berthName = nominationBerth,
                    departureDraught = 0,
                    eta = nominationPlannedArrival,
                    etaSource = null,
                    etaTimestamp = null,
                    etd = null,
                    etdSource = null,
                    etdTimestamp = null,
                    portAreaCode = null,
                    portAreaName = null,
                ),
            ),
            portCallId = 1,
            portCallTimestamp = nominationPlannedArrival,
            portToVisit = "SGSIN",
            prevPort = "",
            radioCallSign = "",
            radioCallSignType = "",
            shipMasterArrival = "",
            shipMasterDeparture = "",
            vesselName = "Test Vessel",
            vesselNamePrefix = "",
            vesselTypeCode = 0,
        )
        whenever(portcallDataSource.generateId("SGSIN", imo, nominationPlannedArrival)).thenReturn("1")
        val portcall = digitrafficService.convertToPortcall(
            digitrafficPortcall,
            imo,
            ScheduledTaskType.DIGITRAFFIC,
        )
        // assert that visit is appended
        assertThat(portcall.visits.first().berthName, `is`(nominationBerth))

        val dueToArriveTime = fetchTimestamp(nominationPlannedArrival, 2, TimeUnit.HOURS)
        val duetoArrive = SgMdhDueToArrive(
            vesselParticulars = SgMdhVessel(digitrafficPortcall.vesselName, "", imo.toLong(), "NL"),
            duetoArriveTime = sgMdhService.fetchSGSINTimestamp(dueToArriveTime),
            locationFrom = "",
            locationTo = "Vopak Banyan 1 OSK",
        )
        // make sure portcall can be fetched by db
        whenever(
            portcallDataSource.getNearestByImoAndDateInterval(
                shipImo = imo,
                port = portcall.port,
                startTime = dueToArriveTime,
                intervalInMillis = sgMdhService.NEW_PORTCALL_INTERVAL,
                finished = null,
            ),
        ).thenReturn(portcall)

        val updatedPortcall = sgMdhService.convertToPortcall(
            serviceModel = duetoArrive,
            imoNumber = portcall.imo,
            taskType = ScheduledTaskType.SG_MDH_DUE_TO_ARRIVE,
        )
        // assert that visit is appended
        assertThat(updatedPortcall.visits.size, `is`(2))
        assertThat(updatedPortcall.visits.first().berthName, `is`(nominationBerth))
        assertThat(updatedPortcall.visits.first().berthEta, `is`(nominationPlannedArrival))
        assertThat(updatedPortcall.visits.last().berthName, `is`(duetoArrive.locationTo))
        assertThat(updatedPortcall.visits.last().berthEta, `is`(dueToArriveTime))
    }
}
