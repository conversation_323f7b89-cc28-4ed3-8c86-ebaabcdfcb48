package nl.teqplay.portcallplus.service

import nl.teqplay.aisengine.shiphistory.client.ShipCurrentClient
import nl.teqplay.portcallplus.PreconditionException
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.Portcall.Companion.IDPREFIX_GNE
import nl.teqplay.portcallplus.api.model.Portcall.Companion.IDPREFIX_NLRTM
import nl.teqplay.portcallplus.api.model.PortcallStatus
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.UpdateType
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.model.service.PortcallGeneratorModel
import nl.teqplay.portcallplus.service.external.PortcallGeneratorService
import nl.teqplay.portcallplus.service.internal.PlatformService
import nl.teqplay.portcallplus.utils.fetchTimestamp
import nl.teqplay.skeleton.platform.client.PortcallClient
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.util.Date
import java.util.concurrent.TimeUnit
import nl.teqplay.platform.model.Portcall as PlatformPortcall

@ExtendWith(MockitoExtension::class)
internal class PortcallGeneratorServiceTest {
    @Mock
    lateinit var shipCurrentClient: ShipCurrentClient

    @Mock
    lateinit var portcallClient: PortcallClient

    @Mock
    lateinit var portcallDataSource: PortcallDataSource

    lateinit var platformService: PlatformService
    lateinit var portcallGeneratorService: PortcallGeneratorService

    @BeforeEach
    fun before() {
        platformService = PlatformService(shipCurrentClient, portcallClient)
        portcallGeneratorService = PortcallGeneratorService(platformService, portcallDataSource, mock(), mock(), mock())
    }

    @Test
    fun `test portcall fetch from platform`() {
        val imo = "9458092"
        val currentTime = Date()
        val orderTime = fetchTimestamp(currentTime, 30, TimeUnit.DAYS)
        val serviceModel = PortcallGeneratorModel(imo, orderTime, IDPREFIX_NLRTM, "Test agency")

        val portcall1 = PlatformPortcall(
            IDPREFIX_NLRTM,
            "1",
            imo,
            PlatformPortcall.PortcallStatus.INBOUND,
            fetchTimestamp(currentTime, 35, TimeUnit.DAYS).time,
        )
        val portcall2 = PlatformPortcall(
            IDPREFIX_NLRTM,
            "2",
            imo,
            PlatformPortcall.PortcallStatus.INBOUND,
            fetchTimestamp(currentTime, 25, TimeUnit.DAYS).time,
        )
        val portcall3 = PlatformPortcall(
            IDPREFIX_NLRTM,
            "3",
            imo,
            PlatformPortcall.PortcallStatus.INBOUND,
            fetchTimestamp(currentTime, 31, TimeUnit.DAYS).time,
        )
        val portcall4 = PlatformPortcall(
            IDPREFIX_NLRTM,
            "4",
            imo,
            PlatformPortcall.PortcallStatus.INBOUND,
            fetchTimestamp(currentTime, 30, TimeUnit.DAYS).time,
        ).apply { finished = true }
        doReturn(listOf(portcall1, portcall2, portcall3, portcall4).toTypedArray())
            .`when`(portcallClient).getAllPortcallsByImo(serviceModel.shipImo, serviceModel.port)

        val portcall = portcallGeneratorService.convertToPortcall(serviceModel, serviceModel.shipImo)

        // portcall3 should be picked as its 'startTime' is closest to the given 'orderTime'
        assertThat(portcall.portcallId, Matchers.`is`(portcall3.portcallId))
    }

    @Test
    fun `test portcall fetch from platform only finished portcalls`() {
        val imo = "9458092"
        val currentTime = Date()
        val orderTime = fetchTimestamp(currentTime, 30, TimeUnit.DAYS)
        val serviceModel = PortcallGeneratorModel(imo, orderTime, IDPREFIX_NLRTM, "portcallid")

        val portcall1 = PlatformPortcall(
            IDPREFIX_NLRTM,
            "1",
            imo,
            PlatformPortcall.PortcallStatus.INBOUND,
            fetchTimestamp(currentTime, 31, TimeUnit.DAYS).time,
        ).apply { finished = true }
        doReturn(listOf(portcall1).toTypedArray())
            .`when`(portcallClient).getAllPortcallsByImo(serviceModel.shipImo, serviceModel.port)

        val portcall = portcallGeneratorService.convertToPortcall(serviceModel, serviceModel.shipImo)

        // as there were only finished portcalls it should have created a new portcall
        assertThat(portcall.portcallId, Matchers.`is`(serviceModel.portcallId))
    }

    @Test
    fun `test no portcall fetch from platform with port managed by portcallplus`() {
        val imo = "9458092"
        val currentTime = Date()
        val orderTime = fetchTimestamp(currentTime, 30, TimeUnit.DAYS)
        val serviceModel = PortcallGeneratorModel(imo, orderTime, IDPREFIX_GNE, "portcallid")

        val portcall = portcallGeneratorService.convertToPortcall(serviceModel, serviceModel.shipImo)

        // as there were only finished portcalls it should have created a new portcall
        assertThat(portcall.portcallId, Matchers.`is`(serviceModel.portcallId))
    }

    @Test
    fun `test fails with portcall exists but for a different imo`() {
        val imo = "9458092"
        val currentTime = Date()
        val orderTime = fetchTimestamp(currentTime, 30, TimeUnit.DAYS)
        val serviceModel = PortcallGeneratorModel(imo, orderTime, IDPREFIX_GNE, "portcallid")

        val existingPortcall =
            Portcall(
                serviceModel.portcallId!!,
                emptySet(),
                serviceModel.port,
                "7458092",
                ScheduledTaskType.UNKNOWN,
                orderTime,
                UpdateType.NOMINATION,
                PortcallStatus.INBOUND,
            )
        whenever(portcallDataSource.get(serviceModel.portcallId!!)).thenReturn(existingPortcall)

        assertThrows<PreconditionException> {
            portcallGeneratorService.convertToPortcall(serviceModel, serviceModel.shipImo)
        }
    }

    @Test
    fun `test fails with portcall exists but for a different port`() {
        val imo = "9458092"
        val currentTime = Date()
        val orderTime = fetchTimestamp(currentTime, 30, TimeUnit.DAYS)
        val serviceModel = PortcallGeneratorModel(imo, orderTime, IDPREFIX_GNE, "portcallid")

        val existingPortcall = Portcall(
            serviceModel.portcallId!!,
            emptySet(),
            IDPREFIX_NLRTM,
            "9458092",
            ScheduledTaskType.UNKNOWN,
            orderTime,
            UpdateType.NOMINATION,
            PortcallStatus.INBOUND,
        )
        whenever(portcallDataSource.get(serviceModel.portcallId!!)).thenReturn(existingPortcall)

        assertThrows<PreconditionException> {
            portcallGeneratorService.convertToPortcall(serviceModel, serviceModel.shipImo)
        }
    }

    @Test
    fun `test fails with portcall exists but for a different port and imo`() {
        val imo = "9458092"
        val currentTime = Date()
        val orderTime = fetchTimestamp(currentTime, 30, TimeUnit.DAYS)
        val serviceModel = PortcallGeneratorModel(imo, orderTime, IDPREFIX_GNE, "portcallid")

        val existingPortcall = Portcall(
            serviceModel.portcallId!!,
            emptySet(),
            IDPREFIX_NLRTM,
            "7458092",
            ScheduledTaskType.UNKNOWN,
            orderTime,
            UpdateType.NOMINATION,
            PortcallStatus.INBOUND,
        )
        whenever(portcallDataSource.get(serviceModel.portcallId!!)).thenReturn(existingPortcall)

        assertThrows<PreconditionException> {
            portcallGeneratorService.convertToPortcall(serviceModel, serviceModel.shipImo)
        }
    }

    @Test
    fun `test get open portcall by portcall id`() {
        val imo = "9458092"
        val currentTime = Date()
        val orderTime = fetchTimestamp(currentTime, 30, TimeUnit.DAYS)
        val serviceModel = PortcallGeneratorModel(imo, orderTime, IDPREFIX_GNE, "portcallid")

        val portcallTime = fetchTimestamp(currentTime, 28, TimeUnit.DAYS)
        val existingPortcall = Portcall(
            serviceModel.portcallId!!,
            emptySet(),
            serviceModel.port,
            serviceModel.shipImo,
            ScheduledTaskType.UNKNOWN,
            portcallTime,
            UpdateType.NOMINATION,
            PortcallStatus.INBOUND,
        )
        whenever(portcallDataSource.get(serviceModel.portcallId!!)).thenReturn(existingPortcall)

        val portcall = portcallGeneratorService.convertToPortcall(serviceModel, serviceModel.shipImo)

        // as there were only finished portcalls it should have created a new portcall
        assertThat(portcall.portcallId, Matchers.`is`(serviceModel.portcallId))
        assertThat(portcall.startTime, Matchers.`is`(serviceModel.orderTime))
    }

    @Test
    fun `test get open portcall by nearest`() {
        val imo = "9458092"
        val currentTime = Date()
        val orderTime = fetchTimestamp(currentTime, 30, TimeUnit.DAYS)
        val serviceModel = PortcallGeneratorModel(imo, orderTime, IDPREFIX_GNE)

        val portcallTime = fetchTimestamp(currentTime, 28, TimeUnit.DAYS)
        val existingPortcall = Portcall(
            "portcallid",
            emptySet(),
            serviceModel.port,
            serviceModel.shipImo,
            ScheduledTaskType.UNKNOWN,
            portcallTime,
            UpdateType.NOMINATION,
            PortcallStatus.INBOUND,
        )
        whenever(
            portcallDataSource.getNearestByImoAndDateInterval(
                serviceModel.shipImo,
                serviceModel.port,
                serviceModel.orderTime,
                TimeUnit.DAYS.toMillis(10),
            ),
        ).thenReturn(existingPortcall)

        val portcall = portcallGeneratorService.convertToPortcall(serviceModel, serviceModel.shipImo)

        // as there were only finished portcalls it should have created a new portcall
        assertThat(portcall.portcallId, Matchers.`is`("portcallid"))
        assertThat(portcall.startTime, Matchers.`is`(serviceModel.orderTime))
    }
}
