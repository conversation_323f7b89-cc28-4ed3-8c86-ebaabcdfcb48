package nl.teqplay.portcallplus.service.common

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType
import nl.teqplay.aisengine.event.model.identifier.GeneralShipIdentifier
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusAgentChangedEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusAtaEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusAtdEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusEtaEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusEtdEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusPortcallVisit
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusShipChangedEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusVisitsUpdateEvent
import nl.teqplay.aisengine.nats.stream.EventStreamAutoConfiguration
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.aisengine.nats.stream.EventStreamServiceImpl
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallStatus
import nl.teqplay.portcallplus.api.model.PortcallVisit
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.UpdateType
import nl.teqplay.skeleton.common.BaseTest
import nl.teqplay.skeleton.nats.NatsClientMock
import nl.teqplay.skeleton.nats.NatsProducerStream
import nl.teqplay.skeleton.nats.queue
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.test.annotation.DirtiesContext
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.TestConstructor
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.Date

@ContextConfiguration
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class EventServiceTest(
    private val natsClientMock: NatsClientMock,
    private val eventService: EventService,
) : BaseTest() {
    companion object {
        private const val TEST_PORTCALL_ID = "TEST_PORTCALL_ID"
        private const val TEST_PORT = "NLRTM"
        private const val TEST_IMO = "1234567"
        private const val TEST_AGENT = "TEST_AGENT"
        private const val TEST_BERTH_ID = "B1"
        private const val TEST_BERTH = "TEST_BERTH"

        // 2023-05-01T00:00:00Z
        private val TEST_TIME_INSTANT = Instant.ofEpochMilli(1682899200000)
        private val TEST_TIME = Date.from(TEST_TIME_INSTANT)
        private val TEST_TIME_ETA = Date.from(TEST_TIME_INSTANT.plus(1, ChronoUnit.DAYS))
        private val TEST_TIME_ATA = Date.from(TEST_TIME_INSTANT.plus(2, ChronoUnit.DAYS))
        private val TEST_TIME_ETD = Date.from(TEST_TIME_INSTANT.plus(3, ChronoUnit.DAYS))
        private val TEST_TIME_ATD = Date.from(TEST_TIME_INSTANT.plus(4, ChronoUnit.DAYS))

        private val TEST_SHIP = GeneralShipIdentifier(null, TEST_IMO.toInt())
        private val TEST_PORT_IDENTIFIER = AreaIdentifier(
            id = null,
            type = AreaType.PORT,
            name = TEST_PORT,
            unlocode = TEST_PORT,
        )

        private val TEST_BERTH_IDENTIFIER = AreaIdentifier(
            id = TEST_BERTH_ID,
            type = AreaType.BERTH,
            name = TEST_BERTH,
            unlocode = TEST_PORT,
        )
    }

    private val testPortcall = Portcall(TEST_PORTCALL_ID, port = TEST_PORT, imo = TEST_IMO, startTime = TEST_TIME, vesselAgent = TEST_AGENT)
    private val testPortIdentifier = AreaIdentifier(id = null, type = AreaType.PORT, name = TEST_PORT, unlocode = TEST_PORT)

    private val testPortcallPlusAtaEvent =
        PortcallPlusAtaEvent(
            TEST_PORTCALL_ID,
            portcallId = TEST_PORTCALL_ID,
            ship = TEST_SHIP,
            area = testPortIdentifier,
            port = testPortIdentifier,
            actualTime = Instant.now(),
        )
    private val testPortcallPlusAtdEvent =
        PortcallPlusAtdEvent(
            TEST_PORTCALL_ID,
            portcallId = TEST_PORTCALL_ID,
            ship = TEST_SHIP,
            area = testPortIdentifier,
            port = testPortIdentifier,
            actualTime = Instant.now(),
        )
    private val testAgentChangedEvent =
        PortcallPlusAgentChangedEvent(
            TEST_PORTCALL_ID,
            portcallId = TEST_PORTCALL_ID,
            ship = TEST_SHIP,
            port = testPortIdentifier,
            actualTime = Instant.now(),
            source = null,
            vesselAgent = "agent",
        )

    @TestConfiguration
    class Config {
        @Bean
        fun testNatsEventProducer(
            natsClientMock: NatsClientMock,
            objectMapper: ObjectMapper,
        ): NatsProducerStream<Event> {
            return natsClientMock.producerStream(
                stream = EventStreamAutoConfiguration.EVENT_STREAM,
                subjects = listOf(EventStreamAutoConfiguration.EVENT_STREAM_SUBJECT),
                serializer = { objectMapper.writeValueAsBytes(it) },
            )
        }

        @Bean
        fun testEventStreamService(producer: NatsProducerStream<Event>): EventStreamService = EventStreamServiceImpl(producer = producer)

        @Bean
        fun testEventService(eventStreamService: EventStreamService): EventService = EventService(eventStreamService)

        @Bean
        fun testNatsClientMock() = NatsClientMock()

        @Bean
        fun objectMapper(): ObjectMapper =
            jacksonObjectMapper()
                .registerModule(JavaTimeModule())
    }

    @Test
    fun `should fire agent event on change`() {
        val portcallWithoutAgent = testPortcall.copy(vesselAgent = null)
        eventService.onUpdatedPortcall(currentPortcall = portcallWithoutAgent, updatedPortcall = testPortcall)

        checkEventQueue { eventQueue ->
            assertEquals(1, eventQueue.size)

            val agentChangedEvent = eventQueue.getFirstEvent<PortcallPlusAgentChangedEvent>()

            val expectedEvent = PortcallPlusAgentChangedEvent(
                _id = agentChangedEvent._id,
                ship = TEST_SHIP,
                portcallId = TEST_PORTCALL_ID,
                port = AreaIdentifier(
                    id = null,
                    type = AreaType.PORT,
                    name = TEST_PORT,
                    unlocode = TEST_PORT,
                ),
                vesselAgent = TEST_AGENT,
                source = null,
                actualTime = agentChangedEvent.actualTime,
                createdTime = agentChangedEvent.createdTime,
            )

            assertEquals(expectedEvent, agentChangedEvent)
        }
    }

    @Test
    fun `should fire imo event on change`() {
        val newImo = "2345678"
        val portcallWithNewImo = testPortcall.copy(imo = newImo)
        eventService.onUpdatedPortcall(currentPortcall = testPortcall, updatedPortcall = portcallWithNewImo)

        checkEventQueue { eventQueue ->
            assertEquals(1, eventQueue.size)

            val shipChangedEvent = eventQueue.getFirstEvent<PortcallPlusShipChangedEvent>()

            val expectedEvent = PortcallPlusShipChangedEvent(
                _id = shipChangedEvent._id,
                ship = TEST_SHIP,
                newShip = GeneralShipIdentifier(null, newImo.toInt()),
                portcallId = TEST_PORTCALL_ID,
                port = AreaIdentifier(
                    id = null,
                    type = AreaType.PORT,
                    name = TEST_PORT,
                    unlocode = TEST_PORT,
                ),
                createdTime = shipChangedEvent.createdTime,
            )

            assertEquals(expectedEvent, shipChangedEvent)
        }
    }

    @Test
    fun `should fire visit event on change`() {
        val newVisits = listOf(
            PortcallVisit(
                berthEta = TEST_TIME_ETA,
                berthEtd = TEST_TIME_ETD,
                berthAta = TEST_TIME_ATA,
                berthAtd = TEST_TIME_ATD,
                berthName = "Some Berth",
            ),
            PortcallVisit(
                berthEta = TEST_TIME_ETA,
                berthEtd = TEST_TIME_ETD,
                berthAta = TEST_TIME_ATA,
                berthAtd = TEST_TIME_ATD,
                berthName = "Some Other Berth",
            ),
        )
        val portcallWithVisit = testPortcall.copy(visits = newVisits)
        eventService.onUpdatedPortcall(currentPortcall = testPortcall, updatedPortcall = portcallWithVisit)

        checkEventQueue { eventQueue ->
            assertEquals(1, eventQueue.size)

            val visitsUpdateEvent = eventQueue.getFirstEvent<PortcallPlusVisitsUpdateEvent>()

            val expectedVisits = listOf(
                PortcallPlusPortcallVisit(
                    berthEta = TEST_TIME_ETA.toInstant(),
                    berthEtd = TEST_TIME_ETD.toInstant(),
                    berthAta = TEST_TIME_ATA.toInstant(),
                    berthAtd = TEST_TIME_ATD.toInstant(),
                    berthName = "Some Berth",
                ),
                PortcallPlusPortcallVisit(
                    berthEta = TEST_TIME_ETA.toInstant(),
                    berthEtd = TEST_TIME_ETD.toInstant(),
                    berthAta = TEST_TIME_ATA.toInstant(),
                    berthAtd = TEST_TIME_ATD.toInstant(),
                    berthName = "Some Other Berth",
                ),
            )
            val expectedEvent = PortcallPlusVisitsUpdateEvent(
                _id = visitsUpdateEvent._id,
                ship = TEST_SHIP,
                portcallId = TEST_PORTCALL_ID,
                port = AreaIdentifier(
                    id = null,
                    type = AreaType.PORT,
                    name = TEST_PORT,
                    unlocode = TEST_PORT,
                ),
                visits = expectedVisits,
                createdTime = visitsUpdateEvent.createdTime,
                vesselAgent = TEST_AGENT,
            )

            assertEquals(expectedEvent, visitsUpdateEvent)
        }
    }

    @Test
    fun `should fire eta and agent changed event on new portcall without visits when in future`() {
        val now = Instant.now()
        // We set the start time to now as the ETA event should only be generated when not older than 2 days
        val startTime = now.truncatedTo(ChronoUnit.MINUTES)

        val newPortcall = testPortcall.copy(
            vesselAgent = TEST_AGENT,
            source = ScheduledTaskType.PORTCALL_GENERATOR,
            startTime = Date.from(startTime),
        )
        eventService.onNewPortcall(newPortcall = newPortcall)

        checkEventQueue { eventQueue ->
            assertEquals(2, eventQueue.size)

            val agentChangedEvent = eventQueue.getFirstEvent<PortcallPlusAgentChangedEvent>()
            val etaEvent = eventQueue.getFirstEvent<PortcallPlusEtaEvent>()

            val expectedPortArea = AreaIdentifier(
                id = null,
                type = AreaType.PORT,
                name = TEST_PORT,
                unlocode = TEST_PORT,
            )

            val expectedAgentChangedEvent = PortcallPlusAgentChangedEvent(
                _id = agentChangedEvent._id,
                ship = TEST_SHIP,
                portcallId = TEST_PORTCALL_ID,
                port = expectedPortArea,
                source = null,
                vesselAgent = TEST_AGENT,
                createdTime = agentChangedEvent.createdTime,
                actualTime = agentChangedEvent.actualTime,
            )

            val expectedEtaEvent = PortcallPlusEtaEvent(
                _id = etaEvent._id,
                ship = TEST_SHIP,
                portcallId = TEST_PORTCALL_ID,
                area = expectedPortArea,
                port = expectedPortArea,
                source = "Teqplay Nominations",
                berth = null,
                berthId = null,
                vesselAgent = TEST_AGENT,
                predictedTime = startTime,
                createdTime = etaEvent.createdTime,
                nomination = false,
            )

            assertEquals(expectedAgentChangedEvent, agentChangedEvent)
            assertEquals(expectedEtaEvent, etaEvent)
        }
    }

    @Test
    fun `drop event update if ata for anchorage`() {
        val portcall = testPortcall.copy(
            source = ScheduledTaskType.SG_MDH_VISIT_ARRIVAL_DECLARATION,
        )

        val testPortcallPlusAtaEvent1 = testPortcallPlusAtaEvent.copy(
            area = AreaIdentifier(id = "", type = AreaType.BERTH, name = "gusong boarding"),
        )
        val testPortcallPlusAtaEvent2 = testPortcallPlusAtaEvent.copy(
            area = AreaIdentifier(id = "", type = AreaType.BERTH, name = "something anchorage"),
        )
        val testPortcallPlusAtaEvent3 = testPortcallPlusAtaEvent.copy(
            area = AreaIdentifier(id = "", type = AreaType.BERTH, name = "WESTERN PETRO ANCH B"),
        )
        val testPortcallPlusAtdEvent1 = testPortcallPlusAtdEvent.copy(
            area = AreaIdentifier(id = "", type = AreaType.BERTH, name = "anch 3"),
        )
        val testPortcallPlusAtdEvent2 = testPortcallPlusAtdEvent.copy(
            area = AreaIdentifier(id = "", type = AreaType.BERTH, name = "gusong pilot"),
        )
        assertEquals(false, portcall.isValidUpdateEvent(testPortcallPlusAtaEvent1))
        assertEquals(false, portcall.isValidUpdateEvent(testPortcallPlusAtaEvent2))
        assertEquals(false, portcall.isValidUpdateEvent(testPortcallPlusAtaEvent3))
        assertEquals(false, portcall.isValidUpdateEvent(testPortcallPlusAtdEvent1))
        assertEquals(false, portcall.isValidUpdateEvent(testPortcallPlusAtdEvent2))

        val testPortcallPlusAtaEvent4 = testPortcallPlusAtaEvent.copy(
            area = AreaIdentifier(id = "", type = AreaType.BERTH, name = "berth1"),
        )

        val testPortcallPlusAtdEvent3 = testPortcallPlusAtdEvent.copy(
            area = AreaIdentifier(id = "", type = AreaType.BERTH, name = "berth2"),
        )
        assertEquals(true, portcall.isValidUpdateEvent(testPortcallPlusAtaEvent4))
        assertEquals(true, portcall.isValidUpdateEvent(testPortcallPlusAtdEvent3))
        assertEquals(true, portcall.isValidUpdateEvent(testAgentChangedEvent))
    }

    @Test
    fun `should NOT fire eta event on new portcall without visits when in past`() {
        val now = Instant.now()
        val startTime = now.minus(2, ChronoUnit.DAYS)
            .truncatedTo(ChronoUnit.MINUTES)

        val newPortcall = testPortcall.copy(
            vesselAgent = TEST_AGENT,
            source = ScheduledTaskType.PORTCALL_GENERATOR,
            startTime = Date.from(startTime),
        )
        eventService.onNewPortcall(newPortcall = newPortcall)

        checkEventQueue { eventQueue ->
            assertEquals(0, eventQueue.size)
        }
    }

    @Test
    fun `should fire events for USHOU portcall with new visit`() {
        val now = Instant.now()
        val eta1 = Date.from(now.plus(Duration.ofDays(1)))
        val etd1 = Date.from(now.plus(Duration.ofDays(2)))

        val eta2 = Date.from(now.plus(Duration.ofDays(2)))
        val etd2 = Date.from(now.plus(Duration.ofDays(3)))

        val existingVisit = PortcallVisit(
            berthEta = eta1,
            berthEtd = etd1,
        )
        val newVisit = PortcallVisit(
            berthEta = eta2,
            berthEtd = etd2,
        )

        val currentPortcall = testPortcall.copy(
            startTime = Date.from(now),
            port = "USHOU",
            portcallId = "V1",
            startTimeType = UpdateType.PORT,
            status = PortcallStatus.INBOUND,
            visits = listOf(existingVisit),
        )
        val updatedPortcall = currentPortcall.copy(
            visits = listOf(existingVisit, newVisit),
        )

        eventService.onNewVisit(portcall = currentPortcall, TEST_BERTH_ID, TEST_BERTH, newVisit, UpdateType.PORT)
        eventService.onUpdatedPortcall(currentPortcall = currentPortcall, updatedPortcall = updatedPortcall)

        checkEventQueue { eventQueue ->
            assertEquals(3, eventQueue.size)
            val etaEvent = eventQueue.getFirstEvent<PortcallPlusEtaEvent>()
            val etdEvent = eventQueue.getFirstEvent<PortcallPlusEtdEvent>()
            val visitsUpdateEvent = eventQueue.getFirstEvent<PortcallPlusVisitsUpdateEvent>()

            val expectedPortArea = AreaIdentifier(
                id = null,
                type = AreaType.PORT,
                name = "USHOU",
                unlocode = "USHOU",
            )

            val expectedEtaEvent = PortcallPlusEtaEvent(
                _id = etaEvent._id,
                ship = TEST_SHIP,
                portcallId = "V1",
                area = expectedPortArea,
                port = expectedPortArea,
                source = null,
                berth = TEST_BERTH,
                berthId = TEST_BERTH_ID,
                vesselAgent = TEST_AGENT,
                predictedTime = eta2.toInstant(),
                createdTime = etaEvent.createdTime,
                nomination = false,
            )

            val expectedEtdEvent = PortcallPlusEtdEvent(
                _id = etdEvent._id,
                ship = TEST_SHIP,
                portcallId = "V1",
                area = expectedPortArea,
                port = expectedPortArea,
                source = null,
                berth = TEST_BERTH,
                berthId = TEST_BERTH_ID,
                vesselAgent = TEST_AGENT,
                predictedTime = etd2.toInstant(),
                createdTime = etdEvent.createdTime,
                nomination = false,
            )

            val expectedVisits = listOf(
                PortcallPlusPortcallVisit(
                    berthEta = eta1.toInstant(),
                    berthEtd = etd1.toInstant(),
                ),
                PortcallPlusPortcallVisit(
                    berthEta = eta2.toInstant(),
                    berthEtd = etd2.toInstant(),
                ),
            )
            val expectedVisitsUpdateEvent = PortcallPlusVisitsUpdateEvent(
                _id = visitsUpdateEvent._id,
                ship = TEST_SHIP,
                portcallId = "V1",
                port = expectedPortArea,
                visits = expectedVisits,
                createdTime = visitsUpdateEvent.createdTime,
                vesselAgent = TEST_AGENT,
            )

            assertEquals(expectedEtaEvent, etaEvent)
            assertEquals(expectedEtdEvent, etdEvent)
            assertEquals(expectedVisitsUpdateEvent, visitsUpdateEvent)
        }
    }

    @Test
    fun `should fire event for SGSIN portcall with new pilotboardingplace visit`() {
        val now = Instant.now()
        val eta1 = Date.from(now.plus(Duration.ofDays(1)))
        val eta2 = Date.from(now.plus(Duration.ofDays(2)))

        val existingVisit = PortcallVisit(
            berthEta = eta1,
            visitType = UpdateType.PORT,
        )
        val newVisit = PortcallVisit(
            berthEta = eta2,
            berthName = "PILOT WEST BOARD GRADE A",
            visitType = UpdateType.PILOTBOARDINGPLACE,
        )

        val currentPortcall = testPortcall.copy(
            startTime = Date.from(now),
            port = "SGSIN",
            portcallId = "V1",
            startTimeType = UpdateType.PORT,
            status = PortcallStatus.INBOUND,
            visits = listOf(existingVisit),
        )
        val updatedPortcall = currentPortcall.copy(
            startTimeType = UpdateType.PILOTBOARDINGPLACE,
            visits = listOf(newVisit),
        )

        eventService.onNewVisit(portcall = currentPortcall, TEST_BERTH_ID, TEST_BERTH, newVisit, UpdateType.PILOTBOARDINGPLACE)
        eventService.onUpdatedPortcall(currentPortcall = currentPortcall, updatedPortcall = updatedPortcall)

        checkEventQueue { eventQueue ->
            assertEquals(2, eventQueue.size)
            val etaEvent = eventQueue.getFirstEvent<PortcallPlusEtaEvent>()
            val visitsUpdateEvent = eventQueue.getFirstEvent<PortcallPlusVisitsUpdateEvent>()

            val expectedPortArea = AreaIdentifier(
                id = null,
                type = AreaType.PORT,
                name = "SGSIN",
                unlocode = "SGSIN",
            )

            val expectedPilotBoardingPlaceArea = AreaIdentifier(
                id = null,
                type = AreaType.PILOT_BOARDING_PLACE,
                name = "PILOT WEST BOARD GRADE A",
                unlocode = "SGSIN",
            )

            val expectedEtaEvent = PortcallPlusEtaEvent(
                _id = etaEvent._id,
                ship = TEST_SHIP,
                portcallId = "V1",
                area = expectedPilotBoardingPlaceArea,
                port = expectedPortArea,
                source = null,
                berth = TEST_BERTH,
                berthId = TEST_BERTH_ID,
                vesselAgent = TEST_AGENT,
                predictedTime = eta2.toInstant(),
                createdTime = etaEvent.createdTime,
                nomination = false,
            )

            val expectedVisits = listOf(
                PortcallPlusPortcallVisit(
                    berthEta = eta2.toInstant(),
                    berthName = "PILOT WEST BOARD GRADE A",
                ),
            )
            val expectedVisitsUpdateEvent = PortcallPlusVisitsUpdateEvent(
                _id = visitsUpdateEvent._id,
                ship = TEST_SHIP,
                portcallId = "V1",
                port = expectedPortArea,
                visits = expectedVisits,
                createdTime = visitsUpdateEvent.createdTime,
                vesselAgent = TEST_AGENT,
            )

            assertEquals(expectedEtaEvent, etaEvent)
            assertEquals(expectedVisitsUpdateEvent, visitsUpdateEvent)
        }
    }

    @Test
    fun `should fire event for SGSIN portcall with updated pilotboardingplace visit`() {
        val now = Instant.now()
        val eta1 = Date.from(now.plus(Duration.ofDays(1)))
        val eta2 = Date.from(now.plus(Duration.ofDays(2)))

        val existingVisit = PortcallVisit(
            berthEta = eta1,
            berthName = "PILOT WEST BOARD GRADE A",
            visitType = UpdateType.PILOTBOARDINGPLACE,
        )
        val updatedVisit = PortcallVisit(
            berthEta = eta2,
            berthName = "PILOT WEST BOARD GRADE A",
            visitType = UpdateType.PILOTBOARDINGPLACE,
        )

        val currentPortcall = testPortcall.copy(
            startTime = Date.from(now),
            port = "SGSIN",
            portcallId = "V1",
            startTimeType = UpdateType.PILOTBOARDINGPLACE,
            status = PortcallStatus.INBOUND,
            visits = listOf(existingVisit),
        )
        val updatedPortcall = currentPortcall.copy(
            startTimeType = UpdateType.PILOTBOARDINGPLACE,
            visits = listOf(updatedVisit),
        )

        eventService.onNewVisit(portcall = currentPortcall, TEST_BERTH_ID, TEST_BERTH, updatedVisit, UpdateType.PILOTBOARDINGPLACE)
        eventService.onUpdatedPortcall(currentPortcall = currentPortcall, updatedPortcall = updatedPortcall)

        checkEventQueue { eventQueue ->
            assertEquals(2, eventQueue.size)
            val etaEvent = eventQueue.getFirstEvent<PortcallPlusEtaEvent>()
            val visitsUpdateEvent = eventQueue.getFirstEvent<PortcallPlusVisitsUpdateEvent>()

            val expectedPortArea = AreaIdentifier(
                id = null,
                type = AreaType.PORT,
                name = "SGSIN",
                unlocode = "SGSIN",
            )

            val expectedPilotBoardingPlaceArea = AreaIdentifier(
                id = null,
                type = AreaType.PILOT_BOARDING_PLACE,
                name = "PILOT WEST BOARD GRADE A",
                unlocode = "SGSIN",
            )

            val expectedEtaEvent = PortcallPlusEtaEvent(
                _id = etaEvent._id,
                ship = TEST_SHIP,
                portcallId = "V1",
                area = expectedPilotBoardingPlaceArea,
                port = expectedPortArea,
                source = null,
                berth = TEST_BERTH,
                berthId = TEST_BERTH_ID,
                vesselAgent = TEST_AGENT,
                predictedTime = eta2.toInstant(),
                createdTime = etaEvent.createdTime,
                nomination = false,
            )

            val expectedVisits = listOf(
                PortcallPlusPortcallVisit(
                    berthEta = eta2.toInstant(),
                    berthName = "PILOT WEST BOARD GRADE A",
                ),
            )
            val expectedVisitsUpdateEvent = PortcallPlusVisitsUpdateEvent(
                _id = visitsUpdateEvent._id,
                ship = TEST_SHIP,
                portcallId = "V1",
                port = expectedPortArea,
                visits = expectedVisits,
                createdTime = visitsUpdateEvent.createdTime,
                vesselAgent = TEST_AGENT,
            )

            assertEquals(expectedEtaEvent, etaEvent)
            assertEquals(expectedVisitsUpdateEvent, visitsUpdateEvent)
        }
    }

    @Test
    fun `should fire nomination event for SGSIN portcall with berth nomination when startTimeType is not changed`() {
        val now = Instant.now()
        val eta1 = Date.from(now.plus(Duration.ofDays(1)))

        val berthNominationVisit = PortcallVisit(
            berthEta = eta1,
            berthName = "MY TEST BERTH",
            uniqueBerthId = TEST_BERTH_ID,
            visitType = UpdateType.NOMINATION_AGENT,
        )

        val currentPortcall = testPortcall.copy(
            startTime = Date.from(now),
            port = "SGSIN",
            portcallId = "V1",
            startTimeType = UpdateType.PORT,
            status = PortcallStatus.INBOUND,
            visits = listOf(),
        )
        val updatedPortcall = currentPortcall.copy(
            visits = listOf(berthNominationVisit),
        )

        eventService.onNewVisit(
            portcall = currentPortcall,
            TEST_BERTH_ID,
            "MY TEST BERTH",
            berthNominationVisit,
            currentPortcall.startTimeType,
        )
        eventService.onUpdatedPortcall(currentPortcall = currentPortcall, updatedPortcall = updatedPortcall)

        checkEventQueue { eventQueue ->
            assertEquals(2, eventQueue.size)
            val etaEvent = eventQueue.getFirstEvent<PortcallPlusEtaEvent>()
            val visitsUpdateEvent = eventQueue.getFirstEvent<PortcallPlusVisitsUpdateEvent>()

            val expectedPortArea = AreaIdentifier(
                id = null,
                type = AreaType.PORT,
                name = "SGSIN",
                unlocode = "SGSIN",
            )

            val expectedBerthArea = AreaIdentifier(
                id = TEST_BERTH_ID,
                type = AreaType.BERTH,
                name = "MY TEST BERTH",
                unlocode = "SGSIN",
            )

            val expectedEtaEvent = PortcallPlusEtaEvent(
                _id = etaEvent._id,
                ship = TEST_SHIP,
                portcallId = "V1",
                area = expectedBerthArea,
                port = expectedPortArea,
                source = null,
                berth = "MY TEST BERTH",
                berthId = TEST_BERTH_ID,
                vesselAgent = TEST_AGENT,
                predictedTime = eta1.toInstant(),
                createdTime = etaEvent.createdTime,
                nomination = true,
            )

            val expectedVisits = listOf(
                PortcallPlusPortcallVisit(
                    berthEta = eta1.toInstant(),
                    berthName = "MY TEST BERTH",
                ),
            )
            val expectedVisitsUpdateEvent = PortcallPlusVisitsUpdateEvent(
                _id = visitsUpdateEvent._id,
                ship = TEST_SHIP,
                portcallId = "V1",
                port = expectedPortArea,
                visits = expectedVisits,
                createdTime = visitsUpdateEvent.createdTime,
                vesselAgent = TEST_AGENT,
            )

            assertEquals(expectedEtaEvent, etaEvent)
            assertEquals(expectedVisitsUpdateEvent, visitsUpdateEvent)
        }
    }

    @Test
    fun `should fire port nomination event for SGSIN portcall`() {
        val now = Instant.now()
        val eta1 = Date.from(now.plus(Duration.ofDays(1)))

        val portNominationVisit = PortcallVisit(
            berthEta = eta1,
            visitType = UpdateType.NOMINATION_AGENT,
        )

        val currentPortcall = testPortcall.copy(
            startTime = Date.from(now),
            port = "SGSIN",
            portcallId = "V1",
            startTimeType = UpdateType.PORT,
            status = PortcallStatus.INBOUND,
            visits = listOf(),
        )
        val updatedPortcall = currentPortcall.copy(
            visits = listOf(portNominationVisit),
        )

        eventService.onNewVisit(portcall = currentPortcall, null, null, portNominationVisit, currentPortcall.startTimeType)
        eventService.onUpdatedPortcall(currentPortcall = currentPortcall, updatedPortcall = updatedPortcall)

        checkEventQueue { eventQueue ->
            assertEquals(2, eventQueue.size)
            val etaEvent = eventQueue.getFirstEvent<PortcallPlusEtaEvent>()
            val visitsUpdateEvent = eventQueue.getFirstEvent<PortcallPlusVisitsUpdateEvent>()

            val expectedPortArea = AreaIdentifier(
                id = null,
                type = AreaType.PORT,
                name = "SGSIN",
                unlocode = "SGSIN",
            )

            val expectedEtaEvent = PortcallPlusEtaEvent(
                _id = etaEvent._id,
                ship = TEST_SHIP,
                portcallId = "V1",
                area = expectedPortArea,
                port = expectedPortArea,
                source = null,
                berth = null,
                berthId = null,
                vesselAgent = TEST_AGENT,
                predictedTime = eta1.toInstant(),
                createdTime = etaEvent.createdTime,
                nomination = true,
            )

            val expectedVisits = listOf(
                PortcallPlusPortcallVisit(
                    berthEta = eta1.toInstant(),
                ),
            )
            val expectedVisitsUpdateEvent = PortcallPlusVisitsUpdateEvent(
                _id = visitsUpdateEvent._id,
                ship = TEST_SHIP,
                portcallId = "V1",
                port = expectedPortArea,
                visits = expectedVisits,
                createdTime = visitsUpdateEvent.createdTime,
                vesselAgent = TEST_AGENT,
            )

            assertEquals(expectedEtaEvent, etaEvent)
            assertEquals(expectedVisitsUpdateEvent, visitsUpdateEvent)
        }
    }

    @Test
    fun `should NOT fire events on a non-numerical imo`() {
        val incorrectPortcall = testPortcall.copy(imo = "INVALID")
        val updatedIncorrectPortcall = incorrectPortcall.copy(vesselAgent = TEST_AGENT)
        eventService.onNewPortcall(newPortcall = incorrectPortcall)
        eventService.onUpdatedPortcall(
            currentPortcall = incorrectPortcall,
            updatedPortcall = updatedIncorrectPortcall,
        )

        checkEventQueue { eventQueue ->
            assertEquals(0, eventQueue.size)
        }
    }

    @Test
    fun `should NOT fire events when updating the imo to a valid value`() {
        val incorrectPortcall = testPortcall.copy(imo = "INVALID", vesselAgent = TEST_AGENT)
        val correctPortcall = incorrectPortcall.copy(imo = TEST_IMO)
        eventService.onUpdatedPortcall(
            currentPortcall = incorrectPortcall,
            updatedPortcall = correctPortcall,
        )

        checkEventQueue { eventQueue ->
            assertEquals(0, eventQueue.size)
        }
    }

    @Test
    fun `should NOT fire an ETA event when too old on new portcall`() {
        val testEta = Date.from(TEST_TIME_INSTANT.minus(Duration.ofDays(2)))
        val newPortcall = testPortcall.copy(
            visits = listOf(
                PortcallVisit(berthEta = testEta),
            ),
        )

        eventService.onNewVisit(
            portcall = newPortcall,
            berthId = TEST_BERTH_ID,
            berth = null,
            newVisit = PortcallVisit(berthEta = testEta),
            newStartTimeType = UpdateType.BERTH,
        )

        checkEventQueue { eventQueue ->
            assertEquals(0, eventQueue.size)
        }
    }

    @Test
    fun `should fire ATA and ATD events on new visit with old times`() {
        val now = Instant.now()
        val testEta = Date.from(now.minus(Duration.ofDays(5)))
        val testEtd = Date.from(now.minus(Duration.ofDays(4)))
        val testAta = Date.from(now.minus(Duration.ofDays(5)))
        val testAtd = Date.from(now.minus(Duration.ofDays(3)))
        val testVisit = PortcallVisit(
            berthEta = testEta,
            berthEtd = testEtd,
            berthAta = testAta,
            berthAtd = testAtd,
        )
        val newPortcall = testPortcall.copy(
            visits = listOf(testVisit),
        )

        eventService.onNewVisit(
            portcall = newPortcall,
            berthId = TEST_BERTH_ID,
            berth = TEST_BERTH,
            newVisit = testVisit,
            newStartTimeType = UpdateType.BERTH,
        )

        checkEventQueue { eventQueue ->
            assertEquals(2, eventQueue.size)

            val resultAtaEvent = eventQueue.getFirstEvent<PortcallPlusAtaEvent>()
            val resultAtdEvent = eventQueue.getFirstEvent<PortcallPlusAtdEvent>()

            val expectedAtaEvent = PortcallPlusAtaEvent(
                _id = resultAtaEvent._id,
                ship = TEST_SHIP,
                portcallId = TEST_PORTCALL_ID,
                area = TEST_BERTH_IDENTIFIER,
                port = TEST_PORT_IDENTIFIER,
                actualTime = testAta.toInstant(),
                createdTime = resultAtaEvent.createdTime,
            )

            val expectedAtdEvent = PortcallPlusAtdEvent(
                _id = resultAtdEvent._id,
                ship = TEST_SHIP,
                portcallId = TEST_PORTCALL_ID,
                area = TEST_BERTH_IDENTIFIER,
                port = TEST_PORT_IDENTIFIER,
                actualTime = testAtd.toInstant(),
                createdTime = resultAtdEvent.createdTime,
            )

            assertEquals(expectedAtaEvent, resultAtaEvent)
            assertEquals(expectedAtdEvent, resultAtdEvent)
        }
    }

    @Test
    fun `should fire an ATA event when in past on new portcall`() {
        val testEta = Date.from(TEST_TIME_INSTANT.minus(Duration.ofDays(2)))
        val newPortcall = testPortcall.copy(
            visits = listOf(
                PortcallVisit(berthEta = testEta),
            ),
        )

        eventService.onNewPortcall(newPortcall = newPortcall)

        checkEventQueue { eventQueue ->
            assertEquals(0, eventQueue.size)
        }
    }

    @Test
    fun `should fire events when updating visit`() {
        val now = Instant.now()

        // Initial times
        val eta1 = Date.from(now.plus(Duration.ofDays(1)))
        val etd1 = Date.from(now.plus(Duration.ofDays(2)))
        val ata1 = Date.from(now.plus(Duration.ofDays(2)))
        val atd1 = Date.from(now.plus(Duration.ofDays(3)))

        // New times
        val eta2 = Date.from(now.plus(Duration.ofDays(4)))
        val etd2 = Date.from(now.plus(Duration.ofDays(5)))
        val ata2 = Date.from(now.plus(Duration.ofDays(5)))
        val atd2 = Date.from(now.plus(Duration.ofDays(6)))

        val currentVisit = PortcallVisit(
            berthEta = eta1,
            berthEtd = etd1,
            berthAta = ata1,
            berthAtd = atd1,
            uniqueBerthId = TEST_BERTH_ID,
            berthName = TEST_BERTH,
        )

        val updatedVisit = PortcallVisit(
            berthEta = eta2,
            berthEtd = etd2,
            berthAta = ata2,
            berthAtd = atd2,
            uniqueBerthId = TEST_BERTH_ID,
            berthName = TEST_BERTH,
        )

        val currentPortcall = testPortcall.copy(
            startTimeType = UpdateType.BERTH,
            visits = listOf(currentVisit),
        )

        eventService.onVisitChanges(
            portcall = currentPortcall,
            berthId = TEST_BERTH_ID,
            berth = TEST_BERTH,
            currentVisit = currentVisit,
            updatedVisit = updatedVisit,
            newStartTimeType = UpdateType.BERTH,
        )

        checkEventQueue { eventQueue ->
            assertEquals(4, eventQueue.size)

            val resultEtaEvent = eventQueue.getFirstEvent<PortcallPlusEtaEvent>()
            val resultEtdEvent = eventQueue.getFirstEvent<PortcallPlusEtdEvent>()
            val resultAtaEvent = eventQueue.getFirstEvent<PortcallPlusAtaEvent>()
            val resultAtdEvent = eventQueue.getFirstEvent<PortcallPlusAtdEvent>()

            val expectedEtaEvent = PortcallPlusEtaEvent(
                _id = resultEtaEvent._id,
                ship = TEST_SHIP,
                portcallId = TEST_PORTCALL_ID,
                area = TEST_BERTH_IDENTIFIER,
                port = TEST_PORT_IDENTIFIER,
                source = null,
                berth = TEST_BERTH,
                berthId = TEST_BERTH_ID,
                vesselAgent = TEST_AGENT,
                predictedTime = eta2.toInstant(),
                createdTime = resultEtaEvent.createdTime,
                nomination = false,
            )

            val expectedEtdEvent = PortcallPlusEtdEvent(
                _id = resultEtdEvent._id,
                ship = TEST_SHIP,
                portcallId = TEST_PORTCALL_ID,
                area = TEST_BERTH_IDENTIFIER,
                port = TEST_PORT_IDENTIFIER,
                source = null,
                berth = TEST_BERTH,
                berthId = TEST_BERTH_ID,
                vesselAgent = TEST_AGENT,
                predictedTime = etd2.toInstant(),
                createdTime = resultEtdEvent.createdTime,
                nomination = false,
            )

            val expectedAtaEvent = PortcallPlusAtaEvent(
                _id = resultAtaEvent._id,
                ship = TEST_SHIP,
                portcallId = TEST_PORTCALL_ID,
                area = TEST_BERTH_IDENTIFIER,
                port = TEST_PORT_IDENTIFIER,
                actualTime = ata2.toInstant(),
                createdTime = resultAtaEvent.createdTime,
            )

            val expectedAtdEvent = PortcallPlusAtdEvent(
                _id = resultAtdEvent._id,
                ship = TEST_SHIP,
                portcallId = TEST_PORTCALL_ID,
                area = TEST_BERTH_IDENTIFIER,
                port = TEST_PORT_IDENTIFIER,
                actualTime = atd2.toInstant(),
                createdTime = resultAtdEvent.createdTime,
            )

            assertEquals(expectedEtaEvent, resultEtaEvent)
            assertEquals(expectedEtdEvent, resultEtdEvent)
            assertEquals(expectedAtaEvent, resultAtaEvent)
            assertEquals(expectedAtdEvent, resultAtdEvent)
        }
    }

    @Test
    fun `should create berth etd on visit change`() {
        val now = Instant.now()

        val eta = Date.from(now.plus(Duration.ofDays(1)))
        val etd = Date.from(now.plus(Duration.ofDays(2)))

        val currentVisit = PortcallVisit(
            berthEta = eta,
            berthEtd = null,
            berthAta = null,
            berthAtd = null,
            uniqueBerthId = TEST_BERTH_ID,
            berthName = TEST_BERTH,
        )

        val updatedVisit = PortcallVisit(
            berthEta = eta,
            berthEtd = etd,
            berthAta = null,
            berthAtd = null,
            uniqueBerthId = TEST_BERTH_ID,
            berthName = TEST_BERTH,
        )

        val currentPortcall = testPortcall.copy(
            startTimeType = UpdateType.BERTH,
            visits = listOf(currentVisit),
        )

        eventService.onVisitChanges(
            portcall = currentPortcall,
            berthId = TEST_BERTH_ID,
            berth = TEST_BERTH,
            currentVisit = currentVisit,
            updatedVisit = updatedVisit,
            newStartTimeType = UpdateType.BERTH,
        )

        checkEventQueue { eventQueue ->
            assertEquals(1, eventQueue.size)

            val resultEtdEvent = eventQueue.getFirstEvent<PortcallPlusEtdEvent>()

            val expectedEtdEvent = PortcallPlusEtdEvent(
                _id = resultEtdEvent._id,
                ship = TEST_SHIP,
                portcallId = TEST_PORTCALL_ID,
                area = TEST_BERTH_IDENTIFIER,
                port = TEST_PORT_IDENTIFIER,
                source = null,
                berth = TEST_BERTH,
                berthId = TEST_BERTH_ID,
                vesselAgent = TEST_AGENT,
                predictedTime = etd.toInstant(),
                createdTime = resultEtdEvent.createdTime,
                nomination = false,
            )

            assertEquals(expectedEtdEvent, resultEtdEvent)
        }
    }

    private fun checkEventQueue(onCheck: (eventQueue: MutableList<Pair<String, Event>>) -> Unit) {
        val eventQueue = natsClientMock.queue<Event>(EventStreamAutoConfiguration.EVENT_STREAM)
        onCheck(eventQueue)
    }

    private fun <T : Event> MutableList<Pair<String, Event>>.getFirstEvent(): T {
        val (_, event) = this.removeFirst()
        return event as T
    }

    @Test
    fun `Test if visits send out event when both visits are equal in objects and sorting`() {
        val portcallVisitOne = PortcallVisit(
            berthEta = Date.from(Instant.ofEpochMilli(1731765600000)),
            berthName = "test1",
        )
        val portcallVisitTwo = PortcallVisit(
            berthEta = Date.from(Instant.ofEpochMilli(1731765800000)),
            berthName = "test2",
        )
        val portcallVisitThree = PortcallVisit(
            berthEta = Date.from(Instant.ofEpochMilli(1731765900000)),
            berthName = "test3",
        )
        val currentVisits = listOf(
            portcallVisitOne,
            portcallVisitTwo,
            portcallVisitThree,
        )
        val updatedVisits = listOf(
            portcallVisitOne,
            portcallVisitTwo,
            portcallVisitThree,
        )
        val updateEvent = eventService.createEventIfVisitsChanged(
            null,
            currentVisits = currentVisits,
            updatedVisits = updatedVisits,
            portcallId = TEST_PORTCALL_ID,
            port = TEST_PORT_IDENTIFIER,
            imo = 123456,
        )

        assert(updateEvent == null)
    }

    @Test
    fun `Test if visits send out event when only the sorting is different with same events`() {
        val portcallVisitOne = PortcallVisit(
            berthEta = Date.from(Instant.ofEpochMilli(1731765600000)),
            berthName = "test1",
        )
        val portcallVisitTwo = PortcallVisit(
            berthEta = Date.from(Instant.ofEpochMilli(1731765800000)),
            berthName = "test2",
        )
        val portcallVisitThree = PortcallVisit(
            berthEta = Date.from(Instant.ofEpochMilli(1731765900000)),
            berthName = "test3",
        )
        val currentVisits = listOf(
            portcallVisitOne,
            portcallVisitTwo,
            portcallVisitThree,
        )
        val updatedVisits = listOf(
            portcallVisitOne,
            portcallVisitThree,
            portcallVisitTwo,
        )
        val updateEvent = eventService.createEventIfVisitsChanged(
            null,
            currentVisits = currentVisits,
            updatedVisits = updatedVisits,
            portcallId = TEST_PORTCALL_ID,
            port = TEST_PORT_IDENTIFIER,
            imo = 123456,
        )

        assert(updateEvent == null)
    }

    @Test
    fun `Test if visits send out event with updated visits`() {
        val portcallVisitOne = PortcallVisit(
            berthEta = Date.from(Instant.ofEpochMilli(1731765600000)),
            berthName = "test1",
        )
        val portcallVisitTwo = PortcallVisit(
            berthEta = Date.from(Instant.ofEpochMilli(1731765800000)),
            berthName = "test2",
        )
        val portcallVisitThree = PortcallVisit(
            berthEta = Date.from(Instant.ofEpochMilli(1731765900000)),
            berthName = "test3",
        )
        val currentVisits = listOf(
            portcallVisitOne,
            portcallVisitTwo,
            portcallVisitThree,
        )
        // Note: the berthEta is changed for the updated visits
        val updatedVisits = listOf(
            portcallVisitOne,
            portcallVisitThree.copy(berthEta = Date.from(Instant.ofEpochMilli(1731766000000))),
            portcallVisitTwo,
        )
        val updateEvent = eventService.createEventIfVisitsChanged(
            null,
            currentVisits = currentVisits,
            updatedVisits = updatedVisits,
            portcallId = TEST_PORTCALL_ID,
            port = TEST_PORT_IDENTIFIER,
            imo = 123456,
        )

        assertNotNull(updateEvent)
    }

    @Test
    fun `should fire Eta event on updated SGSIN portcall`() {
        val now = Instant.now()

        val currentPortcall = testPortcall.copy(
            startTime = Date.from(now),
            port = "SGSIN",
            portcallId = "V1",
            startTimeType = UpdateType.PORT,
            status = PortcallStatus.INBOUND,
            visits = listOf(),
        )
        val updatedStartTime = Date.from(Instant.now().plusSeconds(10))
        val updatedPortcall = currentPortcall.copy(
            startTime = updatedStartTime,
        )

        eventService.onUpdatedPortcall(currentPortcall = currentPortcall, updatedPortcall = updatedPortcall)

        checkEventQueue { eventQueue ->
            assertEquals(1, eventQueue.size)
            val etaEvent = eventQueue.getFirstEvent<PortcallPlusEtaEvent>()

            val expectedPortArea = AreaIdentifier(
                id = null,
                type = AreaType.PORT,
                name = "SGSIN",
                unlocode = "SGSIN",
            )

            val expectedEtaEvent = PortcallPlusEtaEvent(
                _id = etaEvent._id,
                ship = TEST_SHIP,
                portcallId = "V1",
                area = expectedPortArea,
                port = expectedPortArea,
                source = null,
                berth = null,
                berthId = null,
                vesselAgent = TEST_AGENT,
                predictedTime = updatedStartTime.toInstant(),
                createdTime = etaEvent.createdTime,
                nomination = false,
            )

            assertEquals(expectedEtaEvent, etaEvent)
        }
    }
}
