## Cors related settings
cors.allowed-origins=http://localhost:3000

## spring related properties
spring.jackson.default-property-inclusion=non_null

## mongo related properties
mongodb.host=localhost
mongodb.port=27017
mongodb.authDb=
mongodb.username=
mongodb.password=
mongodb.db=portcall-plus

# Spring boot actuator settings
management.endpoints.enabled-by-default=false
management.endpoint.health.enabled=true
management.endpoint.health.show-details=always

## NxtPort related properties
#NxtPortV2 endpoint
nxtPortV2.enable=false
nxtPortV2.allowOtherSourcePortcallUpdate=true
nxtPortV2.loginUrl=https://login-uat.nxtport.com/connect/token
#username linked to the user
nxtPortV2.userName=test
#password linked to the user
nxtPortV2.password=test
#clientId linked to the user
nxtPortV2.clientId=test
#client secret linked to the user
nxtPortV2.clientSecret=test
#subscription key for the api
nxtPortV2.subscriptionKey=test
#url to fetch the vesselStay info incrementally
nxtPortV2.url=https://api.nxtport.com/portstays/v1/stays
nxtPortV2.cycleDuration=PT15M
## when a new portcall is forced in days
nxtPortV2.newPortcallIntervalInDays=2
nxtPortV2.portOfAntwerp.baseUrl=http://*************:8080
nxtPortV2.portOfAntwerp.stayNumberUrl=/v1/antwerp/stay
nxtPortV2.portOfAntwerp.timeout=PT1M

## Token related configuration
#run a task everyday at 12am to check if invalidated tokens must be reset
token.resetOverTheLimitTokens=0 0 0 * * *

## LIS related properties
lis.enable=false
lis.allowOtherSourcePortcallUpdate=false
lis.cycleDuration=PT15M
## when a new portcall is forced in days
lis.newPortcallIntervalInDays=2

## Sgsin datahub properties
sgmdh.enable=false
sgmdh.allowOtherSourcePortcallUpdate=true
sgmdh.cycleDuration=PT15M
sgmdh.token=[Paste key here]
#number of days of due to arrive data to be fetched from now
sgmdh.dueToArriveData=15
#number of hours in the past, for which agent declaration should be looked up
sgmdh.arrivalDeclarationPastNHours=48
#number of days of due to depart data to be fetched from now
sgmdh.dueToDepartData=3
## when a new portcall is forced in days
sgmdh.newPortcallInterval=20
## The interval in hours from a recently ended portcall.
# This is to avoid create new portcalls when vessel is in and out of the port quickly
sgmdh.portcallExitInterval=5
## when a new portcall is forced in case of a ferry ship type in hours
sgmdh.ferryPortcallInterval=1

## Portcall sync service properties
portcallSync.enable=false
portcallSync.allowOtherSourcePortcallUpdate=true
portcallSync.cycleDuration=PT15M
portcallSync.ports=BEANR
portcallSync.url=http://portcallplus-env.eu-west-1.elasticbeanstalk.com/v1/portcall/updates
portcallSync.username=
portcallSync.password=

portbase.enable=false
portbase.url=https://api.portbase.com/

portbase.iamconnected.enable=false
portbase.iamconnected.allowOtherSourcePortcallUpdate=true
portbase.iamconnected.clientId=teqplay-iamconnected
portbase.iamconnected.accessKey=[accessKey]
portbase.iamconnected.secretAccessKey=[secret]

portbase.vopak.enable=false
portbase.vopak.allowOtherSourcePortcallUpdate=true
portbase.vopak.clientId=teqplay-local-vopak
portbase.vopak.accessKey=[accessKey]
portbase.vopak.secretAccessKey=[secret]

portbase.oudkerk.enable=false
portbase.oudkerk.allowOtherSourcePortcallUpdate=true
portbase.oudkerk.clientId=teqplay-local-oudkerk
portbase.oudkerk.accessKey=[accessKey]
portbase.oudkerk.secretAccessKey=[secret]

portbase.s5.enable=false
portbase.s5.allowOtherSourcePortcallUpdate=true
portbase.s5.clientId=teqplay-local-s5
portbase.s5.accessKey=[accessKey]
portbase.s5.secretAccessKey=[secret]

portbase.mariteam.enable=false
portbase.mariteam.allowOtherSourcePortcallUpdate=true
portbase.mariteam.clientId=teqplay-local-mariteam
portbase.mariteam.accessKey=[accessKey]
portbase.mariteam.secretAccessKey=[secret]

## Enigma related properties
enigma.enable=false
enigma.allowOtherSourcePortcallUpdate=true
enigma.cycleDuration=PT15M
## when a new portcall is forced in days
enigma.newPortcallInterval=20

## Digitraffic related properties
digitraffic.enable=false
digitraffic.allowOtherSourcePortcallUpdate=true
digitraffic.url=https://meri.digitraffic.fi/api/port-call/v1/port-calls
digitraffic.cycleDuration=PT15M
digitraffic.lastPortcallUpdate=PT3H
## when a new portcall is forced in hours
digitraffic.newPortcallInterval=1

## Vopak nomination related properties
vopakNomination.enable=false
vopakNomination.allowOtherSourcePortcallUpdate=true
vopakNomination.cycleDuration=PT15M
vopakNomination.lastPortcallUpdate=PT8H
vopakNomination.username=
vopakNomination.password=

## Platform related properties
platform.url=https://backendprontodev.teqplay.nl/
platform.username=
platform.password=

## Auth related properties
auth.useAuthentication=false

## Auth credentials
auth-credentials.secret=baked-elusive-seminar-banker-citric-dodgy

## Status related parameters
# Time in minutes to set the tolerance for seeing a portcall being updated
status.lastPortcallUpdate=PT30M
# Max amount of minutes a service is allowed to stay down before notifying
status.maxDownTime=PT1H
# Time in minutes to set the tolerance for seeing a vesselStay being updated
status.lastVesselStayUpdate=PT30M
# Report counts in slack
status.reportOnSlack=false
# Scheduled timer to run the slack task
status.slackTimer=-
status.notificationTag=

rabbitmq.enabled=false
#connection to receive regular AIS event data
rabbitmq.uri=amqp://arsvslrq:<EMAIL>/arsvslrq
#queue name to receive regular AIS event data
rabbitmq.queuename=PORTEVENTS_PORTCALLPLUS_DEV
#consume events from the queue if set to true
#message consumption rate. When set to 1, will consume 1 message, ack it and only then process the next. Recommended value is 100
rabbitmq.qos=100
#duration in hours during which atleast one portcall must be expected, else an email/slack message must be sent
rabbitmq.monitorQueueInMinutes=30

nats.enabled=false
nats.password=dYI4ywE2nkKc66oDkfm4sOVd
nats.url=nats://nats.brokers.svc.cluster.local:4222
nats.username=service-portcallplus

#if any mTurk is enabled globally
mTurk.enabled=false
#Flag to enable/disable pulling assignments.
mTurk.pullAssignments=false
mTurk.pushAssignments=true
mTurk.uri=https://mturk-requester-sandbox.us-east-1.amazonaws.com
mTurk.accessKeyId=
mTurk.secretAccessKey=
#uniqueId found in the MTurk dashboard: https://requester.mturk.com
mTurk.hitLayoutId=******************************
#uniqueId found in the SNS dashboard: https://console.aws.amazon.com/sns/v3/home?region=us-east-1
mTurk.snsCallbackDestination=arn:aws:sns:us-east-1:050356841556:PortcallPlus
#auto reject an answer when there is no match
mTurk.autoReject=false
#Period for processing the MTurk HIT assignments
mTurk.hitAssignmentsProcessingPeriod=PT30M
#flag to indicate whether we approve also invalid answers (for boosting Teqplay trust as a good payer towards MTurk workers).
mTurk.approveInvalidAnswers=true
#flag to indicate if manually disposing a HIT (and its assignments) once it's manually expired (after getting at least one valid answer)
#mainly for debugging purposes (to review HIT assignments once they're done).
mTurk.disposeAfterExpiring=true
#max duration in which an assignment must be completed once accepted
mTurk.assignmentDuration=PT15M
#max duration an assignment is valid for
mTurk.assignmentLifetime=PT8H
mTurk.autoApprovalDelay=PT8H
#max number of assignments that can be linked per HIT
mTurk.maxAssignments=5
mTurk.daysConfigured=7
# MTurk scheduled cron to update
mTurk.intervalTimer=-
mTurk.minimumSameValidAnswers=2

#cache
cache.ttl.platform-basic-auth=PT1H
cache.ttl.nxtport-vesselstay-api=PT15M
cache.ttl.ship-fetch-api=PT30M
cache.ttl.maxSize=2000

portevents.ports=NLTNZ, NLVLI, BEGNE, BEANR, SGSIN, USHOU, FIHEL, FIVSS, USCRP, NLRTM, NLAMS

smartfleet.enabled=false
smartfleet.eta.ports=USCRP
smartfleet.eta.uri=amqps://<user>:<pass>@rabbitmqdev.teqplay.nl:5671/SmartFleetDev
smartfleet.eta.queuename=SmartFleetDev-Eta-Events-Tmp-Queue

## Kubernetes config
spring.cloud.kubernetes.config.namespace=portcall
spring.cloud.kubernetes.config.name=
spring.cloud.kubernetes.config.enable-api=false
spring.cloud.kubernetes.config.enabled=false
management.health.rabbit.enabled=false

# Poma client configuration
poma.url=https://backendpomadev.teqplay.nl/
poma.domain=keycloakdev.teqplay.nl
poma.realm=dev
poma.client-id=portcallplus
poma.client-secret=secret
poma.berths-with-port=PT12H

# NATS event configuration
nats.event-stream.enabled=false
nats.event-stream.url=nats://nats.brokers.svc.cluster.local:4222
nats.event-stream.username=portcallplus
nats.event-stream.password=EZfrgz6xfkJ8Js

# Keycloak S2S auth
auth-credentials-keycloak-s2s.domain=keycloakdev.teqplay.nl
auth-credentials-keycloak-s2s.realm=dev
auth-credentials-keycloak-s2s.audience=portcallplus

#Portcall end Fallback
portcallEnd.fallback.runtimeHours=24

status.health-check-timer=-
slack.web-hook=
slack.environment=

internal-api.client-id=secret
internal-api.client-secret=secret
internal-api.domain=secret
internal-api.realm=secret
internal-api.url=secret

corpusChristi.enable=false
corpusChristi.allowOtherSourcePortcallUpdate=false
corpusChristi.cycleDuration=PT15M
corpusChristi.allowAgentUpdate=true
corpusChristi.lastPortcallUpdate=PT24H
corpusChristi.getNearestByImoAndDateRangeDays=10
#Take into account that this time is CET referenced!
corpusChristi.s3.cleanProcessedFilesCron=-
corpusChristi.s3.processedFileMaxAge=P30D
corpusChristi.s3.region=eu-west-1
corpusChristi.s3.bucketName=poccagoanywhere
corpusChristi.s3.accesskey=********************
corpusChristi.s3.secret=NkQRP0xBufqQ2waE8mKBYbVMQ+SOtP8Mue/JyMyk

uncaughtexceptionslackreport.webhook=