/*
 * Change in datastructure of PortCall model. `portcallAlias.source : "VOPAK_NOMINATION"` and "VOPAK_NO_NOR" is changed to per terminal
   8210 -- Penjuru
   8205 -- banyan
   8200 -- sebrok
   8215 -- sakra
*/
db.portcall.find({port: "SGSIN", "portcallAlias.source" : {$in: ["VOPAK_NOMINATION", "VOPAK_NO_NOR"]}}).forEach(function(item) {
    
    var portcallAliases = item.portcallAlias;
    portcallAliases.forEach(function(portcallAlias) {
        if(portcallAlias.alias.includes("CV-8210")) {
            if(portcallAlias.source.includes("VOPAK_NOMINATION")) {
                portcallAlias.source = "VOPAK_NOMINATION_PENJURU"
            }
            else if(portcallAlias.source.includes("VOPAK_NO_NOR")) {
                portcallAlias.source = "VOPAK_NO_NOR_PENJURU"
            }
            print("Updated portcall: "+ item._id + " alias: "+ portcallAlias.alias + " with source: " + portcallAlias.source);
        }
        else if(portcallAlias.alias.includes("CV-8205")) {
            if(portcallAlias.source.includes("VOPAK_NOMINATION")) {
                portcallAlias.source = "VOPAK_NOMINATION_BANYAN"
            }
            else if(portcallAlias.source.includes("VOPAK_NO_NOR")) {
                portcallAlias.source = "VOPAK_NO_NOR_BANYAN"
            }
            print("Updated portcall: "+ item._id + " alias: "+ portcallAlias.alias + " with source: " + portcallAlias.source);
        }
        else if(portcallAlias.alias.includes("CV-8200")) {
            if(portcallAlias.source.includes("VOPAK_NOMINATION")) {
                portcallAlias.source = "VOPAK_NOMINATION_SEBAROK"
            }
            else if(portcallAlias.source.includes("VOPAK_NO_NOR")) {
                portcallAlias.source = "VOPAK_NO_NOR_SEBAROK"
            }
            print("Updated portcall: "+ item._id + " alias: "+ portcallAlias.alias + " with source: " + portcallAlias.source);
        }
        else if(portcallAlias.alias.includes("CV-8215")) {
            if(portcallAlias.source.includes("VOPAK_NOMINATION")) {
                portcallAlias.source = "VOPAK_NOMINATION_SAKRA"
            }
            else if(portcallAlias.source.includes("VOPAK_NO_NOR")) {
                portcallAlias.source = "VOPAK_NO_NOR_SAKRA"
            }
            print("Updated portcall: "+ item._id + " alias: "+ portcallAlias.alias + " with source: " + portcallAlias.source);
        }
    });
    db.portcall.updateOne({_id: item._id}, {$set: {'portcallAlias': portcallAliases}});
});