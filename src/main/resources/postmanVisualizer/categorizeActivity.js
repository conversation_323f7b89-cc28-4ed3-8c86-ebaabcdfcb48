/*
    This is a Postman visualizer script which allows to convert activities categorized per day into a
    nice graph. Copy this script to the 'Tests' tab in the request window of Postman.
    Associated call:
    GET http://{{hostname}}/v1/activity/categorized?taskType=<taskType>>&to=<yyyy-MM-dd'T'HH:mm:ss'Z'>
    &from=<yyyy-MM-dd'T'HH:mm:ss'Z'>&imo=<shipImo>
*/
var template = `
<canvas id="myChart" height="75"></canvas>
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.5.0/Chart.min.js"></script>
<script>
    // Get DOM element to render the chart in
    var ctx = document.getElementById("myChart");

    // Configure Chart JS here.
    var myChart = new Chart(ctx, {
        type: "line",
        data: {
            datasets: []
        },
        options: {
            legend: { display: true },
            title: {
                display: true,
                text: 'Activities'
            }
        }
    });

    // Access the data passed to pm.visualizer.set() from the JavaScript code of the Visualizer template
    pm.getData(function (err, value) {
        var timestamps = [];
        var taskCount = new Map();

        value.forEach(element => {
            if(!timestamps.includes(element.timestamp)) {
                timestamps.push(element.timestamp);
            }
            if(taskCount.has(element.taskName)) {
                taskCount.get(element.taskName).push(element.count);
            }
            else {
                taskCount.set(element.taskName, [element.count]);
            }
        });

        for (let [name, count] of taskCount) {
            var randomColor = '#'+(0x1000000+(Math.random())*0xffffff).toString(16).substr(1,6)
            myChart.data.datasets.push({
                'data': count,
                'label': name,
                'backgroundColor': randomColor,
                'borderColor': randomColor,
                'fill': false
            });
        }
        myChart.data.labels = timestamps;
        myChart.update();
    });
</script>`;

// Set the visualizer template
pm.visualizer.set(template, pm.response.json());