package nl.teqplay.portcallplus.utils

import nl.teqplay.portcallplus.api.model.PortcallVisit
import java.util.Date

fun getStartTime(
    startTime: Date,
    visits: List<PortcallVisit>,
): Date {
    val visitStartTime = getFirstVisitStartTime(visits)
    return visitStartTime ?: startTime
}

fun getFirstVisitStartTime(visits: List<PortcallVisit>): Date? {
    val firstVisit = visits.firstOrNull()
    return firstVisit?.berthAta ?: firstVisit?.berthEta
}

fun isNotValidNewVisit(newVisit: PortcallVisit): Boolean =
    newVisit.berthAta != null && newVisit.berthAtd != null &&
        (newVisit.berthAtd!! <= newVisit.berthAta || newVisit.berthAtd!! <= newVisit.berthEta)

fun List<PortcallVisit>.unsortedEquals(other: List<PortcallVisit>) = this.toSet() == other.toSet()
