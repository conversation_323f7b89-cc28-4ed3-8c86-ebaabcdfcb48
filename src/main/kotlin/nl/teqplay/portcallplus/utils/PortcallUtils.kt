package nl.teqplay.portcallplus.utils

import nl.teqplay.platform.model.area.PilotBoardingPlaceType
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallAlias
import nl.teqplay.portcallplus.api.model.PortcallAliasName
import nl.teqplay.portcallplus.api.model.PortcallStatus
import nl.teqplay.portcallplus.api.model.PortcallVisit
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.UpdateType
import org.joda.time.Instant
import java.util.Date
import nl.teqplay.platform.model.Portcall as PlatformPortcall

/**
 * A function to generate the id of a portcall
 */
fun generatePortcallId(
    port: String,
    imo: String,
    time: Date,
    attempt: Int = 0,
): String {
    return port + imo + fetchTimestamp(time, "yyMMdd") + attempt + "T"
}

/**
 * A simple mapper from platform portcall: [portcall] to a local one
 */
fun convertPlatformPortcall(
    portcall: PlatformPortcall,
    source: ScheduledTaskType,
): Portcall {
    return Portcall(
        portcallId = portcall.portcallId,
        // At the moment the only portcalls that are not known to portcallplus are the portcalls from Hamis and Iris which use the same id as Portbase
        portcallAlias = setOf(PortcallAlias(PortcallAliasName.PORTBASE, portcall.portcallId)),
        port = portcall.port,
        imo = portcall.imo,
        source = source,
        startTime = portcall.startTime?.let { Date(it) } ?: Instant.EPOCH.toDate(),
        startTimeType = UpdateType.NOMINATION,
        status = portcall.status?.let { PortcallStatus.valueOf(it.name) } ?: PortcallStatus.UNKNOWN,
        endTime = portcall.endTime?.let { Date(it) },
        portAtaTime = portcall.portAtaTime?.let { Date(it) },
        portAtdTime = portcall.portAtdTime?.let { Date(it) },
        etd = portcall.etd?.let { Date(it) },
        enteredPBP = portcall.enteredPBP ?: false,
        pilotBoardingPlaceType = portcall.pilotBoardingPlaceType ?: PilotBoardingPlaceType.UNKNOWN,
        vesselAgent = portcall.vesselAgent,
        originUnlocode = portcall.originUnlocode,
        destinationUnlocode = portcall.destinationUnlocode,
        visits = portcall.visits?.mapNotNull { platformVisit ->
            PortcallVisit(
                berthEta = platformVisit.startTime?.let { Date(it) },
                berthEtd = platformVisit.endTime?.let { Date(it) },
                terminal = platformVisit.terminal,
                berthName = platformVisit.berthName,
                arrivalMovementId = platformVisit.arrivalMovementId,
                departureMovementId = platformVisit.departureMovementId,
                connected = platformVisit.connected,
            )
        } ?: listOf(),
    )
}

fun setPortcallAlias(
    portcall: Portcall,
    aliasName: PortcallAliasName?,
    newPortcallId: String?,
): Set<PortcallAlias> {
    val portcallAlias = portcall.portcallAlias.firstOrNull { it.source == aliasName }
    return if (portcallAlias != null || newPortcallId == null || aliasName == null) {
        portcall.portcallAlias
    } else {
        portcall.portcallAlias + PortcallAlias(aliasName, newPortcallId)
    }
}

/**
 * Get the portcallId of a specific service e.g. Simply5 specific portcallId or Portbase portcallId
 */
fun getPortcallByAlias(
    matchingPortcalls: List<Portcall>,
    portcallIds: Set<String>,
    aliasName: PortcallAliasName,
): Map<String, String?> {
    return matchingPortcalls.associate { portcall ->
        // Try to match both the given source and a matching portcallId
        // This is to make sure we match the correct portcall when 2 different nominations are received from the
        // same source for the same portcall. Card: https://trello.com/c/m2iLnQFK
        val aliasByPortcallId = portcall.portcallAlias.firstOrNull { it.alias in portcallIds }
        if (aliasByPortcallId != null) {
            portcall.portcallId to aliasByPortcallId.alias.takeIf { aliasByPortcallId.source == aliasName }
        } else {
            // if missing, match it with the given source only
            portcall.portcallId to portcall.portcallAlias.firstOrNull { it.source == aliasName }?.alias
        }
    }
}
