package nl.teqplay.portcallplus.utils

import okhttp3.Headers.Companion.toHeaders
import okhttp3.HttpUrl.Companion.toHttpUrlOrNull
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response

/**
 * Auxiliar method to prepare and execute a post call with the given OkHttpClient
 */
fun OkHttpClient.executeGetCall(url: String, queryParams: Map<String, String>, headers: Map<String, String>): Response {
    val uriBuilder = url.toHttpUrlOrNull()?.newBuilder().apply {
        queryParams.forEach { (parameter, value) ->
            this?.addQueryParameter(parameter, value)
        }
    } ?: throw IllegalArgumentException("Invalid URL: $url")
    val request = Request.Builder()
        .url(uriBuilder.build())
        .get()
        .headers(headers.toHeaders())
        .build()
    return this.newCall(request).execute()
}

/**
 * Auxiliar method to prepare and execute a post call with the given OkHttpClient
 */
fun OkHttpClient.executePostCall(url: String, postRequestMap: Map<String, Any>, headers: Map<String, String>): Response {
    val jsonBody = mapper
        .writeValueAsString(postRequestMap)
        .toRequestBody("application/json".toMediaTypeOrNull())
    val request = buildPostRequest(url, jsonBody, headers)
    return this.newCall(request).execute()
}

/**
 * Auxiliar method to prepare and execute a post call as Form with the given OkHttpClient
 */
fun OkHttpClient.executePostCallAsForm(url: String, formFields: Map<String, String>, additionalHeaders: Map<String, String> = emptyMap()): Response {
    val formHeaders = mapOf("Content-Type" to "application/x-www-form-urlencoded")
    val headers = formHeaders + additionalHeaders
    val body = okhttp3.FormBody.Builder().apply {
        formFields.forEach {
            add(it.key, it.value)
        }
    }.build()
    val request = buildPostRequest(url, body, headers)
    return this.newCall(request).execute()
}

/**
 * Private auxiliar method to build a Post Request.
 */
private fun buildPostRequest(url: String, body: RequestBody, headers: Map<String, String>): Request {
    return Request.Builder()
        .url(url)
        .post(body)
        .headers(headers.toHeaders())
        .build()
}
