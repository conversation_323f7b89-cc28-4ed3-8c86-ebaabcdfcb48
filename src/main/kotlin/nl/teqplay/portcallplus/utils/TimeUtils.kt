package nl.teqplay.portcallplus.utils

import nl.teqplay.portcallplus.service.common.AuthToken
import org.apache.commons.lang3.time.DurationFormatUtils
import org.joda.time.DateTime
import org.joda.time.DateTimeZone
import java.text.SimpleDateFormat
import java.time.Duration
import java.time.Instant
import java.util.Date
import java.util.Locale
import java.util.TimeZone
import java.util.concurrent.TimeUnit
import kotlin.math.abs

const val TIMESTAMP_FORMAT = "yyyy-MM-dd'T'HH:mm:ss'Z'"
const val READABLE_FORMAT = "yyyy-MM-dd HH:mm:ss"
private const val DURATION_FORMAT = "HH:mm:ss"
const val DURATION_FORMAT_MS = "HH:mm:ss.SSS"
private val UTC_TIMEZONE = TimeZone.getTimeZone("UTC")
val NL_TIMEZONE: TimeZone = TimeZone.getTimeZone("Europe/Brussels")

/**
 * Gets the time in [Date] format corresponding to the given timestamp in the given format and timezone
 */
fun fetchTimestamp(
    timestamp: String,
    format: String = TIMESTAMP_FORMAT,
    timeZone: TimeZone = UTC_TIMEZONE,
): Date {
    val dateFormat = SimpleDateFormat(format, Locale.ENGLISH)
    dateFormat.timeZone = timeZone
    return dateFormat.parse(timestamp)
}

/**
 * Gets the time in [Date] format corresponding to the given timestamp in the given format and timezone
 */
fun fetchTimestampOrNull(
    timestamp: String?,
    format: String = TIMESTAMP_FORMAT,
    timeZone: TimeZone = UTC_TIMEZONE,
): Date? {
    if (timestamp == null) {
        return null
    }
    return fetchTimestamp(timestamp, format, timeZone)
}

/**
 * Adds [addTime] to the given [timestamp] based on the given unit: [unit]
 */
fun fetchTimestamp(
    timestamp: Date,
    addTime: Number,
    unit: TimeUnit = TimeUnit.MILLISECONDS,
): Date {
    val millisToAdd = unit.toMillis(addTime.toLong())
    return Date(timestamp.time.plus(millisToAdd))
}

/**
 * Adds [duration] to the given [timestamp]
 */
fun fetchTimestamp(
    timestamp: Date,
    duration: Duration,
): Date {
    return Date(timestamp.time.plus(duration.toMillis()))
}

/**
 * Fetch readable time in mm,ss from the given millis
 */
fun fetchReadableTimeFromMillis(
    millis: Long,
    format: String = DURATION_FORMAT,
): String {
    var readableTime = DurationFormatUtils.formatDuration(abs(millis), format)
    // if millis is lesser than zero, add a negative sign
    if (millis < 0) readableTime = "-$readableTime"
    return readableTime
}

/**
 * Fetch readable timestamp in ISO format from millis
 */
fun millisToIso(
    timestamp: Long,
    format: String = TIMESTAMP_FORMAT,
    timeZone: TimeZone = UTC_TIMEZONE,
): String {
    val dateFormat = SimpleDateFormat(format, Locale.ENGLISH)
    dateFormat.timeZone = timeZone
    return dateFormat.format(Date(timestamp))
}

/**
 * Gets the date at the start of the given [date]
 */
fun getStartOfTheDay(
    date: Date,
    timeZone: TimeZone = TimeZone.getTimeZone("UTC"),
): Date {
    val dateTime = DateTime(date.time).withZone(DateTimeZone.forTimeZone(timeZone))
    return Date(date.time - dateTime.millisOfDay)
}

/**
 * Returns true, if the given [date] falls between [intervalFrom] and [intervalTo]
 */
fun isTimeInInterval(
    date: Date,
    intervalFrom: Date,
    intervalTo: Date,
): Boolean {
    return date.after(intervalFrom) && date.before(intervalTo)
}

/**
* Fetch the given data in the required format
*/
fun fetchTimestamp(
    date: Date,
    format: String = TIMESTAMP_FORMAT,
    timeZone: TimeZone = TimeZone.getTimeZone("UTC"),
): String {
    val simpleDateFormat = SimpleDateFormat(format)
    simpleDateFormat.timeZone = timeZone
    return simpleDateFormat.format(date)
}

/**
 * Trim milliseconds from the given [date]
 */
fun fetchMillisTrimmedTimestamp(
    date: Date,
    format: String = TIMESTAMP_FORMAT,
    timeZone: TimeZone = TimeZone.getTimeZone("UTC"),
): Date = fetchTimestamp(fetchTimestamp(date, format, timeZone))

/**
 * Returns true if the time gap between [fromDate] and [toDate] is lesser than the given [duration]
 */
fun isTimeInRange(
    fromDate: Date,
    toDate: Date,
    duration: Long,
    unit: TimeUnit = TimeUnit.MILLISECONDS,
) = ((toDate.time - fromDate.time) - unit.toMillis(duration)) <= 0

/**
 * For a given [from] and [to] dates, returns a pair of date which is in the configured days apart
 */
fun getTimeInRange(
    from: Date?,
    to: Date?,
    maxRangeInMillis: Long,
): Pair<Date, Date> {
    var fromDate = from
    var toDate = to
    if (from == null && to != null) {
        fromDate = fetchTimestamp(to, -1 * maxRangeInMillis)
    } else if (from != null && to == null) {
        toDate = fetchTimestamp(from, maxRangeInMillis)
    }
    toDate = toDate ?: Date()
    fromDate = fromDate ?: fetchTimestamp(toDate, -1 * maxRangeInMillis)

    if (!isTimeInRange(fromDate, toDate, maxRangeInMillis)) {
        fromDate = fetchTimestamp(toDate, -1 * maxRangeInMillis)
    }
    return Pair(fromDate, toDate)
}

fun fetchTimestamp(date: Instant): Date {
    return Date(date.toEpochMilli())
}

/**
 * Returns true if this [AuthToken] is valid
 */
fun AuthToken.isValid() = this.expiration?.after(fetchTimestamp(Date(), Duration.ofMinutes(5))) == true
