package nl.teqplay.portcallplus.utils

import java.time.Duration
import java.time.Instant
import kotlin.math.abs

data class CacheObject<T>(
    val time: Instant,
    val item: T,
)

class ItemCache<T, I>(private val getter: (I) -> T, private val cacheAge: Duration) {
    private val cache: HashMap<I, CacheObject<T>> = HashMap()

    fun get(identifier: I): T? {
        val item: T?
        val cacheResult = cache[identifier]
        val isOldCache = cacheResult != null && (abs(Instant.now().compareTo(cacheResult.time)) > cacheAge.toMillis())

        if (cache[identifier] == null || isOldCache) {
            item = catchingHttpCall { getter(identifier) }
            if (item != null) {
                cache.put(identifier, CacheObject(Instant.now(), item))
            }
            return item
        }
        return cache[identifier]?.item
    }
}
