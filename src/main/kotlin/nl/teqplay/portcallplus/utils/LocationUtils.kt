package nl.teqplay.portcallplus.utils

import nl.teqplay.platform.model.Location as PlatformLocation
import nl.teqplay.poma.api.v1.Location as PomaLocation
import nl.teqplay.skeleton.model.Location as SkeletonLocation

fun PomaLocation.toPlatformLocation(): PlatformLocation = PlatformLocation(this.latitude, this.longitude)

fun PlatformLocation.toSkeletonLocation(): SkeletonLocation = SkeletonLocation(lat = this.latitude, lon = this.longitude)

fun PomaLocation.toSkeletonLocation(): SkeletonLocation = SkeletonLocation(lat = this.latitude, lon = this.longitude)

fun List<PomaLocation>.toPlatformLocations(): List<PlatformLocation> = this.map { it.toPlatformLocation() }

fun List<PlatformLocation>.toSkeletonLocations(): List<SkeletonLocation> = this.map { it.toSkeletonLocation() }

fun pointInArea(
    area: List<SkeletonLocation>,
    check: SkeletonLocation,
): <PERSON><PERSON>an {
    // check the amount of intersects with the lines defining the bounding box,
    // if even, point is outside, if odd, point is inside polygon
    var result = false
    var i = 0
    var j = area.size - 1

    while (i < area.size) {
        if (check == area[i]) {
            return true
        }
        if (intersectsLine(area[i], area[j], check)) {
            result = !result
        }
        j = i++
    }
    return result
}

/**
 * Check if the point intersects with the line from start to end
 * @return true if the point intersects with the line
 */
private fun intersectsLine(
    start: SkeletonLocation,
    end: SkeletonLocation,
    point: SkeletonLocation,
) = longitudeBetween(start, point, end) &&
    (point.lat < latitudeDiff(end, start) * longitudeDiff(point, start) / longitudeDiff(end, start) + start.lat)

/**
 * Check if the longitude of the point is between boundary 1 point and boundary2 point
 */
private fun longitudeBetween(
    boundary1: SkeletonLocation,
    point: SkeletonLocation,
    boundary2: SkeletonLocation,
) = (boundary1.lon <= point.lon && point.lon < boundary2.lon) ||
    (boundary2.lon <= point.lon && point.lon < boundary1.lon)

/**
 * @return the difference between latitudes
 */
private fun latitudeDiff(
    one: SkeletonLocation,
    another: SkeletonLocation,
) = one.lat - another.lat

/**
 * @return the difference between longitudes
 */
private fun longitudeDiff(
    one: SkeletonLocation,
    another: SkeletonLocation,
) = one.lon - another.lon
