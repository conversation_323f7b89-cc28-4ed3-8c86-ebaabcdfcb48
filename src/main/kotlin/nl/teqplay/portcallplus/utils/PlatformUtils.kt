package nl.teqplay.portcallplus.utils

import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.skeleton.model.Location

/**
 * Gets the corresponding UNLOCode of the port based on the given [destination]. If no matches are seen, returns null
 */
fun getScheldePort(destination: String?) =
    when {
        destination == null -> null
        BEANR_DESTINATIONS.any { destination.lowercase().contains(it) } -> Portcall.IDPREFIX_ANR
        NLTNZ_DESTINATIONS.any { destination.lowercase().contains(it) } -> Portcall.IDPREFIX_TNZ
        NLVLI_DESTINATIONS.any { destination.lowercase().contains(it) } -> Portcall.IDPREFIX_VLI
        BEGNE_DESTINATIONS.any { destination.lowercase().contains(it) } -> Portcall.IDPREFIX_GNE
        BEOST_DESTINATIONS.any { destination.lowercase().contains(it) } -> Portcall.IDPREFIX_OST
        BEZEE_DESTINATIONS.any { destination.lowercase().contains(it) } -> Portcall.IDPREFIX_ZEE
        BEWLB_DESTINATIONS.any { destination.lowercase().contains(it) } -> Portcall.IDPREFIX_WLB
        else -> null
    }

/**
 * Approximate reference to the center of all Schelde ports
 */
val SCHELDE_CENTER = Location(51.**************, 3.***************)

private val BEANR_DESTINATIONS = arrayOf("beanr", "be anr", "beant", "antwerp", "be ant", "steenbank", "wandelaar", "antwerpen")
private val NLTNZ_DESTINATIONS = arrayOf("terneuzen", "nltnz", "nl tnz")
private val NLVLI_DESTINATIONS = arrayOf("vlissingen", "flushing", "nlvli", "nl vli")
private val BEGNE_DESTINATIONS = arrayOf("gent", "ghent", "begne", "be gne")
private val BEZEE_DESTINATIONS = arrayOf("zeebrugge", "bezee")
private val BEOST_DESTINATIONS = arrayOf("oostende", "beost")
private val BEWLB_DESTINATIONS = arrayOf("bewlb", "boven-zeeschelde", "boven zeeschelde", "bovenzee", "boven zee")
