package nl.teqplay.portcallplus.utils

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.MapperFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.databind.SerializerProvider
import com.fasterxml.jackson.databind.json.JsonMapper
import com.fasterxml.jackson.databind.module.SimpleModule
import com.fasterxml.jackson.databind.ser.std.StdSerializer
import com.fasterxml.jackson.datatype.joda.JodaModule
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.KotlinModule
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.skeleton.util.FileLog
import org.apache.commons.lang3.StringUtils
import org.springframework.web.client.HttpClientErrorException
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.util.Date
import java.util.logging.Level

val log = KotlinLogging.logger {}
val mapper: ObjectMapper by lazy {
    val dateModule = SimpleModule("dateModule")
    dateModule.addSerializer(Date::class.java, DateSerializer())

    JsonMapper.builder()
        .addModule(JodaModule())
        .addModule(JavaTimeModule())
        .addModule(dateModule)
        .addModule(KotlinModule.Builder().build())
        .enable(MapperFeature.USE_BASE_TYPE_AS_DEFAULT_IMPL)
        .disable(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES)
        .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
        .disable(SerializationFeature.FAIL_ON_EMPTY_BEANS)
        .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
        .serializationInclusion(JsonInclude.Include.NON_NULL)
        .build()
}

/**
 * Standardized logging for the application
 */
fun log(
    portcallId: String = "",
    imoNumber: String = "",
    port: String = "",
    message: String,
    level: Level = Level.INFO,
) {
    val stackTraceRef = getStackRef()
    val prefix = if (portcallId.isNotEmpty() || imoNumber.isNotEmpty() || port.isNotEmpty()) "$port:$portcallId:$imoNumber -" else ""
    when (level) {
        Level.FINE, Level.FINER, Level.FINEST -> log.debug { "$stackTraceRef$prefix $message" }
        Level.INFO -> log.info { "$stackTraceRef$prefix $message" }
        Level.WARNING -> log.warn { "$stackTraceRef$prefix $message" }
        Level.SEVERE -> log.error { "$stackTraceRef$prefix $message" }
    }
}

/**
 * Gets the location where this method was called outside this class
 */
private fun getStackRef(): String {
    val stackTrace = Thread.currentThread().stackTrace.firstOrNull {
        !it.className.contains("Thread") && !it.className.contains("GenericUtils", true)
    }
    return if (stackTrace != null) "[${stackTrace.fileName}:${stackTrace.lineNumber}] " else ""
}

/**
 * Standardized logging for the application
 */
fun log(
    portcall: Portcall,
    message: String,
    level: Level = Level.INFO,
) = log(portcall.portcallId, portcall.imo, portcall.port, message, level)

/**
 * Reads a json and converts into the given type: [T]. Exceptions are caught and logged. Returns null in that case
 */
fun <T> readFromString(
    jsonString: String?,
    typeReference: TypeReference<T>,
): T? {
    return if (StringUtils.isBlank(jsonString)) {
        null
    } else {
        try {
            mapper.readValue<T>(jsonString, typeReference)
        } catch (ex: Exception) {
            log(message = "Error while deserializing json. Message: $ex", level = Level.SEVERE)
            null
        }
    }
}

/**
 * Reads a json and converts into the given type: [T]. Exceptions are caught and logged. Returns null in that case
 */
fun <T> readFromString(
    jsonString: String,
    clazz: Class<T>,
): T? {
    return try {
        mapper.readValue(jsonString, clazz)
    } catch (ex: Exception) {
        log(message = "Error while deserializing json. Message: $ex", level = Level.SEVERE)
        null
    }
}

/**
 * Removes the '[' and ']' from the serialized version of this collection
 */
fun <E> Collection<E>.removeBraces(): String {
    return toString().replace("[", "").replace("]", "")
}

/**
 * Helper method to log any nominations in a seperate log: `nominations.log`
 */
fun logNomination(message: String) {
    val prefix = getStackRef().let { if (it.isNotEmpty()) "$it:" else "" }
    FileLog().writeToLogFile("$$prefix $message".trim(), "nominations.out")
}

/**
 * Converts [feet] and [inches] to decimeters
 */
fun convertftIncestoDM(
    feet: Int?,
    inches: Int?,
): Double? {
    return if (feet != null || inches != null) {
        (feet?.let { it * 3.048 } ?: 0.0) + (inches?.let { it * 0.254 } ?: 0.0)
    } else {
        null
    }
}

class DateSerializer : StdSerializer<Date>(Date::class.java) {
    private val fmt = DateTimeFormatter.ISO_DATE_TIME.withZone(ZoneOffset.UTC)

    override fun serialize(
        value: Date?,
        gen: JsonGenerator,
        provider: SerializerProvider,
    ) {
        if (value == null) {
            gen.writeNull()
        } else {
            gen.writeString(fmt.format(value.toInstant()))
        }
    }
}

fun <T> catchingHttpCall(
    retry: Boolean = true,
    retryCounter: Int = 3,
    function: () -> T,
): T? {
    return runCatching { function() }
        .onFailure {
            if (it is HttpClientErrorException && it.statusCode.value() == 404) {
                return null
            }
            if (retry && retryCounter < 0) {
                log(message = "HTTP request failed, retrying")
                return catchingHttpCall(retry, retryCounter - 1, function)
            }
        }
        .getOrThrow()
}

/**
 * Removes all special characters from the given [answer]
 */
fun removeSpecialChars(answer: String): String = answer.replace(Regex("[^a-zA-Z0-9]"), "").uppercase()
