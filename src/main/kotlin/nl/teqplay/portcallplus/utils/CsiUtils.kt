package nl.teqplay.portcallplus.utils

import nl.teqplay.csi.model.ship.info.ShipRegisterInfo
import nl.teqplay.csi.model.ship.info.component.ShipAdministration
import nl.teqplay.csi.model.ship.info.component.ShipCommunication
import nl.teqplay.csi.model.ship.info.component.ShipDimensions
import nl.teqplay.csi.model.ship.info.component.ShipIdentifiers
import nl.teqplay.csi.model.ship.info.component.ShipScores
import nl.teqplay.csi.model.ship.info.component.ShipSpecification
import nl.teqplay.csi.model.ship.info.component.ShipTypes

fun getShipRegister(
    mmsi: String? = null,
    imo: String? = null,
    name: String? = null,
    length: Double? = null,
) = ShipRegisterInfo(
    identifiers = ShipIdentifiers(
        mmsi = mmsi,
        imo = imo,
        name = name,
        callSign = null,
        eni = null,
    ),
    administration = ShipAdministration(null, null, null, null, null, null, null, null, null),
    communication = ShipCommunication(null),
    dimensions = ShipDimensions(
        length = length,
    ),
    scores = ShipScores(null, null),
    specification = ShipSpecification(),
    types = ShipTypes(),
)
