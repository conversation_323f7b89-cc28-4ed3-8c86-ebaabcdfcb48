package nl.teqplay.portcallplus.utils

import com.mongodb.kotlin.client.FindIterable
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.model.ScheduledTaskTypeFieldsProxy
import nl.teqplay.portcallplus.model.data.ServiceFetchCounter
import nl.teqplay.portcallplus.model.data.UpdateType
import nl.teqplay.portcallplus.model.httpResponse.ServiceFetchCounterResult
import nl.teqplay.portcallplus.model.httpResponse.ServiceFetchCounterResult.PortcallUpdateDetails
import java.util.Date

/**
 * Gets a count of all NxtPort requests done in total and also per imoNumber of the vessel
 * @param serviceCounters List of all [ServiceFetchCounter]
 * @param portcallIds Only fetch counters for these portcallIds
 * @param updateType Only fetch counters for this updateType
 * @param detailedFetch Fetch counter details along with event timestamps
 * @param onlyPortcall Only fetch counter details that resulted in portcall updates
 */
fun getServiceFetchCounters(
    serviceCounters: FindIterable<ServiceFetchCounter>,
    portcallIds: Set<String>,
    updateType: UpdateType?,
    detailedFetch: Boolean,
    onlyPortcall: Boolean,
    scheduledTaskTypeFieldsProxy: ScheduledTaskTypeFieldsProxy,
): ServiceFetchCounterResult {
    val result = ServiceFetchCounterResult()
    serviceCounters.forEach { count ->
        if (updateType != null) {
            updateServiceFetchCounter(count, updateType, portcallIds, result, detailedFetch, onlyPortcall, scheduledTaskTypeFieldsProxy)
        } else {
            UpdateType.values().forEach {
                updateServiceFetchCounter(count, it, portcallIds, result, detailedFetch, onlyPortcall, scheduledTaskTypeFieldsProxy)
            }
        }
    }
    return result
}

private fun updateServiceFetchCounter(
    count: ServiceFetchCounter,
    updateType: UpdateType,
    portcallIds: Set<String>,
    result: ServiceFetchCounterResult,
    detailedFetch: Boolean,
    onlyPortcall: Boolean,
    scheduledTaskTypeFieldsProxy: ScheduledTaskTypeFieldsProxy,
) {
    val taskName = scheduledTaskTypeFieldsProxy.getFields(count.taskType).taskFullName
    // update the counters for the event updates
    count.getEventUpdateType(updateType).parallelStream().forEach { details ->
        updateCounters(
            count.timestamp, taskName, details.reference, details.updatedEventType, result, onlyPortcall,
            detailedFetch, portcallIds, updateType,
        )
    }
    // update the counters for non-event updates
    count.getNoEventUpdateType(updateType).parallelStream().forEach { reference ->
        updateCounters(
            count.timestamp, taskName, reference, null, result, onlyPortcall,
            detailedFetch, portcallIds, updateType,
        )
    }
}

private fun updateCounters(
    taskTimestamp: Date,
    taskTypeNameFull: String,
    reference: String,
    updatedEventType: Date?,
    result: ServiceFetchCounterResult,
    onlyPortcall: Boolean,
    detailedFetch: Boolean,
    portcallIds: Set<String>,
    updateType: UpdateType,
) {
    if ((!onlyPortcall || isPortcallId(reference)) && (portcallIds.isEmpty() || portcallIds.contains(reference))) {
        // only update the total count for successful NxtPort calls
        if (updateType != UpdateType.FAILED_UPDATE) result.total.getAndIncrement()
        if (detailedFetch) {
            result.addPortcallDetails(
                reference,
                updateType,
                PortcallUpdateDetails(taskTimestamp, taskTypeNameFull, updatedEventType),
            )
        }
        // increment the counter
        when (updateType) {
            UpdateType.CREATE -> result.counter.createCount.getAndIncrement()
            UpdateType.ETA_UPDATE -> result.counter.etaUpdateCount.getAndIncrement()
            UpdateType.BERTH_UPDATE -> result.counter.berthUpdateCount.getAndIncrement()
            UpdateType.ETD_UPDATE -> result.counter.etdUpdateCount.getAndIncrement()
            UpdateType.AGENT_UPDATE -> result.counter.agentUpdateCount.getAndIncrement()
            UpdateType.FINISH -> result.counter.finishCount.getAndIncrement()
            UpdateType.NO_UPDATE -> result.counter.noUpdatesCount.getAndIncrement()
            UpdateType.EMPTY_RESULT -> result.counter.noStaysCount.getAndIncrement()
            UpdateType.FAILED_UPDATE -> result.counter.failedCount.getAndIncrement()
        }
    }
}

/**
 * Returns true if the given [id] is a portcallId. Antwerp portcalls start with a V, Rotterdam ones start with an
 * NLRTM etc
 */
private fun isPortcallId(id: String): Boolean {
    return id.startsWith("V") || id.startsWith(Portcall.IDPREFIX_NLRTM) || id.startsWith(Portcall.IDPREFIX_NLAMS) ||
        id.startsWith(Portcall.IDPREFIX_TNZ) || id.startsWith(Portcall.IDPREFIX_VLI) ||
        id.startsWith(Portcall.IDPREFIX_GNE) || id.startsWith(Portcall.IDPREFIX_SGSIN)
}

fun String.getProperBerthName(): String {
    // Remove prefix from berth names, e.g. BEANR11113LS903 -> S903
    if (this.startsWith("BEANR")) {
        return this.substring(this.length - 4, this.length)
    }
    return this
}
