package nl.teqplay.portcallplus.utils

import jakarta.servlet.ServletOutputStream
import nl.teqplay.portcallplus.api.model.Portcall
import org.apache.poi.ss.usermodel.Row
import org.apache.poi.ss.usermodel.Sheet
import org.apache.poi.ss.usermodel.Workbook
import org.apache.poi.xssf.streaming.SXSSFWorkbook

/**
 * Export the Portcalls and write to the outputStream
 */
fun exportPortcallsToExcel(
    portcalls: Collection<Portcall>,
    outputStream: ServletOutputStream,
) {
    val workbook = createPortcallsWorkbook(portcalls)
    createExcelStream(workbook, outputStream)
    disposeExcel(workbook)
}

/**
 * Create the excel workbook containing the portcalls provided
 */
fun createPortcallsWorkbook(portcalls: Collection<Portcall>): Workbook {
    val workbook: Workbook = createLargeExcelFile()
    val sheet: Sheet = createExcelSheet(workbook)
    var rowNr = 0
    // create header
    createExcelRow(sheet, rowNr++, listOf("Portcall", "Port", "Agent", "Start Time", "End Time", "Berths"))

    val timestampFormat = "yyyy-MM-dd HH:mm 'UTC'"
    // create rows
    portcalls.forEach {
        val atdString = it.portAtdTime.let { atdTime ->
            if (atdTime == null) "" else fetchTimestamp(atdTime, timestampFormat)
        }
        val standardValues: List<String?> = listOf(
            it.portcallId,
            it.port,
            it.vesselAgent,
            fetchTimestamp(it.startTime, timestampFormat),
            atdString,
        )
        val berths: List<String> = it.visits.mapNotNull { it.berthName }.toList()
        createExcelRow(sheet, rowNr++, standardValues.plus(berths))
    }
    return workbook
}

/**
 * Create a SXSSFWorkbook
 * This type will store the excel on the disk during the creation of the complete excel
 * and will keep only rowsInMemory amount of lines in memory
 * If you wish to create large excel files containing more than a 1000 of lines consider using this
 * Note: always call the dispose function after finishing this
 */
private fun createLargeExcelFile(rowsInMemory: Int = 100) = SXSSFWorkbook(rowsInMemory)

/**
 * Create an Excel Worksheet
 */
private fun createExcelSheet(
    workbook: Workbook,
    name: String? = null,
): Sheet = if (name == null) workbook.createSheet() else workbook.createSheet(name)

/**
 * Create an empty row containing nothing
 */
private fun createEmptyExcelRow(
    sheet: Sheet,
    rowNr: Int,
): Row = sheet.createRow(rowNr)

/**
 * Write a row and all cells within that row
 */
private fun createExcelRow(
    sheet: Sheet,
    rowNr: Int,
    values: List<String?>,
): Row {
    val row: Row = createEmptyExcelRow(sheet, rowNr)
    values.forEachIndexed { cellIndex, value ->
        writeExcelCell(sheet, rowNr, cellIndex, value ?: "")
    }
    return row
}

/**
 * Write to a specific cell
 */
private fun writeExcelCell(
    sheet: Sheet,
    rowNr: Int,
    cellNr: Int,
    value: String,
) {
    sheet.getRow(rowNr).createCell(cellNr).setCellValue(value)
}

/**
 *
 */
private fun createExcelStream(
    workbook: Workbook,
    servletOutputStream: ServletOutputStream,
) {
    workbook.write(servletOutputStream)
}

/**
 * Always call this function after you are done with the workbook
 * This will delete the temporary files if a SXSSFWorkbook is used
 * And will close the workbook
 */
private fun disposeExcel(workbook: Workbook) {
    workbook.close()
}
