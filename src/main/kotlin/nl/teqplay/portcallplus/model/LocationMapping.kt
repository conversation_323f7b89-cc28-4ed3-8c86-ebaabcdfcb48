package nl.teqplay.portcallplus.model

import com.fasterxml.jackson.annotation.JsonIgnore
import nl.teqplay.portcallplus.api.model.DataModel
import nl.teqplay.skeleton.model.Location
import org.bson.types.ObjectId

typealias Unlocode = String

/**
 * Data class to map external location name + source + port to a location to be later resolved to get the Teqplay location.
 */
data class LocationMapping(
    val source: String,
    val port: Unlocode,
    val name: String,
    val location: Location?,
) : DataModel() {
    @JsonIgnore
    override val _id = ObjectId()
}
