package nl.teqplay.portcallplus.model.data.portbase

// PortBase class name: BezoekschipOrganisation
data class VisitShipOrganisation(
    val fullName: String?,
    val shortName: String?,
    val iamConnectedId: String?,
    val portAuthorityId: String?,
    val emailAddress: String?,
    val address: String?,
    val city: String?,
    val zipCode: String?,
    val countryUnCode: String?,
    val countryName: String?,
    val phoneNumber: String?,
    val faxNumber: String?,
    val contact: String?,
    val customsEORINumber: String?,
    val ean: String?,
    val chamberOfCommerceNumber: String?,
    val scacCode: String?,
)
