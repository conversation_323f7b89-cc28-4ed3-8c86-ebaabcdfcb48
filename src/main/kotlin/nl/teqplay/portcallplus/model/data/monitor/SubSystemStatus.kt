package nl.teqplay.portcallplus.model.data.monitor

import org.springframework.boot.actuate.health.Status

/**
 * This data class contains status information for an subsystems of a system. Those subsystems can be anything. Like
 * an adapter or task, for all subsystems in the system a companion object is created. This companion object will make
 * a new SubsystemStatus from the given subsystem. This data class is a DTO (Data Transfer Object) to communicate the
 * subsystem status across multiple systems.
 *
 * <AUTHOR>
 */
data class SubSystemStatus(
    val name: String,
    val enabled: Boolean,
    val status: Status,
    val detailedStatus: String?,
    val errorMessage: String?,
    val lastUpdated: String,
    val start: String,
    val stop: String,
    val restart: String? = null,
    val terminate: String? = null,
)
