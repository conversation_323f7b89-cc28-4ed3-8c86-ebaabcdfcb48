package nl.teqplay.portcallplus.model.data.digitraffic

data class ImoInformation(
    val briefParticularsVoyage: String?,
    val cargoDeclarationOb: Int?,
    val crewListsOb: Int?,
    val crewsEffectsDeclarationsOb: Int?,
    val healthDeclarationsOb: Int?,
    val imoGeneralDeclaration: String?,
    val numberOfCrew: Int?,
    val numberOfPassangers: Int?,
    val passangerListsOb: Int?,
    val portOfDischarge: String?,
    val shipStoresDeclarationsOb: Int?,
)
