package nl.teqplay.portcallplus.model.data.portbase

data class Consignment(
    val actualDeparture: String?,
    val agentVoyageNumber: String?,
    val bulkAuthorisations: List<VisitShipOrganisation>?,
    val consignee: Party?,
    val consignmentNumber: String?,
    val consignor: Party?,
    val customsProcess: String?,
    val customsStatus: String?,
    val exchangeRate: ExchangeRate?,
    val goodsItems: List<GoodsItem>?,
    val movementReferenceNumbers: List<String>?,
    val movementType: String?,
    val onCarriageDetails: OnCarriageDetails?,
    val partShipment: Boolean?,
    val partiesToNotify: List<Party>?,
    val placeOfDelivery: String?,
    val placeOfDestination: PlaceOfOrigin?,
    val placeOfOrigin: PlaceOfOrigin?,
    val portOfDischarge: Port?,
    val portOfLoading: Port?,
    val terminal: Terminal?,
    val warehouseLicense: WarehouseLicense?,
    val beenAccepted: Boolean?,
    val bulk: Boolean?,
    val declarationStatus: String?,
)
