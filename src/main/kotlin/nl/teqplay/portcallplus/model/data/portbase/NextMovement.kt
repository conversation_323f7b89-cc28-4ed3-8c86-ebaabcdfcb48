package nl.teqplay.portcallplus.model.data.portbase

data class NextMovement(
    val vesselDraft: Double?,
    val vesselMasterName: String?,
    val numberOfCrew: Int?,
    val numberOfPassengers: Int?,
    val cargo: String?,
    val orderEmail: String?,
    val orderSms: String?,
    val pilotService: PilotService?,
    val order: Boolean?,
    val cancellationReason: String?,
    val pilotExemption: PilotExemption?,
    val offStandardBeam: OffStandardBeam?,
    val stormPilotageInformation: StormPilotageInformation?,
)
