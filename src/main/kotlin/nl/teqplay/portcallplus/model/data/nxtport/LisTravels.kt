package nl.teqplay.portcallplus.model.data.nxtport

import com.fasterxml.jackson.annotation.JsonIgnore
import nl.teqplay.portcallplus.api.model.DataModel
import nl.teqplay.portcallplus.api.model.PortcallStatus
import nl.teqplay.portcallplus.model.service.LisTravel
import org.bson.codecs.pojo.annotations.BsonId

data class LisTravels(
    /** The portcall reference number, UCRN for Rotterdam  */
    @BsonId
    val type: PortcallStatus,
    val travels: List<LisTravel>? = null,
) : DataModel() {
    @JsonIgnore
    override val _id = type
}
