package nl.teqplay.portcallplus.model.data.portbase

data class SecurityDeclaration(
    val securityReportRequired: String?,
    val companySecurityOfficer: CompanySecurityOfficer?,
    val currentSecurityLevel: String?,
    val approvedSspDocumentOnBoard: Boolean?,
    val isscSecurityDocument: IsscSecurityDocument?,
    val shipToShipActivities: List<ShipToShipActivities>?,
    val securityRelatedMatter: String?,
    val reasonLessPortFacilities: String?,
)
