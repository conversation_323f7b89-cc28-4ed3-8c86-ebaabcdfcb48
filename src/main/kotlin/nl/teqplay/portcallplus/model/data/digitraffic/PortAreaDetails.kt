package nl.teqplay.portcallplus.model.data.digitraffic

import java.util.Date

data class PortAreaDetails(
    val arrivalDraught: Int,
    // The Actual time of arrival
    val ata: Date?,
    val ataSource: String?,
    // The time the ata is entered in their system
    val ataTimestamp: Date?,
    // The actual time of departure
    val atd: Date?,
    val atdSource: String?,
    // The time the atd is entered in their system
    val atdTimestamp: Date?,
    val berthCode: String?,
    val berthName: String?,
    val departureDraught: Int,
    // The expected time of arrival
    val eta: Date?,
    val etaSource: String?,
    // The time the eta is entered in their system
    val etaTimestamp: Date?,
    // The expected time of departure
    val etd: Date?,
    val etdSource: String?,
    // The time the etd is entered in their system
    val etdTimestamp: Date?,
    val portAreaCode: String?,
    val portAreaName: String?,
)
