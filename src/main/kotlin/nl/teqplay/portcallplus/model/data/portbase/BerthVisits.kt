package nl.teqplay.portcallplus.model.data.portbase

data class BerthVisits(
    val id: String?,
    val callId: String?,
    val berth: Berth?,
    val terminal: Terminal?,
    val stevedore: Stevedore?,
    val eta: String?,
    val requestedEta: String?,
    val ata: String?,
    val etd: String?,
    val requestedEtd: String?,
    val atd: String?,
    val mooring: String?,
    val visitPurposes: List<String>?,
    val tugboatAtArrival: Tugboat?,
    val boatmenAtArrival: Boatmen?,
    val boatmenAtDeparture: Boatmen?,
    val tugboatAtDeparture: Tugboat?,
    val nextMovement: NextMovement?,
    val remarks: String?,
    val bollardFrom: Int?,
    val bollardTo: Int?,
    val vesselServiceCode: String?,
    val vesselServiceName: String?,
    val discharge: Int?,
    val load: Int?,
    val externalInfo: ExternalInfo?,
)
