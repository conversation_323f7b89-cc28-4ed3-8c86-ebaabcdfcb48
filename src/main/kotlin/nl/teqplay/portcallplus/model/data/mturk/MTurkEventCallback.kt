package nl.teqplay.portcallplus.model.data.mturk

import com.fasterxml.jackson.annotation.JsonProperty
import java.util.Date

/**
 * Class to encapsulate all details received from MTurk callback via the configured SNS
 * E.g json:
 * {
 "Events": [
 {
 "Answer": "<?xml version=\"1.0\" encoding=\"ASCII\"?><QuestionFormAnswers xmlns=\"http://mechanicalturk.amazonaws.com/AWSMechanicalTurkDataSchemas/2005-10-01/QuestionFormAnswers.xsd\"><Answer><QuestionIdentifier>agent</QuestionIdentifier><FreeText>Grimaldi belgium nv (GRIMAL)</FreeText></Answer></QuestionFormAnswers>",
 "HITGroupId": "34KQN2TBL0ZN33DFWTQTGGHYPCXSAB",
 "EventType": "AssignmentSubmitted",
 "EventTimestamp": "2020-12-17T15:44:27Z",
 "HITId": "3M67TQBQRN84TOJ45IGSP2QRI8M9AN",
 "AssignmentId": "3KOPY89HMFMV1ZVM8HXXF8MB58D3J8",
 "WorkerId": "A46VTZY1D4EK",
 "HITTypeId": "3RSP1MSSW6ZRFKPC8CTPZS9T4TCUKL"
 }
 ],
 "EventDocId": "b98bec91644c6b89ccbbbcebaf98a4f70afba63b",
 "SourceAccount": "************",
 "CustomerId": "A36HH7QA9D2EVK",
 "EventDocVersion": "2014-08-15"
 }
 */
data class MTurkEventCallback(
    @JsonProperty("Events")
    val events: List<AnswerEvent>,
)

data class AnswerEvent(
    @JsonProperty("Answer")
    val answer: String? = null,
    @JsonProperty("HITGroupId")
    val hitGroupId: String,
    @JsonProperty("EventTimestamp")
    val eventTimestamp: Date,
    @JsonProperty("HITId")
    val hitId: String,
    @JsonProperty("AssignmentId")
    val assignmentId: String,
    @JsonProperty("WorkerId")
    val workerId: String,
    @JsonProperty("HITTypeId")
    val hitTypeId: String,
)
