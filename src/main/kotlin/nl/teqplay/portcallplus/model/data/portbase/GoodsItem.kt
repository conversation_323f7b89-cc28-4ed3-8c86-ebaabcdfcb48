package nl.teqplay.portcallplus.model.data.portbase

data class GoodsItem(
    val itemNumber: Int?,
    val description: String?,
    val grossWeight: Double?,
    val numberOfOuterPackages: Int?,
    val outerPackageType: CargoPack?,
    val netWeight: Double?,
    val numberOfInnerPackages: Int?,
    val innerPackageType: CargoPack?,
    val classification: GoodsClassification?,
    val dangerInformation: DangerInformation?,
    val marksAndNumbers: List<String>?,
    val minimumTemperature: Double?,
    val maximumTemperature: Double?,
    val placements: List<GoodPlacement>,
    val producedDocuments: List<GoodsDocument>,
)
