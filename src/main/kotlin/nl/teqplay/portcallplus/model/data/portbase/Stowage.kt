package nl.teqplay.portcallplus.model.data.portbase

data class Stowage(
    val goodId: String?,
    val goodIdIsMissing: String?,
    val portOfDischarge: Port?,
    val portOfLoading: Port?,
    val stowageNumber: String?,
    val weight: String?,
    val type: String?,
    val emptied: Boolean?,
    val amount: Int?,
    val innerPackageType: PackageType?,
    val netExplosiveMass: Double?,
    val netWeight: Double?,
    val numberOfInnerPackages: Int?,
    val numberOfOuterPackages: Int?,
    val outerPackageType: PackageType?,
    val position: String?,
    val transportInLimitedQuantity: Boolean?,
)
