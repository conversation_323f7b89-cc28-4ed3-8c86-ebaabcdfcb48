package nl.teqplay.portcallplus.model.data.portbase

data class Good(
    val goodData: GoodData?,
    val id: String?,
    val remarks: String?,
    val type: String?,
    val criticalTemperature: Double,
    val flashPoint: Double?,
    val heatingOrder: Double?,
    val meltingPoint: Double,
    val viscosity: Double,
    val radioactive: Boolean?,
    val radioactivity: Radioactivity?,
    val segregationInformation: String?,
)
