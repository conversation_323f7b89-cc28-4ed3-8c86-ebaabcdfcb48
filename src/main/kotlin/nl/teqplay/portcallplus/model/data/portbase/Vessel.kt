package nl.teqplay.portcallplus.model.data.portbase

data class Vessel(
    val imoCode: String,
    val name: String?,
    val radioCallSign: String?,
    val motUnCode: String?,
    val motName: String?,
    val summerDeadWeight: Int?,
    val maxWidth: Int?,
    val flagCode: String?,
    val flagCountryUnCode: String?,
    val netTonnage: Int?,
    val grossTonnage: Int?,
    val registrationPlaceUnloCode: String?,
    val registrationPlaceName: String?,
    val registrationDate: String?,
    val mmsiNumber: String?,
    val fullLength: Int?,
    val emailAddress: String?,
    val statCode5: String?,
)
