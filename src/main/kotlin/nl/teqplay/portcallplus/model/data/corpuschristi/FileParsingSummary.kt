package nl.teqplay.portcallplus.model.data.corpuschristi

import com.fasterxml.jackson.annotation.JsonIgnore
import nl.teqplay.portcallplus.api.model.DataModel
import org.bson.codecs.pojo.annotations.BsonId
import java.time.Instant

data class FileParsingSummary(
    @BsonId
    val fileKey: String,
    val timestamp: Instant,
    val invalidObjectsCount: Int,
    val validMovementsCount: Int,
    val message: String = buildMessage(fileKey, invalidObjectsCount, validMovementsCount),
) : DataModel() {
    @JsonIgnore
    override val _id: String = fileKey

    val totalObjectsCount = invalidObjectsCount + validMovementsCount

    companion object {
        private fun buildMessage(
            fileKey: String,
            invalidObjectsCount: Int,
            validMovementsCount: Int,
        ): String {
            val totalObjectsCount = invalidObjectsCount + validMovementsCount
            return when {
                totalObjectsCount == 0 -> "No movements recognized or received. Please, check the file $fileKey!"
                validMovementsCount == 0 -> "No movements processed out of $totalObjectsCount. Please, check the file $fileKey!"
                validMovementsCount != totalObjectsCount -> "Only $validMovementsCount out of $totalObjectsCount movements processed. Please, check the file $fileKey!"
                else -> "File $fileKey processed all movements ($validMovementsCount) correctly!"
            }
        }
    }
}
