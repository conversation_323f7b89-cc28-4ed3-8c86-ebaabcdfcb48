package nl.teqplay.portcallplus.model.data.corpuschristi

data class RawMovement(
    val dataExportTime: String?,
    val movementId: String?,
    val visitId: String?,
    val visitNumber: String?,
    val jobType: String?,
    val movement_status_type_id: String?,
    val jobStatus: String?,
    val isCanceledMovement: String?,
    val vesselIMO: String?,
    val vesselName: String?,
    val scheduledTime: String?,
    val underwayTime: String?,
    val offTime: String?,
    val foreDraft: String?,
    val aftDraft: String?,
    val from_stop_location_id: String?,
    val from_stop_location_code: String?,
    val from_stop_location: String?,
    val to_stop_location_id: String?,
    val to_stop_location_code: String?,
    val to_stop_location: String?,
    val movement_agency: String?,
    val movement_agency_code: String?,
    val lineHandler: String?,
    val tugName: String?,
    val pilotNo: String?,
    val cargo_function: String?,
    val product_code: String?,
)
