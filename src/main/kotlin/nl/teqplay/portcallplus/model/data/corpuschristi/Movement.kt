package nl.teqplay.portcallplus.model.data.corpuschristi

import nl.teqplay.portcallplus.api.model.ServiceModel
import java.util.Date
import java.util.UUID

data class Movement(
    val _id: String = UUID.randomUUID().toString(),
    val dataExportTime: Date,
    val movementId: String?, // ToDo: remove nullable when confirmed
    /** [visitId] is unique for the portcall and different from [visitNumber]. I'll serve as alias. */
    val visitId: String?, // ToDo: remove nullable when confirmed
    /** [visitNumber] is unique for the portcall and different from [visitId]. I'll serve as alias. */
    val visitNumber: String,
    // ToDo: When the model consolidates, transform the [Movement.jobType] to an enum class
    // jobStatus values seen so far are: [ Arrival, Shift, Departure ]
    val jobType: String?, // ToDo: remove nullable when confirmed
    val movement_status_type_id: String?, // ToDo: remove nullable when confirmed
    // ToDo: When the model consolidates, transform the [Movement.jobStatus] to an enum class
    // Values seen so far: [ CNF, SUB, SCH, RDY, COM, CAN ]
    val jobStatus: String?, // ToDo: remove nullable when confirmed
    // UPPERCASE boolean value
    val isCanceledMovement: String?, // ToDo: remove nullable when confirmed
    val vesselIMO: String,
    val vesselName: String?, // ToDo: remove nullable when confirmed
    val scheduledTime: Date?, // ToDo: remove nullable when confirmed
    val underwayTime: Date?,
    val offTime: Date?,
    // It seems a Double
    val foreDraft: String?,
    // It seems a Double
    val aftDraft: String?,
    // ToDo: make sure POMA includes the locations ids,
    // Numeric id of the location
    val from_stop_location_id: String?, // ToDo: remove nullable when confirmed
    // Alphanumeric code of the location
    val from_stop_location_code: String?, // ToDo: remove nullable when confirmed
    // Origin location name
    val from_stop_location: String,
    // Numeric id of the location
    val to_stop_location_id: String?, // ToDo: remove nullable when confirmed
    // Alphanumeric code of the location
    val to_stop_location_code: String?, // ToDo: remove nullable when confirmed
    // Destination location name
    val to_stop_location: String,
    // Agency name
    val movement_agency: String,
    // Agency name code
    // ToDo, make a mapping if needed.
    val movement_agency_code: String?, // ToDo: remove nullable when confirmed
    val lineHandler: String?,
    // Actually not Tug Name, but Towing Company, but the field comes with that field name, so keeping it HERE (not in the portcall model) to avoid mistakes.
    val tugName: String?,
    // Integer or Double nullable number
    val pilotNo: String?,
    /** ToDo: When the model consolidates, transform the [Movement.cargo_function] to an enum class */
    val cargo_function: String?, // ToDo: remove nullable when confirmed
    /** ToDo: When the model consolidates, transform the [Movement.product_code] to an enum class and make a product map */
    val product_code: String?, // ToDo: remove nullable when confirmed
) : ServiceModel
