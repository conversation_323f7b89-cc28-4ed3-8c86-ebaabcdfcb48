package nl.teqplay.portcallplus.model.data.portbase

data class Container(
    val number: String?,
    val portOfLoading: Port?,
    val portOfDischarge: Port?,
    val actualDeparture: String?,
    val terminal: Terminal?,
    val type: String?,
    val sizeType: SizeType?,
    val containerOperator: ContainerOperator?,
    val tareWeight: Int?,
    val bookingReference: String?,
    val shipperSealNumber: String?,
    val carrierSealNumber: String?,
    val customsSealNumber: String?,
    val shippersOwned: Boolean?,
    val empty: Boolean?,
    val temperature: Int?,
    val overlength: Int?,
    val overheight: Int?,
    val overwidth: Int?,
    val oversizeRemarks: String?,
    val onCarriageDetails: OnCarriageDetails?,
    val initialDemurrage: String?,
    val redeliveryAddress: String?,
)
