package nl.teqplay.portcallplus.model.data.amqp

data class PortEvent(
    // The platform event class
    val _class: String,
    // The type of event For example: area.port.NLRTM.start
    val type: String,
    // The timestamp of the event
    val datetime: Long,
    // The title of the event
    val title: String,
    // The description of the event
    val description: String,
    // The ShipInfo details
    val summary: String,
    // Is it planned
    val planning: Boolean,
    // The event related to this.
    val relatedEvent: String?,
    // The shipMmsi it is about
    val shipMmsi: String,
    // the location is excluded as it creates unnecessary overhead
    val location: Any,
    // The ship name it is about
    val shipName: String?,
    // The destination of the ship
    val destination: String?,
    // The draught of the ship
    val maxDraught: Double,
    // The type of event For example AREA
    val eventType: String,
    // The category of the event
    val category: String,
    // The id of this event
    val _id: String,
)
