package nl.teqplay.portcallplus.model.data.portbase

data class PortOfCall(
    val port: Port,
    val portAuthority: VisitShipOrganisation?,
    val customsOffice: CustomsOffice?,
    val ataReported: Boolean?,
    val atdReported: Boolean?,
    val atdPortReported: Boolean?,
    val paDeclarationRequired: Boolean?,
    val swDeclarationRequired: Boolean?,
    val wasteEnabled: Boolean?,
    val dangerousGoodsEnabled: Boolean?,
    val harbourDuesEnabled: Boolean?,
    val orderNauticalServicesEnabled: Boolean?,
    val enableNotificationsToPa: Boolean?,
    val authoriseOrganisationsDisabled: Boolean?,
    val tugBoatsRequired: Boolean?,
    val nauticalServicesApplicable: Boolean?,
    val notRequiringTugBoats: Boolean?,
    val paxDisabled: Boolean?,
    val msvDisabled: Boolean?,
)
