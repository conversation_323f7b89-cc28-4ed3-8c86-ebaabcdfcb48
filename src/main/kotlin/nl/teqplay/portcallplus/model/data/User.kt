package nl.teqplay.portcallplus.model.data

import com.fasterxml.jackson.annotation.JsonIgnore
import nl.teqplay.portcallplus.api.model.DataModel
import nl.teqplay.skeleton.auth.credentials.User
import nl.teqplay.skeleton.common.exception.InternalErrorException

data class User(
    override val username: String,
    override val roles: Set<String> = setOf("ANY"),
) : User, DataModel() {
    @JsonIgnore
    override val _id = username

    // Roles consistency
    init {
        if (roles.size != 1) {
            throw InternalErrorException("A user can have ONLY one role.")
        }
    }
}
