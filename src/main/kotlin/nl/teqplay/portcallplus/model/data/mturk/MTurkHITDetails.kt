package nl.teqplay.portcallplus.model.data.mturk

import com.fasterxml.jackson.annotation.JsonIgnore
import nl.teqplay.portcallplus.api.model.DataModel
import nl.teqplay.portcallplus.model.service.MTurkHIT
import org.bson.codecs.pojo.annotations.BsonId
import software.amazon.awssdk.services.mturk.model.AssignmentStatus
import software.amazon.awssdk.services.mturk.model.HIT
import software.amazon.awssdk.services.mturk.model.HITReviewStatus
import software.amazon.awssdk.services.mturk.model.HITStatus
import java.time.Instant

/**
 * Captures all the details related to a MTurk HIT (Human Intelligence Task)
 * A HIT picked up by a "worker" in MTurk is termed as an assignment. (So 1:N relation)
 */
data class MTurkHITDetails(
    // relevant request details
    val request: MTurkHIT,
    // relevant HIT details
    @BsonId
    val hitId: String,
    val hitTypeId: String,
    val hitReviewStatus: HITReviewStatus = HITReviewStatus.UNKNOWN_TO_SDK_VERSION,
    val hitStatus: HITStatus = HITStatus.UNKNOWN_TO_SDK_VERSION,
    val expired: Boolean = false,
    val disappearedInAmazon: Boolean? = null,
    // status of the assignment linked
    val assignments: List<MTurkHITResponse> = listOf(),
    val overallAssignmentStatus: AssignmentStatus = AssignmentStatus.UNKNOWN_TO_SDK_VERSION,
    val approvedAnswer: String? = null,
) : DataModel() {
    @JsonIgnore
    override val _id: String = hitId

    constructor(
        hitRequest: MTurkHIT,
        hitDetails: HIT,
        assignments: List<MTurkHITResponse> = listOf(),
        overallAssignmentStatus: AssignmentStatus = AssignmentStatus.UNKNOWN_TO_SDK_VERSION,
    ) : this(
        hitRequest, hitDetails.hitId(), hitDetails.hitTypeId(), hitDetails.hitReviewStatus(), hitDetails.hitStatus(),
        hitDetails.expiration().isBefore(Instant.now()), false, assignments, overallAssignmentStatus,
    )

    fun updateWith(hitDetails: HIT) =
        this.copy(
            hitId = hitDetails.hitId(),
            hitTypeId = hitDetails.hitTypeId(),
            hitReviewStatus = hitDetails.hitReviewStatus(),
            hitStatus = hitDetails.hitStatus(),
            expired = hitDetails.expiration().isBefore(Instant.now()),
            disappearedInAmazon = false,
        )
}
