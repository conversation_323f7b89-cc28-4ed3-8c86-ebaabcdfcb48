package nl.teqplay.portcallplus.model.data.mturk

import nl.teqplay.portcallplus.service.external.MTurkService.Companion.parseAnswer
import nl.teqplay.portcallplus.utils.removeSpecialChars
import software.amazon.awssdk.services.mturk.model.Assignment
import software.amazon.awssdk.services.mturk.model.AssignmentStatus
import java.util.Date

/**
 * This class encapsulates any response to a HIT
 */
data class MTurkHITResponse(
    // uniqueId assigned per assigned related to a HIT
    val assignmentId: String,
    // uniqueId of the worker who picked up this assignment
    val workerId: String,
    // status of the assignment
    val status: AssignmentStatus,
    // answer given by the worker
    val answer: String? = "",
    // time at which the assignment was submitted
    val submitTime: Date? = null,
    // time at which the assignment submission was approved
    val approvalTime: Date? = null,
    // flag indicating if the assignment is accepted or rejected
    val isRejected: Boolean = false,
    // accepted or rejection reason
    val feedback: String? = "",
    // Answer parsed from xml [answer]
    val parsedAnswer: String? = answer?.let { parseAnswer(answer) },
    // key-ed answer
    val formattedAnswer: String? = parsedAnswer?.let { removeSpecialChars(parsedAnswer) },
) {
    constructor(assignment: Assignment) : this(
        assignment.assignmentId(),
        assignment.workerId(),
        assignment.assignmentStatus(),
        assignment.answer(),
        assignment.submitTime()?.let { Date(it.toEpochMilli()) },
        assignment.approvalTime()?.let { Date(it.toEpochMilli()) },
        assignment.rejectionTime() != null,
        assignment.requesterFeedback(),
    )
}
