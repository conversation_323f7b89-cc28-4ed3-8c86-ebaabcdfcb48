package nl.teqplay.portcallplus.model.data.portbase

data class EntryDependency(
    val vesselName: String?,
    val vesselImoCode: String?,
    val crn: String?,
    val berthVisitId: String?,
    val berthName: String?,
    val berthCode: String?,
    val autoOrder: Boolean?,
    val email: String?,
    val phoneNumber: String?,
    val estimatedTimeBerth: String?,
    val actualTimeBerth: String?,
    val orderStatus: String?,
    val movementOnHold: Boolean?,
    val dependencyType: String?,
)
