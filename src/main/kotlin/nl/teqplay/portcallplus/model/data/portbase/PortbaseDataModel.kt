package nl.teqplay.portcallplus.model.data.portbase

import com.fasterxml.jackson.annotation.JsonIgnore
import nl.teqplay.portcallplus.api.model.DataModel
import nl.teqplay.portcallplus.model.service.PortBasePortCall
import org.bson.codecs.pojo.annotations.BsonId

data class PortbaseDataModel(
    @BsonId
    val portcallId: String,
    val portBasePortCall: PortBasePortCall,
) : DataModel() {
    @JsonIgnore
    override val _id = portcallId
}
