package nl.teqplay.portcallplus.model.data.portbase

data class Radioactivity(
    val radionuclide: Radionuclide?,
    val packageCategory: String?,
    val identification: String?,
    /**
     * Previously it was Int? but PortBase API returns a Long value to avoid overflow issues
     */
    val level: Long?,
    val transportIndex: String?,
    val criticalSafetyIndex: Int?,
    val licenseNumber: String?,
    val remarks: String?,
)
