package nl.teqplay.portcallplus.model.data.portbase

data class HealthDeclaration(
    val vesselMaster: VesselContact?,
    val vesselSurgeon: VesselContact?,
    val additionalContact: VesselContact?,
    val arrivingFrom: Port?,
    val sailingTo: Port?,
    val portsFromCommencementOfVoyage: List<PortOfVoyage>,
    val healthQuestions: HealthQuestions?,
    val generalQuestions: GeneralQuestions?,
    val numberOfCrew: Int?,
    val numberOfPassengers: Int?,
    val recoveredIllOrDeadPeople: List<RecoveredIllOrDeadPeople>,
    val peopleJoinedVessel: List<PeopleJoinedVessel>,
    val remarks: String?,
)
