package nl.teqplay.portcallplus.model.data.portbase

data class RecoveredIllOrDeadPeople(
    val name: String?,
    val gender: String?,
    val nationality: String?,
    val dateOfBirth: String?,
    val jobTitleOrRank: String?,
    val joinedVesselAtPort: Port?,
    val joinedVesselAtDate: String?,
    val natureOfIllness: String?,
    val dateOnsetOfSymptoms: String?,
    val reportedToMedicalOfficer: Boolean?,
    val disposalOfCase: String?,
    val treatmentGiven: String?,
    val comments: String?,
)
