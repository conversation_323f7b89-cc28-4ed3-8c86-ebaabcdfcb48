package nl.teqplay.portcallplus.model.data.enigma

import com.fasterxml.jackson.annotation.JsonIgnore
import nl.teqplay.portcallplus.api.model.DataModel
import nl.teqplay.portcallplus.api.model.PortcallStatus
import nl.teqplay.portcallplus.model.service.EnigmaTravel
import org.bson.codecs.pojo.annotations.BsonId

data class EnigmaTravels(
    @BsonId
    val type: PortcallStatus,
    val travels: List<EnigmaTravel>? = null,
) : DataModel() {
    @JsonIgnore
    override val _id = type
}
