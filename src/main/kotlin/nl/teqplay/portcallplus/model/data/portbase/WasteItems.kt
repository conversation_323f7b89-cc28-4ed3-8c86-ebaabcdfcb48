package nl.teqplay.portcallplus.model.data.portbase

data class WasteItems(
    val ssn: SSN?,
    val berthIdForPickup: String?,
    val quantityToBeDelivered: Double?,
    val quantityToBeRetained: Double?,
    val maxDedicatedCapacity: Double?,
    val quantityToBeGenerated: Double?,
    val portForRetainedWaste: Port?,
    val type: String?,
    val collector: VisitShipOrganisation?,
    val specification: String?,
    val quantityOfLastDelivery: Int?,
)
