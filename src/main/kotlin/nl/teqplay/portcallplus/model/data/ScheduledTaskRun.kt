package nl.teqplay.portcallplus.model.data

import com.fasterxml.jackson.annotation.JsonIgnore
import nl.teqplay.portcallplus.api.model.DataModel
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import org.bson.codecs.pojo.annotations.BsonId
import org.bson.codecs.pojo.annotations.BsonIgnore
import java.util.Date

data class ScheduledTaskRun(
    /**
     * Corresponding task type
     */
    @BsonId
    val taskType: ScheduledTaskType,
    /**
     * The last time this task was run (either success or not)
     */
    val lastRun: Date,
    val lastFetchIndex: String? = null,
    /**
     * last run message
     */
    val lastRunMessage: String = "",
    /**
     * The amount of time taken by this task to run
     */
    val lastRunDuration: Long = 0,
    /**
     * The last time this task finished successfully
     */
    val lastSuccessRun: Date? = null,
    /**
     * The last time this task failed to run
     */
    val lastFailedRun: Date? = null,
    /**
     * last failure message seen for this task
     */
    val lastFailedRunMessage: String? = null,
    /**
     * Indicates the latest updated portcall created/updated by this [taskType].
     */
    val lastUpdatedPortcallTime: Date? = null,
    /**
     * Description of this task. Used to see any description of the task in the status endpoint.
     * Implemented with a custom getter to ignore db values
     */
    val taskName: String,
    /**
     * Indication is this task is enabled or not. Used to see status of the task in the status endpoint
     * Implemented with a custom getter to ignore db values
     */
    val enabled: Boolean,
) : DataModel() {
    @JsonIgnore
    override val _id = taskType

    /**
     * Flag to indicate if is Running or not. A task might be enabled and force stopped. This value is not stored in
     * the db. But injected via /status call
     */
    @BsonIgnore
    var isRunning: Boolean? = null

    /**
     * Indicates the time in format: HH:mm:ss for the next execution of this [taskType]. This value is not stored in
     * the db. But injected via /status call
     */
    @BsonIgnore
    var timeToNextRun: String? = null
}
