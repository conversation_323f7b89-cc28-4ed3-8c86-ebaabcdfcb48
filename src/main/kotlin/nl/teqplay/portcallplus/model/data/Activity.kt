package nl.teqplay.portcallplus.model.data

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonProperty
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import nl.teqplay.portcallplus.api.model.DataModel
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.utils.fetchTimestamp
import java.util.Date
import java.util.TreeSet
import java.util.UUID
import java.util.concurrent.TimeUnit

/**
 * This class is a model created to save the ships retrieved from a task for every task that ran
 */
data class Activity(
    val imoList: Set<String>,
    val taskName: ScheduledTaskType,
) : DataModel() {
    override val _id = UUID.randomUUID().toString()

    constructor(imoList: Set<String>, taskName: ScheduledTaskType, timestamp: Date) : this(imoList, taskName) {
        super.creationTimestamp = timestamp
    }

    class ActivityData(
        val timestamp: String = "",
        val taskName: ScheduledTaskType = ScheduledTaskType.UNKNOWN,
        @JsonIgnore
        val imos: HashSet<String> = hashSetOf(),
    ) : Comparable<ActivityData> {
        @JsonProperty("count")
        fun getCount() = imos.size

        override fun compareTo(other: ActivityData): Int {
            var compare = timestamp.compareTo(other.timestamp)
            if (compare == 0) {
                compare = taskName.compareTo(other.taskName)
            }
            // if timestamp and taskName matches. Just add all current imos to the new list and replace (compare == 0)
            if (compare == 0) {
                other.imos.addAll(imos)
            }
            return compare
        }

        companion object {
            fun getActivityData(activities: Collection<Activity>): TreeSet<ActivityData> {
                val result = TreeSet<ActivityData>()
                val timestamps = TreeSet<String>()
                val taskNames = hashSetOf<ScheduledTaskType>()

                val deferredActivities = activities.map { activity ->
                    CoroutineScope(Job()).async {
                        val timestamp = fetchTimestamp(activity.creationTimestamp).substring(0, 10)
                        ActivityData(timestamp, activity.taskName, activity.imoList.toHashSet())
                    }
                }
                runBlocking {
                    deferredActivities.forEach {
                        val activityData = it.await()
                        result.add(activityData)
                        timestamps.add(activityData.timestamp)
                        taskNames.add(activityData.taskName)
                    }
                }

                if (result.isNotEmpty()) {
                    // also append data for all tasks counters from the first activity timestamp found (useful for tracking and graph)
                    val firstTimestamp = fetchTimestamp(timestamps.first(), "yyyy-MM-dd")
                    val lastTimestamp = fetchTimestamp(timestamps.last(), "yyyy-MM-dd")
                    taskNames.forEach { taskName ->
                        var count = 0
                        do {
                            val timestampInc = fetchTimestamp(firstTimestamp, count, TimeUnit.DAYS)
                            val timestampIncString = fetchTimestamp(timestampInc, "yyyy-MM-dd")
                            if (result.parallelStream().noneMatch { it.taskName == taskName && it.timestamp == timestampIncString }) {
                                result.add(ActivityData(timestampIncString, taskName, hashSetOf()))
                            }
                            count++
                        } while (timestampInc.before(lastTimestamp))
                    }
                }
                return result
            }
        }
    }
}
