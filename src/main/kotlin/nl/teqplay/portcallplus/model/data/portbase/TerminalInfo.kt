package nl.teqplay.portcallplus.model.data.portbase

data class TerminalInfo(
    val eta: String?,
    val etd: String?,
    val ata: String?,
    val atd: String?,
    val expectedFirstLift: String?,
    val firstLift: String?,
    val expectedLastLift: String?,
    val lastLift: String?,
    val cargoOpeningTime: String?,
    val cargoClosingTime: String?,
    val mooringOrientation: String?,
    val quay: String?,
    val bollardAft: String?,
    val bollardFore: String?,
    val expectedLoad: String?,
    val expectedDischarge: String?,
    val vessel: String?,
    val phase: String?,
    val carrier: String?,
)
