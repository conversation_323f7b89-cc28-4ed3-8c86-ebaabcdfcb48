package nl.teqplay.portcallplus.model.data.mturk

import java.time.Instant

data class HIT(
    val hitId: String?,
    val hitTypeId: String?,
    val hitGroupId: String?,
    val hitLayoutId: String?,
    val creationTime: Instant?,
    val title: String?,
    val description: String?,
    val question: String?,
    val keywords: String?,
    val hitStatus: String?,
    val maxAssignments: Int?,
    val reward: String?,
    val autoApprovalDelayInSeconds: Long,
    val expiration: Instant?,
    val assignmentDurationInSeconds: Long,
    val requesterAnnotation: String?,
    val hitReviewStatus: String?,
    val numberOfAssignmentsPending: Int?,
    val numberOfAssignmentsAvailable: Int?,
    val numberOfAssignmentsCompleted: Int?,
)

data class Assignment(
    val assignmentId: String?,
    val workerId: String?,
    val hitId: String?,
    val assignmentStatus: String?,
    val autoApprovalTime: Instant?,
    val acceptTime: Instant?,
    val submitTime: Instant?,
    val approvalTime: Instant?,
    val rejectionTime: Instant?,
    val deadline: Instant?,
    val answer: String?,
    val requesterFeedback: String?,
)

data class WorkerBlock(
    val workerId: String?,
    val reason: String?,
)

enum class HitStatus {
    ASSIGNABLE,
    UNASSIGNABLE,
    REVIEWABLE,
    REVIEWING,
    DISPOSED,
}
