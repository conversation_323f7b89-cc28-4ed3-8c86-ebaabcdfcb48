package nl.teqplay.portcallplus.model.data.portbase

data class PortVisit(
    val pilotStation: PilotStation?,
    val entryPoint: EntryPoint?,
    val etaPort: String?,
    val portEntry: PortEntry?,
    val firstMovement: FirstMovement?,
    val defectTypes: List<String>?,
    val berthVisits: List<BerthVisits>?,
    val dangerousGoodsLoading: Boolean?,
    val dangerousGoodsDischarge: Boolean?,
    val dangerousGoodsTransit: Boolean?,
    val vesselInspectionRequired: Boolean?,
    val exitPoint: ExitPoint?,
    val etdPort: String?,
    val atdPort: String?,
    val ataPort: String?,
    val passingThroughTugboats: PassingThroughTugboats?,
    val defectTypeRemarks: String?,
    val possibleAnchorage: Boolean?,
    val shipConfiguration: String?,
)
