package nl.teqplay.portcallplus.model.data.portbase

data class ImportDeclaration(
    val bulkDischarges: List<BulkDischarges>?,
    val cargoDeclarant: VisitShipOrganisation?,
    val carrierCustomsId: String?,
    val clearedManifestIds: List<String>?,
    val consignmentDifferences: List<ConsignmentDifferences>?,
    val consignments: List<Consignment>?,
    val containers: List<Container>?,
    val crn: String?,
    val declarations: List<Declaration>?,
    val discharges: List<Discharges>?,
    val incomingMessages: List<Any>?, // ??
    val inspectionUpdates: List<InspectionUpdates>?,
    // receivedMrns is not mentioned anywhere but it is in the live response samples thus we keep it here
    val receivedMrns: MovementReferenceNumbers?,
    // Not mentioned in the API documents but seen in live response
    val refusedDischarges: List<Any>?,
    val timestamp: String?,
    val visitCreated: String?,
)
