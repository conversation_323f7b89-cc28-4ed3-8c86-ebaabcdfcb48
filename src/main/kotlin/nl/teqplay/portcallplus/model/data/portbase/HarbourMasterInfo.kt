package nl.teqplay.portcallplus.model.data.portbase

data class HarbourMasterInfo(
    val ata: String?,
    val atd: String?,
    val orderStatus: String?,
    val rejectionReason: String?,
    val eta: String?,
    val etd: String?,
    val pilotOnBoard: String?,
    val remarks: HarbourMasterRemarks?,
    val movementOnHold: Boolean?,
    val tidalWindowStatus: TidalWindowStatus?,
    val pilotFromDistance: String?,
    val vesselDraught: Double?,
)
