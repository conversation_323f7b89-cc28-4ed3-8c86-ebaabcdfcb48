package nl.teqplay.portcallplus.model.data

import com.fasterxml.jackson.annotation.JsonIgnore
import nl.teqplay.portcallplus.api.model.DataModel
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import org.bson.codecs.pojo.annotations.BsonId
import java.util.Date

/**
 * A simple bean to persist the number of service calls done at a given timestamp
 */
data class ServiceFetchCounter(
    /**
     * Timestamp at which this counter was initiated
     */
    @BsonId
    val timestamp: Date,
    /**
     * Task to describe this service
     */
    val taskType: ScheduledTaskType = ScheduledTaskType.UNKNOWN,
) : DataModel() {
    /**
     * List of all new portcallIds created after a a service request was made
     */
    val create = mutableSetOf<ServiceFetchDetail>()

    /**
     * List of all portcallIds whose eta has been updated after a service request was made
     */
    val etaUpdate = mutableSetOf<ServiceFetchDetail>()

    /**
     * List of all portcallIds whose berth has been updated after a service request was made
     */
    val berthUpdate = mutableSetOf<ServiceFetchDetail>()

    /**
     * List of all portcallIds whose etd has been updated after a service request was made
     */
    val etdUpdate = mutableSetOf<ServiceFetchDetail>()

    /**
     * List of all portcallIds whose [Portcall.vesselAgent] has been updated after a service request was made
     */
    val agentUpdate = mutableSetOf<String>()

    /**
     * List of all portcallIds which have a [Portcall.portAtdTime] time set
     */
    val finish = mutableSetOf<ServiceFetchDetail>()

    /**
     * List of all portcallIds for which no update was done (as all data matched) after a service request was made
     */
    val noUpdates = mutableSetOf<String>()

    /**
     * List of all imoNumbers for which no relevant information was fetched. But no error was seen.
     */
    val emptyResult = mutableSetOf<String>()

    /**
     * List of all Imo numbers for which a request was made but an error was seen
     */
    val failedImoNumbers = mutableSetOf<String>()

    /**
     * Returns the number of updates that are done to different portcalls. Note that this can include updates on the
     * same portcall, as well.
     */
    @JsonIgnore
    fun getPortcallUpdateCount() = create.size + etaUpdate.size + berthUpdate.size + etdUpdate.size + finish.size + agentUpdate.size

    /**
     * Returns the number of update for which a portcall was not updated
     */
    @JsonIgnore
    fun getNoPortcallUpdateCount() = noUpdates.size + emptyResult.size

    /**
     * Returns the total elements in [create], [etaUpdate], [berthUpdate], [etdUpdate], [noUpdates], [agentUpdate],
     * [finish], [noUpdates], [emptyResult] and [failedImoNumbers]
     */
    @JsonIgnore
    fun getSize() = getPortcallUpdateCount() + getNoPortcallUpdateCount() + failedImoNumbers.size

    @JsonIgnore
    override val _id = timestamp

    /**
     * Returns the portcallIds or imoNumbers corresponding to the given [type] for which an update was performed
     */
    fun getEventUpdateType(type: UpdateType): Set<ServiceFetchDetail> {
        return when (type) {
            UpdateType.CREATE -> create
            UpdateType.ETA_UPDATE -> etaUpdate
            UpdateType.BERTH_UPDATE -> berthUpdate
            UpdateType.ETD_UPDATE -> etdUpdate
            UpdateType.FINISH -> finish
            else -> mutableSetOf()
        }
    }

    /**
     * Returns the portcallIds or imoNumbers corresponding to the given non event update [type]
     */
    fun getNoEventUpdateType(type: UpdateType): Set<String> {
        return when (type) {
            UpdateType.AGENT_UPDATE -> agentUpdate
            UpdateType.NO_UPDATE -> noUpdates
            UpdateType.EMPTY_RESULT -> emptyResult
            UpdateType.FAILED_UPDATE -> failedImoNumbers
            else -> mutableSetOf()
        }
    }
}

/**
 * Just a simple class to encapsulate information about the portcallId and the corresponding update in the eventTime
 */
data class ServiceFetchDetail(
    /**
     * Unique reference to this detail. Either a portcallId or an imo
     */
    val reference: String,
    /**
     * The actual update in the corresponding eventTime for the reference
     */
    val updatedEventType: Date?,
)

/**
 * Enum type to describe the type of portcall update
 */
enum class UpdateType {
    CREATE,
    ETA_UPDATE,
    BERTH_UPDATE,
    ETD_UPDATE,
    AGENT_UPDATE,
    FINISH,
    NO_UPDATE,
    EMPTY_RESULT,
    FAILED_UPDATE,
}
