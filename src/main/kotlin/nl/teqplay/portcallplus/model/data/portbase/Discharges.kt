package nl.teqplay.portcallplus.model.data.portbase

data class Discharges(
    val number: String?,
    val portOfLoading: Port?,
    val terminal: Terminal?,
    val actualTimeOfHandling: String?,
    val stowageLocation: String?,
    val grossWeight: Int?,
    val transportStatus: String?,
    val type: String?,
    val sizeType: SizeType?,
    val containerOperator: ContainerOperator?,
    val cargoAgentShortName: String?,
    val tareWeight: Int?,
    val bookingReference: String?,
    val shipperSealNumber: String?,
    val carrierSealNumber: String?,
    val customsSealNumber: String?,
    val shippersOwned: Boolean?,
    val empty: Boolean?,
    val temperature: Int?,
    val overlength: Int?,
    val overheight: Int?,
    val overwidth: Int?,
    val oversizeRemarks: String?,
)
