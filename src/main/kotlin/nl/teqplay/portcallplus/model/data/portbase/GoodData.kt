package nl.teqplay.portcallplus.model.data.portbase

data class GoodData(
    val emsFireCode: String?,
    val emsSpillageCode: String?,
    val gdsCode: String?,
    val goodCode: String?,
    val hazardClass: String?,
    val name: String?,
    val packingGroup: String?,
    val properties: String?,
    val segregationGroup: String?,
    val subsidiaryRisk: String?,
    val unCode: String?,
    val stowageType: String?,
    val type: String?,
)
