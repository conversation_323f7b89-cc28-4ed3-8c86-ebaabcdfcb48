package nl.teqplay.portcallplus.model.httpResponse

import com.fasterxml.jackson.annotation.JsonUnwrapped
import nl.teqplay.portcallplus.model.data.UpdateType
import java.util.Date
import java.util.TreeSet
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicInteger

/**
 * Simple wrapper class around [nl.teqplay.portcallplus.model.NxtPortFetchCounter] to collect and show the number of
 * updates done to the portcall document
 */
class ServiceFetchCounterResult {
    /**
     * The total number of calls done to the corresponding service
     */
    var total = AtomicInteger(0)

    @JsonUnwrapped
    var counter = OverallFetchCounter()

    /**
     * The total number of calls done to the corresponding service per portcallID
     */
    val totalPerPortcallId = ConcurrentHashMap<String, PortcallCounter>()

    /**
     * Appends the given [details] to the [totalPerPortcallId] corresponding to the given [portcallId]
     */
    fun addPortcallDetails(
        portcallId: String,
        updateType: UpdateType,
        details: PortcallUpdateDetails,
    ) {
        totalPerPortcallId.putIfAbsent(portcallId, PortcallCounter())
        when (updateType) {
            UpdateType.CREATE -> totalPerPortcallId[portcallId]!!.create = details
            UpdateType.ETA_UPDATE -> totalPerPortcallId[portcallId]!!.etaUpdate.add(details)
            UpdateType.BERTH_UPDATE -> totalPerPortcallId[portcallId]!!.berthUpdate.add(details)
            UpdateType.ETD_UPDATE -> totalPerPortcallId[portcallId]!!.etdUpdate.add(details)
            UpdateType.AGENT_UPDATE -> totalPerPortcallId[portcallId]!!.agentUpdate.add(details)
            UpdateType.FINISH -> totalPerPortcallId[portcallId]!!.finish.add(details)
            UpdateType.NO_UPDATE -> totalPerPortcallId[portcallId]!!.noUpdates.add(details)
            UpdateType.FAILED_UPDATE -> totalPerPortcallId[portcallId]!!.failedUpdates.add(details)
            UpdateType.EMPTY_RESULT -> totalPerPortcallId[portcallId]!!.noStays.add(details)
        }
    }

    class OverallFetchCounter {
        val createCount = AtomicInteger(0)
        val etaUpdateCount = AtomicInteger(0)
        val berthUpdateCount = AtomicInteger(0)
        val etdUpdateCount = AtomicInteger(0)
        val agentUpdateCount = AtomicInteger(0)
        val finishCount = AtomicInteger(0)
        val noUpdatesCount = AtomicInteger(0)
        val noStaysCount = AtomicInteger(0)
        val failedCount = AtomicInteger(0)
    }

    class PortcallCounter {
        var create: PortcallUpdateDetails? = null
            internal set(value) {
                field = value
            }
        val etaUpdate = TreeSet<PortcallUpdateDetails>()
        val berthUpdate = TreeSet<PortcallUpdateDetails>()
        val etdUpdate = TreeSet<PortcallUpdateDetails>()
        val agentUpdate = TreeSet<PortcallUpdateDetails>()
        val finish = TreeSet<PortcallUpdateDetails>()
        val noUpdates = TreeSet<PortcallUpdateDetails>()
        val noStays = TreeSet<PortcallUpdateDetails>()
        val failedUpdates = TreeSet<PortcallUpdateDetails>()
    }

    class PortcallUpdateDetails(
        /**
         * Actual time of running the task
         */
        val time: Date,
        /**
         * Actual task for this count
         */
        val task: String,
        /**
         * Actual update to the eventTime that this task updated
         */
        val eventTime: Date? = null,
    ) : Comparable<PortcallUpdateDetails> {
        override fun compareTo(other: PortcallUpdateDetails) = this.time.compareTo(other.time)
    }
}
