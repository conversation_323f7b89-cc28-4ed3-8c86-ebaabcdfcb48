package nl.teqplay.portcallplus.model.httpResponse

import java.util.concurrent.atomic.AtomicInteger

class ServiceOverallCounter {
    /**
     * The total number of calls succeeded to the corresponding service
     */
    val portcallUpdates = AtomicInteger(0)

    /**
     * The total number of calls that succeeded but did not result in an update
     */
    val noPortcallUpdates = AtomicInteger(0)

    /**
     * The total number of calls failed to the corresponding service
     */
    val failure = AtomicInteger(0)

    /**
     * The total number of calls done to the corresponding service
     */
    val total = AtomicInteger(0)
        get() {
            field.set(portcallUpdates.get())
            field.getAndAdd(noPortcallUpdates.get())
            field.getAndAdd(failure.get())
            return field
        }
}
