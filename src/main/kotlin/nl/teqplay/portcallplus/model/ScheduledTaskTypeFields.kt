package nl.teqplay.portcallplus.model

import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.CORPUS_CHRISTI
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.DIGITRAFFIC
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.ENIGMA_SCRAPER_INCOMING
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.ENIGMA_SCRAPER_OUTGOING
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.HARBORLIGHTS
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.IAMCONNECTED_PORTBASE
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.LIS_SCRAPPER
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.MARITEAM_PORTBASE
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.MTURK
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.NM10TO110_8HOURS
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.NM110_24HOURS
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.NM20_12HOURS
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.NM20_2HOURS_MOVING
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.NM20_4HOURS_NO_BERTH
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.NM20_8HOURS
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.NM240_2HOURS
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.NXTPORT_V2
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.OUDKERK_PORTBASE
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.PORTCALL_GENERATOR
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.PORTCALL_SYNC
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.S5_PORTBASE
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.SG_MDH_DUE_TO_ARRIVE
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.SG_MDH_DUE_TO_DEPART
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.SG_MDH_VISIT_ARRIVAL_DECLARATION
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.SIMPLY5_NOMINATION
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.SMARTFLEET
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.TMA_NOMINATION
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.UNKNOWN
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.VOPAK_NOMINATION
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.VOPAK_PORTBASE
import java.time.Duration

data class ScheduledTaskTypeFields(
    /**
     * Enabled flag
     */
    val enabled: Boolean = false,
    /**
     * The rate at which this task is configured to be scheduled. Defaulted to 1 hour
     */
    val cycleDuration: Duration = Duration.ofHours(1),
    /**
     * The amount of time the last updated portcall is allowed to be in the past on top of 1x the cycle duration
     */
    val lastPortcallUpdateDuration: Duration,
    /**
     * The amount of time the task is allowed to be in a failed state
     */
    val maxDownTime: Duration,
    /**
     * The very type itself, in case we need it (currently for getting the TaskNameFull.
     */
    val type: ScheduledTaskType,
) {
    val taskFullName = when (type) {
        PORTCALL_SYNC -> "$cycleDuration Portcall Sync Task"
        LIS_SCRAPPER -> "$cycleDuration LIS Scraper Task"
        HARBORLIGHTS -> "$cycleDuration HarbourLight Scraper Task"
        DIGITRAFFIC -> "$cycleDuration Finnish Digitraffic Task"
        NXTPORT_V2 -> "$cycleDuration NxtPort Incremental Fetch Task"

        ENIGMA_SCRAPER_INCOMING -> "$cycleDuration Enigma Incoming Scraper Task"
        ENIGMA_SCRAPER_OUTGOING -> "$cycleDuration Enigma Outgoing Scraper Task"

        SG_MDH_VISIT_ARRIVAL_DECLARATION -> "$cycleDuration Singapore MDH Arrival Declaration Task"
        SG_MDH_DUE_TO_ARRIVE -> "$cycleDuration Singapore MDH Due to Arrive Task"
        SG_MDH_DUE_TO_DEPART -> "$cycleDuration Singapore MDH Due to Depart Task"

        VOPAK_PORTBASE -> "Vopak PortBase Task started once, running indefinitely"
        S5_PORTBASE -> "S5 PortBase Task started once, running indefinitely"
        OUDKERK_PORTBASE -> "Oudkerk PortBase Task started once, running indefinitely"
        IAMCONNECTED_PORTBASE -> "IAmConnected PortBase Task started once, running indefinitely"
        MARITEAM_PORTBASE -> "Mariteam PortBase Task started once, running indefinitely"
        VOPAK_NOMINATION -> "$cycleDuration VopakNomination Task"
        CORPUS_CHRISTI -> "$cycleDuration CorpusChristi Port Sync Task"

        UNKNOWN, PORTCALL_GENERATOR, SIMPLY5_NOMINATION, MTURK, SMARTFLEET, TMA_NOMINATION,
        NM10TO110_8HOURS, NM240_2HOURS, NM110_24HOURS, NM20_12HOURS, NM20_2HOURS_MOVING,
        NM20_4HOURS_NO_BERTH, NM20_8HOURS,
        -> type.name
    }
}
