package nl.teqplay.portcallplus.model

import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.properties.CorpusChristiProperties
import nl.teqplay.portcallplus.properties.DigitrafficProperties
import nl.teqplay.portcallplus.properties.EnigmaProperties
import nl.teqplay.portcallplus.properties.LisProperties
import nl.teqplay.portcallplus.properties.MTurkProperties
import nl.teqplay.portcallplus.properties.NxtPortV2Properties
import nl.teqplay.portcallplus.properties.PortBaseProperties
import nl.teqplay.portcallplus.properties.PortcallSyncProperties
import nl.teqplay.portcallplus.properties.SgmdhProperties
import nl.teqplay.portcallplus.properties.SmartFleetProperties
import nl.teqplay.portcallplus.properties.StatusProperties
import nl.teqplay.portcallplus.properties.VopakNominationProperties
import org.springframework.stereotype.Component
import java.time.Duration

@Component
class ScheduledTaskTypeFieldsProxy(
    private val digitrafficProperties: DigitrafficProperties,
    private val enigmaProperties: EnigmaProperties,
    private val nxtPortV2Properties: NxtPortV2Properties,
    private val lisProperties: LisProperties,
    private val portBaseProperties: PortBaseProperties,
    private val portcallSyncProperties: PortcallSyncProperties,
    private val sgmdhProperties: SgmdhProperties,
    private val statusProperties: StatusProperties,
    private val corpusChristiProperties: CorpusChristiProperties,
    private val vopakNominationProperties: VopakNominationProperties,
    private val smartFleetProperties: SmartFleetProperties,
    private val mTurkProperties: MTurkProperties,
) {
    fun getFields(type: ScheduledTaskType): ScheduledTaskTypeFields {
        val default = ScheduledTaskTypeFields(
            type = type,
            lastPortcallUpdateDuration = statusProperties.lastPortcallUpdate,
            maxDownTime = statusProperties.maxDownTime,
        )
        return when (type) {
            ScheduledTaskType.LIS_SCRAPPER ->
                default.copy(cycleDuration = lisProperties.cycleDuration, enabled = lisProperties.enable)
            ScheduledTaskType.ENIGMA_SCRAPER_INCOMING,
            ScheduledTaskType.ENIGMA_SCRAPER_OUTGOING,
            ->
                default.copy(cycleDuration = enigmaProperties.cycleDuration, enabled = enigmaProperties.enable)
            ScheduledTaskType.NXTPORT_V2 ->
                default.copy(cycleDuration = nxtPortV2Properties.cycleDuration, enabled = nxtPortV2Properties.enable)
            ScheduledTaskType.SG_MDH_VISIT_ARRIVAL_DECLARATION,
            ScheduledTaskType.SG_MDH_DUE_TO_ARRIVE,
            ScheduledTaskType.SG_MDH_DUE_TO_DEPART,
            ->
                default.copy(
                    cycleDuration = sgmdhProperties.cycleDuration,
                    lastPortcallUpdateDuration = Duration.ofDays(1),
                    enabled = sgmdhProperties.enable,
                )
            ScheduledTaskType.PORTCALL_SYNC ->
                default.copy(cycleDuration = portcallSyncProperties.cycleDuration, enabled = portcallSyncProperties.enable)
            ScheduledTaskType.VOPAK_PORTBASE ->
                default.copy(
                    cycleDuration = Duration.ofHours(0),
                    lastPortcallUpdateDuration = Duration.ofHours(4),
                    enabled = portBaseProperties.vopak.enable,
                )
            ScheduledTaskType.S5_PORTBASE ->
                default.copy(
                    cycleDuration = Duration.ofHours(0),
                    lastPortcallUpdateDuration = Duration.ofHours(4),
                    enabled = portBaseProperties.s5.enable,
                )
            ScheduledTaskType.OUDKERK_PORTBASE ->
                default.copy(
                    cycleDuration = Duration.ofHours(0),
                    lastPortcallUpdateDuration = Duration.ofHours(24),
                    enabled = portBaseProperties.oudkerk.enable,
                )
            ScheduledTaskType.IAMCONNECTED_PORTBASE ->
                default.copy(
                    cycleDuration = Duration.ofHours(0),
                    lastPortcallUpdateDuration = Duration.ofHours(24),
                    enabled = portBaseProperties.iamconnected.enable,
                )
            ScheduledTaskType.MARITEAM_PORTBASE ->
                default.copy(
                    cycleDuration = Duration.ofHours(0),
                    lastPortcallUpdateDuration = Duration.ofHours(24),
                    enabled = portBaseProperties.mariteam.enable,
                )
            ScheduledTaskType.DIGITRAFFIC ->
                default.copy(
                    cycleDuration = digitrafficProperties.cycleDuration,
                    enabled = digitrafficProperties.enable,
                    lastPortcallUpdateDuration = digitrafficProperties.lastPortcallUpdate,
                )
            ScheduledTaskType.CORPUS_CHRISTI ->
                default.copy(
                    cycleDuration = corpusChristiProperties.cycleDuration,
                    enabled = corpusChristiProperties.enable,
                    lastPortcallUpdateDuration = corpusChristiProperties.lastPortcallUpdate,
                )
            ScheduledTaskType.VOPAK_NOMINATION ->
                default.copy(
                    cycleDuration = vopakNominationProperties.cycleDuration,
                    enabled = vopakNominationProperties.enable,
                    lastPortcallUpdateDuration = vopakNominationProperties.lastPortcallUpdate,
                )

            ScheduledTaskType.PORTCALL_GENERATOR -> default.copy(enabled = true)
            ScheduledTaskType.SMARTFLEET -> default.copy(enabled = smartFleetProperties.enabled)
            ScheduledTaskType.MTURK -> default.copy(enabled = mTurkProperties.enabled)
            ScheduledTaskType.HARBORLIGHTS,
            ScheduledTaskType.SIMPLY5_NOMINATION,
            ScheduledTaskType.TMA_NOMINATION,
            ScheduledTaskType.NM240_2HOURS,
            ScheduledTaskType.NM110_24HOURS,
            ScheduledTaskType.NM20_12HOURS,
            ScheduledTaskType.NM20_2HOURS_MOVING,
            ScheduledTaskType.NM20_4HOURS_NO_BERTH,
            ScheduledTaskType.NM20_8HOURS,
            ScheduledTaskType.NM10TO110_8HOURS,
            ScheduledTaskType.UNKNOWN,
            -> default
        }
    }
}
