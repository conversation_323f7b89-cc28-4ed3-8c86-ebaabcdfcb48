package nl.teqplay.portcallplus.model.service

import nl.teqplay.portcallplus.api.model.ServiceModel
import nl.teqplay.portcallplus.model.data.digitraffic.AgentInfo
import nl.teqplay.portcallplus.model.data.digitraffic.ImoInformation
import nl.teqplay.portcallplus.model.data.digitraffic.PortAreaDetails
import java.util.Date

data class DigitrafficPortcall(
    val agentInfo: List<AgentInfo>,
    val arrivalWithCargo: Boolean,
    val certificateEndDate: Date?,
    val certificateIssuer: String,
    val certificateStartDate: Date?,
    val currentSecurityLevel: Int?,
    val customsReference: String,
    val discharge: Int?,
    val domesticTrafficArrival: Boolean,
    val domesticTrafficDeparture: Boolean,
    val forwarderNameArrival: String,
    val forwarderNameDeparture: String,
    val freeTextArrival: String,
    val freeTextDeparture: String,
    val imoInformation: List<ImoInformation>,
    val imoLloyds: Int?,
    val managementNameArrival: String,
    val managementNameDeparture: String,
    val mmsi: Int,
    val nationality: String,
    val nextPort: String,
    val notLoading: Boolean,
    val portAreaDetails: List<PortAreaDetails>,
    val portCallId: Int,
    val portCallTimestamp: Date?,
    val portToVisit: String,
    val prevPort: String,
    val radioCallSign: String,
    val radioCallSignType: String,
    val shipMasterArrival: String,
    val shipMasterDeparture: String,
    val vesselName: String,
    val vesselNamePrefix: String,
    val vesselTypeCode: Int,
) : ServiceModel
