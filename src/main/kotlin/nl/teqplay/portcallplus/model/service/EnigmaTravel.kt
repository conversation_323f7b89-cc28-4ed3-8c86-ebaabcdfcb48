package nl.teqplay.portcallplus.model.service

import nl.teqplay.portcallplus.api.model.ServiceModel

data class EnigmaTravel(
    val from: String?,
    /** The destination of the vessel */
    val to: String?,
    /** The name of the vessel */
    val shipName: String?,
    val shipImo: String,
    /** time ordered */
    val orderTime: String,
    /** The berth it is going to */
    val berth: String,
    /** The company that entered the data. Usually the agency handling the vessel */
    val submitter: String?,
    val port: String,
) : ServiceModel
