package nl.teqplay.portcallplus.model.service

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonIgnore
import nl.teqplay.portcallplus.api.model.PortcallAliasName
import nl.teqplay.portcallplus.api.model.ServiceModel
import org.bson.codecs.pojo.annotations.BsonId
import java.util.Date

data class VopakNomination(
    @BsonId
    val callId: String,
    val terminalName: String,
    @JsonAlias("Jetty")
    val berth: VopakJetty = VopakJetty(),
    val source: String,
    val recordDateTime: Date,
    val status: String,
    val laycan: VopakLaycan,
    val vessel: VopakVessel,
    val timestamps: VopakTimestamp,
) : Nomination(PortcallAliasName.VOPAK_NOMINATION.name), ServiceModel {
    @JsonIgnore
    override val _id = callId
}

/**
 * Vessel data received via Vopak Nominations. Imo and name are sometimes missing
 */
data class VopakVessel(
    val imo: String?,
    val name: String?,
    @JsonAlias("Mmsi")
    val mmsi: String?,
)

/**
 * Laycan data implying when is it contractually allowed to occupy a terminal spot
 */
data class VopakLaycan(val start: Date?, val end: Date?)

data class VopakTimestamp(
    val plannedArrival: Date? = null,
    val actualArrival: Date? = null,
    val plannedDeparture: Date? = null,
    val actualDeparture: Date? = null,
)

// Jetty To be Nominated keyword
const val JETTY_TBN = "Jetty TBN"

data class VopakJetty(
    @JsonAlias("Id")
    val berthId: String = JETTY_TBN,
    @JsonAlias("Description")
    val berthName: String = "Jetty To Be Nominated",
)
