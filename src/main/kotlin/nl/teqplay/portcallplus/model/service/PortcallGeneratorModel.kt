package nl.teqplay.portcallplus.model.service

import nl.teqplay.portcallplus.api.model.PortcallPurpose
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.ServiceModel
import java.util.Date

/**
 * Simple class to contain all details needed to fetch or create a new portcall based on an request
 */
data class PortcallGeneratorModel(
    val shipImo: String,
    /** time the ship is expected */
    val orderTime: Date,
    val port: String,
    val portcallId: String? = null,
    val source: ScheduledTaskType = ScheduledTaskType.PORTCALL_GENERATOR,
    val purpose: Set<PortcallPurpose> = setOf(),
) : ServiceModel
