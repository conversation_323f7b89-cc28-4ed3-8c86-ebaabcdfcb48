package nl.teqplay.portcallplus.model.service

import nl.teqplay.portcallplus.api.model.ServiceModel

data class LisTravel(
    val from: String,
    /** The destination of the vessel */
    val to: String,
    /** The name of the vessel */
    val shipName: String,
    val shipImo: String?,
    /** The time the pilot is ordered  */
    val orderTime: String,
    /** The length of the vessel */
    val shipLength: Double?,
    /** The draught of the vessel */
    val shipDraught: Double?,
) : ServiceModel
