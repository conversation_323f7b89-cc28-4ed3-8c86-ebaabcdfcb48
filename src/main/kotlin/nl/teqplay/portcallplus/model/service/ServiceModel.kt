package nl.teqplay.portcallplus.model.service

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import nl.teqplay.portcallplus.api.model.DataModel
import nl.teqplay.portcallplus.api.model.Portcall

/**
 * Dummy abstract class to be extended by all nomination received that are persisted in the db
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "provider")
@JsonSubTypes(
    JsonSubTypes.Type(value = VopakNomination::class, name = "VOPAK_NOMINATION"),
    JsonSubTypes.Type(value = PortcallBerthNomination::class, name = "TMA_NOMINATION"),
)
abstract class Nomination(
    @JsonIgnore
    val provider: String,
) : DataModel()

/**
 * Class to wrap processed result of each [ServiceModel] from a Service endpoint.
 */
data class PortcallUpdateResult(
    val imo: String,
    /**
     * Actual response from the service provider and translated to internal Portcall type
     */
    val portcallFromService: Portcall,
    /**
     * Current portcall already found in the system, before processing updates in [portcallFromService]
     */
    val currentPortcall: Portcall? = null,
    /**
     * The portcall that is created/updated based on [portcallFromService]
     */
    val updatedPortcall: Portcall? = null,
)
