package nl.teqplay.portcallplus.model.service

import nl.teqplay.portcallplus.api.model.PortcallPurpose
import nl.teqplay.portcallplus.api.model.ServiceModel
import nl.teqplay.portcallplus.model.data.sgmdh.SgMdhVessel

data class SgMdhArrivalDeclaration(
    val vesselParticulars: SgMdhVessel,
    val location: String,
    val grid: String,
    val purpose: String,
    val agent: String,
    // In Singapore time
    val reportedArrivalTime: String,
) : ServiceModel {
    fun getPurpose(): Set<PortcallPurpose> {
        val result = hashSetOf<PortcallPurpose>()
        if (purpose.isEmpty()) {
            return result
        }
        val purposes = purpose.split(",")
        if (purposes[0] == "Y") {
            result.add(PortcallPurpose.CARGO)
        }
        if (purposes[1] == "Y") {
            result.add(PortcallPurpose.PASSENGERS)
        }
        if (purposes[2] == "Y") {
            result.add(PortcallPurpose.BUNKER)
        }
        if (purposes[3] == "Y") {
            result.add(PortcallPurpose.SUPPLIES)
        }
        if (purposes[4] == "Y") {
            result.add(PortcallPurpose.CREW)
        }
        if (purposes[5] == "Y") {
            result.add(PortcallPurpose.REPAIR)
        }
        if (purposes[6] == "Y") {
            result.add(PortcallPurpose.OFFSHORE)
        }
        if (purposes[7] == "Y") {
            result.add(PortcallPurpose.UNUSED)
        }
        if (purposes[8] == "Y") {
            result.add(PortcallPurpose.OTHER)
        }
        return result
    }
}
