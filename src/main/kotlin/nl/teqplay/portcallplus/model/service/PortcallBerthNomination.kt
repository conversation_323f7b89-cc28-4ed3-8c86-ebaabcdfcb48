package nl.teqplay.portcallplus.model.service

import com.fasterxml.jackson.annotation.JsonAlias
import nl.teqplay.portcallplus.api.model.PortcallAliasName
import nl.teqplay.portcallplus.api.model.ServiceModel
import java.util.Date
import java.util.UUID

data class PortcallBerthNomination(
    val uuid: String = UUID.randomUUID().toString(),
    val eventType: String?,
    val source: String,
    val portcallId: String?,
    val port: String,
    val location: LocationStandard?,
    val recordTime: Date,
    val eventTime: Date?,
    val ship: Ship?,
    val context: PortcallBerthContext,
) : Nomination(PortcallAliasName.TMA_NOMINATION.name), ServiceModel {
    override val _id: Any
        get() = uuid
}

data class Ship(
    val mmsi: String?,
    val imo: String?,
    val name: String?,
)

data class LocationStandard(
    @JsonAlias("teqplayid")
    val name: String?,
    val type: String?,
)

data class Mooring(
    val bollardAft: Int,
    val bollardFore: Int,
)

data class PortcallBerthContext(
    val vesselAgent: String?,
    val berthVisitId: String?,
    val organisationPortcallId: String?,
    val mooring: Mooring?,
)
