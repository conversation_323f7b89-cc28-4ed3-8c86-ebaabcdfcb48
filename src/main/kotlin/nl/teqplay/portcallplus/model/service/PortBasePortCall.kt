package nl.teqplay.portcallplus.model.service

import nl.teqplay.portcallplus.api.model.ServiceModel
import nl.teqplay.portcallplus.model.data.portbase.DangerousGoodsDeclaration
import nl.teqplay.portcallplus.model.data.portbase.DeclarationStatuses
import nl.teqplay.portcallplus.model.data.portbase.HealthDeclaration
import nl.teqplay.portcallplus.model.data.portbase.ImportDeclaration
import nl.teqplay.portcallplus.model.data.portbase.PaxDeclarationSummaries
import nl.teqplay.portcallplus.model.data.portbase.PortOfCall
import nl.teqplay.portcallplus.model.data.portbase.SecurityDeclaration
import nl.teqplay.portcallplus.model.data.portbase.ShipStoresDeclaration
import nl.teqplay.portcallplus.model.data.portbase.Vessel
import nl.teqplay.portcallplus.model.data.portbase.VisitDeclaration
import nl.teqplay.portcallplus.model.data.portbase.VisitShipOrganisation
import nl.teqplay.portcallplus.model.data.portbase.WasteCollection
import nl.teqplay.portcallplus.model.data.portbase.WasteDeclaration

data class PortBasePortCall(
    val crn: String,
    val portOfCall: PortOfCall,
    val vessel: Vessel,
    val owner: VisitShipOrganisation?,
    val declarant: VisitShipOrganisation?,
    val cargoDeclarants: List<VisitShipOrganisation>?,
    val visitDeclaration: VisitDeclaration,
    val securityDeclaration: SecurityDeclaration?,
    val dangerousGoodsDeclaration: DangerousGoodsDeclaration?,
    val wasteDeclaration: WasteDeclaration?,
    val wasteCollections: List<WasteCollection>?,
    val shipStoresDeclaration: ShipStoresDeclaration?,
    val paxDeclarationSummaries: List<PaxDeclarationSummaries>?,
    val healthDeclaration: HealthDeclaration?,
    val declarationStatuses: DeclarationStatuses?,
    val etaPortAis: String?,
    val ignoreEtaPortAis: Boolean?,
    val cancelled: Boolean?,
    val ataAcknowledged: Boolean?,
    val orderIncomingMovement: Boolean?,
    val importDeclarations: List<ImportDeclaration>?,
    val transhipments: List<Any>?,
) : ServiceModel
