package nl.teqplay.portcallplus.model.service

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonProperty
import nl.teqplay.portcallplus.api.model.ServiceModel
import java.util.Date

data class S5Nomination(
    @JsonProperty("callno")
    val portcallId: String,
    @JsonProperty("imono")
    val imo: String,
    @JsonProperty("portcode")
    val port: String,
    val eta: Date,
    val purpose: Set<S5NominationType> = setOf(),
) : ServiceModel

enum class S5NominationType {
    BUNKERS,
    DISCHARGING,
    SPARES,
    CREW_CHANGE,
    LOADING,
    PROVISION_STORES,
    TECHNICIAN,
    DE_SLOPPING,
    STS,
    SLUDGE_REMOVAL,
    CASH_TO_MASTER,
    OTHERS,
    UNKNOWN,
    ;

    companion object {
        @JsonCreator
        @JvmStatic
        fun getByName(type: String): S5NominationType {
            var matchingType = UNKNOWN
            values().forEach { nominationType ->

                val formattedName = nominationType.name.replace("_", "").uppercase()
                val types = type.split("/").map {
                    it.replace("-", "")
                        .replace(" ", "")
                        .replace("_", "")
                        .uppercase()
                }

                when {
                    types.any { it.startsWith(formattedName) || formattedName.startsWith(it) } -> return nominationType
                    types.any { it.contains(formattedName) || formattedName.contains(it) } -> matchingType = nominationType
                }
            }
            return matchingType
        }
    }
}
