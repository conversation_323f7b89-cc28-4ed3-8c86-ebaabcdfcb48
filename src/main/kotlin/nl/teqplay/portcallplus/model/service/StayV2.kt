package nl.teqplay.portcallplus.model.service

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonCreator
import nl.teqplay.portcallplus.api.model.ServiceModel

/**
 * A vessel stay according to the nxtPort's incremental fetch api
 */
data class StayV2(
    /** imo of the vessel. Annoying it could still be null. We ignore such data  */
    val imo: String?,
    /** Estimated time of arrival in the port  */
    @JsonAlias("cbs_entry_point_eta")
    val eta: String? = null,
    /** The origin as an UNLOCODE  */
    @JsonAlias("previous_port_of_call")
    val origin: String? = null,
    /** The destination as an UNLOCODE  */
    @JsonAlias("next_port_of_call")
    val destination: String? = null,
    /** unique stay or portcall number. Annoying it could still be null. We ignore such data  */
    @JsonAlias("stay_number")
    val stayNumber: String? = null,
    /** The name of the berth where the ship will arrive first  */
    @JsonAlias("berth_at_arrival")
    val berthArrival: String? = null,
    /** The name of the berth where the ship will last depart from  */
    @JsonAlias("berth_at_departure")
    val berthDeparture: String? = null,
) : ServiceModel {
    /**
     * Used by the json mapper
     */
    @JsonCreator
    constructor(
        ship: StayShip,
        cbs_entry_point_eta: String?,
        previous_port_of_call: String?,
        next_port_of_call: String?,
        stay_number: String?,
        berth_at_arrival: String?,
        berth_at_departure: String?,
    ) : this(
        ship.imo_number,
        cbs_entry_point_eta,
        previous_port_of_call,
        next_port_of_call,
        stay_number,
        berth_at_arrival,
        berth_at_departure,
    )
}

data class StayShip(
    val imo_number: String,
)
