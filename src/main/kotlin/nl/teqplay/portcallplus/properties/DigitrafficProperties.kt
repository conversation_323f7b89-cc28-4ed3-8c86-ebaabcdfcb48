package nl.teqplay.portcallplus.properties

import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.Duration

@ConfigurationProperties(prefix = "digitraffic")
data class DigitrafficProperties(
    val enable: Boolean,
    val allowOtherSourcePortcallUpdate: Boolean,
    val url: String,
    val cycleDuration: Duration,
    val lastPortcallUpdate: Duration,
    val newPortcallInterval: Long,
)
