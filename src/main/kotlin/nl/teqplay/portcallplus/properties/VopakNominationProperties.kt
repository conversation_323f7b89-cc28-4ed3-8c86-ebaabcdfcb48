package nl.teqplay.portcallplus.properties

import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.Duration

@ConfigurationProperties(prefix = "vopaknomination")
data class VopakNominationProperties(
    val enable: Boolean,
    val allowOtherSourcePortcallUpdate: Boolean,
    val cycleDuration: Duration,
    val lastPortcallUpdate: Duration,
    val username: String,
    val password: String,
)
