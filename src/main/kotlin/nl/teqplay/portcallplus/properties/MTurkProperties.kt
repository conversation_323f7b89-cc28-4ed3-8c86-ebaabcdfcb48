package nl.teqplay.portcallplus.properties

import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.Duration

@ConfigurationProperties("mturk")
data class MTurkProperties(
    val enabled: Boolean,
    val pullAssignments: <PERSON><PERSON>an,
    val pushAssignments: Bo<PERSON>an,
    val uri: String,
    val accessKeyId: String,
    val secretAccessKey: String,
    val hitLayoutId: String,
    val snsCallbackDestination: String,
    val autoReject: <PERSON>olean,
    val approveInvalidAnswers: <PERSON><PERSON>an,
    val disposeAfterExpiring: Boolean,
    val assignmentDuration: Duration,
    val assignmentLifetime: Duration,
    val autoApprovalDelay: Duration,
    val maxAssignments: Int,
    val daysConfigured: Long,
    val minimumSameValidAnswers: Int,
)
