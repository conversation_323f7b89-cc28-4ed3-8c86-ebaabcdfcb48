package nl.teqplay.portcallplus.properties

import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.Duration

@ConfigurationProperties(prefix = "portcallsync")
data class PortcallSyncProperties(
    val enable: Boolean,
    val allowOtherSourcePortcallUpdate: Boolean,
    val cycleDuration: Duration,
    val ports: List<String>,
    val url: String,
    val username: String,
    val password: String,
)
