package nl.teqplay.portcallplus.properties

import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.Duration

@ConfigurationProperties(prefix = "nxtportv2")
data class NxtPortV2Properties(
    val enable: Boolean,
    val allowOtherSourcePortcallUpdate: Boolean,
    val url: String,
    val loginUrl: String,
    val userName: String,
    val password: String,
    val clientId: String,
    val clientSecret: String,
    val subscriptionKey: String,
    val cycleDuration: Duration,
    val newPortcallIntervalInDays: Long,
    val portOfAntwerp: AntwerpProperties,
)

data class AntwerpProperties(
    val baseUrl: String,
    val stayNumberUrl: String,
    val timeout: Duration,
)
