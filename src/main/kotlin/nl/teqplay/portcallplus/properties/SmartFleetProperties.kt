package nl.teqplay.portcallplus.properties

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties("smartfleet")
data class SmartFleetProperties(
    val enabled: Boolean,
    val eta: SmartFleetEtaProperties,
)

// This class is used as a property of SmartFleetProperties
data class SmartFleetEtaProperties(
    val uri: String,
    val queuename: String,
    val ports: List<String>,
)
