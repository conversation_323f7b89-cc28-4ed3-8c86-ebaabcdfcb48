package nl.teqplay.portcallplus.properties

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "portbase")
data class PortBaseProperties(
    val enable: Boolean,
    val url: String,
    val newPortcallInterval: Long,
    /**
     * Timeout for the request to PortBase in milliseconds
     * Default on their side is 10 seconds
     */
    val maxRequestTimeOutMs: Long,
    val vopak: PortBaseClientProperties,
    val oudkerk: PortBaseClientProperties,
    val mariteam: PortBaseClientProperties,
    val s5: PortBaseClientProperties,
    val iamconnected: PortBaseClientProperties,
)
