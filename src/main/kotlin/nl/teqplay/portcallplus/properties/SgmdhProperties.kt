package nl.teqplay.portcallplus.properties

import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.Duration

@ConfigurationProperties(prefix = "sgmdh")
data class SgmdhProperties(
    val enable: Boolean,
    val allowOtherSourcePortcallUpdate: Boolean,
    val cycleDuration: Duration,
    val token: String,
    val dueToArriveData: Int,
    val arrivalDeclarationPastNHours: String,
    val dueToDepartData: Int,
    val newPortcallInterval: Long,
    val portcallExitInterval: Long,
    val ferryPortcallInterval: Long,
)
