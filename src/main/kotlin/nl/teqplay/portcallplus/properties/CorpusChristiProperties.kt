package nl.teqplay.portcallplus.properties

import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.Duration

@ConfigurationProperties("corpuschristi")
data class CorpusChristiProperties(
    val enable: <PERSON>olean,
    val allowOtherSourcePortcallUpdate: Boolean,
    val cycleDuration: Duration,
    val lastPortcallUpdate: Duration,
    val allowAgentUpdate: <PERSON>olean,
    val getNearestByImoAndDateRangeDays: Int,
    val s3: CorpusChristiS3Properties,
)
