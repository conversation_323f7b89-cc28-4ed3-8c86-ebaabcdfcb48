package nl.teqplay.portcallplus.properties

import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.Duration

/**
 * Not used directly in the logic, but Spring won't boot up if they aren't provided.
 */
@ConfigurationProperties("spring-schedule")
data class SpringScheduleProperties(
    val timezone: String,
    // Scheduled cron expression for the automatic clean up of too old files.
    val cleanCorpusChristiProcessedFilesCron: String,
    // Scheduled cron expression for
    val healthStatusCheckCron: String,
    val mTurk: MTurk,
    val portcallEnder: PortcallEnder,
    val publishCountersOnSlackCron: String,
)

/**
 * MTurk scheduled task cron details.
 */
data class MTurk(
    // Scheduled cron expression to automatically approve correct answers.
    val approveCorrectAnswersCron: String,
    // Scheduled cron expression for processing the MTurk HIT assignments.
    val hitAssignmentsProcessCron: String,
)

/**
 * Chron initial and fixed delay for the automatic portcall ender.
 */
data class PortcallEnder(
    val initialDelayDuration: Duration,
    val fixedDelayDuration: Duration,
)
