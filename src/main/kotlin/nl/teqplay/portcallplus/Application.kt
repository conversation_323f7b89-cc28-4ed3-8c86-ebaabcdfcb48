package nl.teqplay.portcallplus

import jakarta.annotation.PostConstruct
import jakarta.annotation.PreDestroy
import nl.teqplay.portcallplus.properties.MongoDbProperties
import nl.teqplay.portcallplus.service.common.TaskService
import nl.teqplay.portcallplus.utils.log
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.EnableAsync
import org.springframework.scheduling.annotation.EnableScheduling
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity

@SpringBootApplication(exclude = [MongoAutoConfiguration::class])
@EnableMethodSecurity(securedEnabled = true)
@ConfigurationPropertiesScan
@EnableScheduling
@EnableAsync
class Application : SpringBootServletInitializer() {
    @Autowired
    private lateinit var mongoDbProperties: MongoDbProperties

    @Autowired
    private lateinit var taskServices: List<TaskService>

    @PostConstruct
    private fun startup() {
        taskServices.forEach { it.startAllTasks() }
    }

    @PreDestroy
    private fun shutdown() {
        taskServices.forEach { it.stopAllTasks() }
    }

    @EventListener(ApplicationReadyEvent::class)
    fun init() {
        log(
            message =
            "PortcallPlus configuration: \n " +
                "mongodb.host= ${mongoDbProperties.host} \n" +
                "mongodb.port= ${mongoDbProperties.port} \n" +
                "mongodb.db= ${mongoDbProperties.db} \n" +
                "mongodb.authdb=${mongoDbProperties.authDb} \n" +
                "mongodb.username=${mongoDbProperties.username} \n" +
                "mongodb.password=" + (if (mongoDbProperties.password.isBlank()) "<empty>" else "<hidden>") + " \n",
        )
    }
}

fun main(args: Array<String>) {
    runApplication<Application>(*args)
}
