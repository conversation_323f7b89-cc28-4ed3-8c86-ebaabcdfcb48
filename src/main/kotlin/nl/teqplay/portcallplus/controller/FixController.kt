package nl.teqplay.portcallplus.controller

import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.ExampleObject
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusAgentChangedEvent
import nl.teqplay.portcallplus.model.httpResponse.fixagent.PortcallAgentFixResult
import nl.teqplay.portcallplus.service.internal.FixService
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.OffsetDateTime
import java.util.Date
import io.swagger.v3.oas.annotations.Operation as SwaggerOperation
import io.swagger.v3.oas.annotations.Parameter as SwaggerParameter
import io.swagger.v3.oas.annotations.parameters.RequestBody as SwaggerRequestBody

@RestController
@RequestMapping("/v1/fix")
class FixController(
    private val fixService: FixService,
) {
    @SwaggerOperation(description = "(Re)send PortcallPlusAgentChangedEvent-s for a given list of portcallIds.")
    @PostMapping("/fireMissedAgentChangedEvents")
    fun createAndFireMissingAgentChangedEvents(
        @SwaggerRequestBody(
            description = "PortcallIds to target to (re)send PortcallPlusAgentChangedEvent-s.",
            required = true,
            content = arrayOf(Content(examples = arrayOf(ExampleObject(value = "[\n\t\"BEANR1111111111\",\t\"BEANR2222222222\"]")))),
        )
        @RequestBody portcallIds: List<String>,
    ): List<PortcallPlusAgentChangedEvent> {
        return fixService.createAndFireMissingAgentChangedEvents(portcallIds)
    }

    /**
     * Endpoint to update the agent of portcalls  (source: NXTPORT_V2) in the range [from]-[to], by scraping the Port of Antwerp-Bruges site.
     * If [overrideAgent] is true, it'll set the agent a valid one was scraped.
     * If [triggerAgentChangeEvents] is true, it'll create and send the related PortcallPlusAgentChangedEvents.
     */
    @SwaggerOperation(
        description = "Endpoint to update the agent of portcalls  (source: NXTPORT_V2) in the range [from]-[to], by scraping the Port of Antwerp-Bruges site.",
    )
    @GetMapping("nxtport/setAgentToPortcallsByTimeRange")
    fun nxtPortsetAgentsToNxtPortPortcallsByTimeRange(
        @SwaggerParameter(
            description = "Low boundary for portcall.startTime.<br><b>Time between 'from' and 'to' will be coerced to 30 days, starting in 'from'.</b>",
            required = true,
            example = "2025-03-24T00:00:00.000Z",
        )
        @RequestParam
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
        from: OffsetDateTime,
        @SwaggerParameter(
            description = "Higher boundary for portcall.startTime.<br><b>Time between 'from' and 'to' will be coerced to 30 days, starting in 'from'.</b>",
            required = true,
            example = "2025-03-25T00:00:00.000Z",
        )
        @RequestParam
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
        to: OffsetDateTime,
        @SwaggerParameter(description = "Flag to override the portcall's agent if already set (not null).", required = true)
        @RequestParam
        overrideAgent: Boolean,
        @SwaggerParameter(description = "Flag to send PortcallAgentChange events if agent was set or updated.", required = true)
        @RequestParam
        triggerAgentChangeEvents: Boolean,
    ): PortcallAgentFixResult {
        val fromDate = Date.from(from.toInstant())
        val toDate = Date.from(to.toInstant())
        return fixService.setAgentsToNxtPortPortcallsByTimeRange(fromDate, toDate, overrideAgent, triggerAgentChangeEvents)
    }

    /**
     * Endpoint to update the agent for the given [portcallIds] (source: NXTPORT_V2), by scraping the Port of Antwerp-Bruges site.
     * If [overrideAgent] is true, it'll set the agent a valid one was scraped.
     * If [triggerAgentChangeEvents] is true, it'll create and send the related PortcallPlusAgentChangedEvents.
     */
    @SwaggerOperation(
        description = "Endpoint to update the agent for the given [portcallIds] (source: NXTPORT_V2), by scraping the Port of Antwerp-Bruges site.",
    )
    @PostMapping("nxtport/setAgentToPortcallsByPortcallId")
    fun nxtPortsetAgentsToNxtPortPortcallsByPortcallId(
        @SwaggerRequestBody(
            description = "PortcallIds to target on the scrape and set agent operation.",
            required = true,
            content = arrayOf(Content(examples = arrayOf(ExampleObject(value = "[\n\t\"BEANR1111111111\",\t\"BEANR2222222222\"]")))),
        )
        @RequestBody portcallIds: List<String>,
        @SwaggerParameter(description = "", required = true)
        @RequestParam
        overrideAgent: Boolean,
        @SwaggerParameter(description = "", required = true)
        @RequestParam
        triggerAgentChangeEvents: Boolean,
    ): PortcallAgentFixResult {
        return fixService.setAgentsToNxtPortPortcalls(portcallIds, overrideAgent, triggerAgentChangeEvents)
    }
}
