package nl.teqplay.portcallplus.controller

import io.swagger.annotations.ApiOperation
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.Portcall.Companion.IDPREFIX_NLAMS
import nl.teqplay.portcallplus.api.model.Portcall.Companion.IDPREFIX_NLRTM
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.utils.convertPlatformPortcall
import nl.teqplay.skeleton.platform.client.PortcallClient
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.client.HttpClientErrorException
import java.time.Instant

@RestController
@RequestMapping("/v2/portcall")
class PortcallV2Controller(
    private val portcallDataSource: PortcallDataSource,
    private val portcallClient: PortcallClient,
) {
    companion object {
        private val platformPrefixes = listOf(IDPREFIX_NLRTM, IDPREFIX_NLAMS)
    }

    @GetMapping("/{portcallId}")
    fun getPortcall(
        @PathVariable portcallId: String,
    ): Portcall? {
        return if (portcallId.portcallPrefix() in platformPrefixes) {
            portcallClient.getPortcallById(portcallId)
                ?.let { convertPlatformPortcall(it, ScheduledTaskType.UNKNOWN) }
        } else {
            portcallDataSource.get(portcallId)
        }
    }

    @ApiOperation(
        value = "Gets the currently ongoing portcall for the ship with the given IMO number, if any.",
    )
    @GetMapping("/imo/{imoNumber}")
    fun getPortcallByImo(
        @PathVariable imoNumber: String,
    ): Portcall? {
        val platformPortcall = try {
            portcallClient.getPortcallByImo(imoNumber, "")
        } catch (e: HttpClientErrorException.NotFound) {
            null
        }
        val ourPortcall = portcallDataSource.getMostRecentByImo(imoNumber)

        val platformStart = platformPortcall?.startTime?.let { Instant.ofEpochMilli(it) }
        val ourStart = ourPortcall?.startTime?.toInstant()

        return if (platformStart != null && (ourStart == null || platformStart > ourStart)) {
            convertPlatformPortcall(platformPortcall, ScheduledTaskType.UNKNOWN)
        } else {
            ourPortcall
        }
    }

    @ApiOperation(
        value = "Gets all portcalls in the given port for the ship with the given IMO number.",
    )
    @GetMapping("/imo/all/{port}/{imoNumber}")
    fun getAllPortcallsByImo(
        @PathVariable port: String,
        @PathVariable imoNumber: String,
    ): List<Portcall> {
        return if (port in platformPrefixes) {
            portcallClient.getAllPortcallsByImo(imoNumber, port)
                // Some platform portcalls have a null port?? Filter them out by ensuring the port matches the requested
                // one
                .filter { it.port == port }
                .map { convertPlatformPortcall(it, ScheduledTaskType.UNKNOWN) }
        } else {
            portcallDataSource.getByFiltered(
                shipImo = imoNumber,
                port = port,
            ).toList()
        }
    }

    private fun String.portcallPrefix() = substring(0, 5)
}
