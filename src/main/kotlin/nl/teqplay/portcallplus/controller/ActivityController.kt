package nl.teqplay.portcallplus.controller

import io.swagger.annotations.ApiOperation
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.Companion.getAllTasks
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.Companion.getTasksByPort
import nl.teqplay.portcallplus.datasource.ActivityDataSource
import nl.teqplay.portcallplus.model.data.Activity
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.OffsetDateTime
import java.util.Collections
import java.util.Date
import java.util.TreeSet

@RestController
@RequestMapping("/v1/activity")
class ActivityController {
    @Autowired
    private lateinit var activityDataSource: ActivityDataSource

    @ApiOperation(value = "Gets all activities for a max of 15 days, for the given filters")
    @RequestMapping(method = [RequestMethod.GET], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun getActivities(
        @RequestParam imo: String?,
        @RequestParam port: String?,
        @RequestParam taskType: ScheduledTaskType?,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) from: OffsetDateTime?,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) to: OffsetDateTime?,
    ): Set<Activity> {
        val fromDate = from?.toInstant()?.let { Date.from(it) }
        val toDate = to?.toInstant()?.let { Date.from(it) }
        val result = Collections.synchronizedSet(TreeSet<Activity>())
        // if no taskType is given. Repeatedly fetch all tasks for the given range (so we do not miss data for some tasks)
        if (taskType == null) {
            val tasks = if (port != null) {
                getTasksByPort(port.uppercase())
            } else {
                getAllTasks()
            }
            tasks.parallelStream().forEach { result.addAll(activityDataSource.get(imo, it, fromDate, toDate)) }
        } else {
            result.addAll(activityDataSource.get(imo, taskType, fromDate, toDate))
        }
        return if (imo != null) {
            result.filter { activity ->
                activity.imoList.contains(imo)
            }.toSortedSet()
        } else {
            result
        }
    }

    @ApiOperation(value = "Gets all activities categorized per day (for a max of 15 days) for the given filters")
    @RequestMapping("/categorized", method = [RequestMethod.GET], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun getActivitiesPerDay(
        @RequestParam imo: String?,
        @RequestParam port: String?,
        @RequestParam taskType: ScheduledTaskType?,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) from: OffsetDateTime?,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) to: OffsetDateTime?,
    ): TreeSet<Activity.ActivityData> {
        val activities = getActivities(imo, port, taskType, from, to)
        return Activity.ActivityData.getActivityData(activities)
    }
}
