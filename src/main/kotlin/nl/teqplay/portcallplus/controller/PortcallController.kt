package nl.teqplay.portcallplus.controller

import io.swagger.annotations.ApiOperation
import io.swagger.annotations.ApiParam
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import nl.teqplay.portcallplus.ModelNotFoundException
import nl.teqplay.portcallplus.PreconditionException
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallAliasName
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.model.Roles
import nl.teqplay.portcallplus.model.httpResponse.PortcallComparison
import nl.teqplay.portcallplus.model.service.PortcallGeneratorModel
import nl.teqplay.portcallplus.service.external.PortcallGeneratorService
import nl.teqplay.portcallplus.utils.exportPortcallsToExcel
import nl.teqplay.portcallplus.utils.fetchTimestamp
import nl.teqplay.portcallplus.utils.getPortcallByAlias
import nl.teqplay.portcallplus.utils.mapper
import nl.teqplay.skeleton.platform.client.PortcallClient
import nl.teqplay.skeleton.util.FileLog
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.MediaType
import org.springframework.security.access.annotation.Secured
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.OffsetDateTime
import java.util.Date
import java.util.TreeSet
import java.util.concurrent.TimeUnit

@RestController
@RequestMapping("/v1/portcall")
class PortcallController {
    @Autowired
    private lateinit var portcallDataSource: PortcallDataSource

    @Autowired
    private lateinit var portcallClient: PortcallClient

    @Autowired
    private lateinit var portcallGeneratorService: PortcallGeneratorService

    @ApiOperation(notes = "E.g. payload format: [\"V12345\", \"V12346\"]", value = "Get all the portcalls given in the request body")
    @RequestMapping(method = [RequestMethod.GET], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun getPortcalls(
        @RequestBody portcallIds: Collection<String>,
    ) = portcallDataSource.get(portcallIds)

    @ApiOperation(notes = "E.g. payload format: [\"imo1\", \"imo2\"]", value = "Get the most recently updated portcalls for the given imos")
    @RequestMapping(method = [RequestMethod.POST], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun getRecentPortcalls(
        @RequestBody imoNumbers: Set<String>,
    ): List<Portcall> {
        return portcallDataSource.getMostRecentPortcalls(imoNumbers)
    }

    /**
     * Gets all the updated portcalls for the given time frame.
     * @param from If null, defaulted to one month in the past
     * @param to If null, defaulted to current time
     */
    @ApiOperation(
        value = "Gets all the portcalls (sorted by update time) that are updated in the time frame given. " +
            "Defaulted to the last month. Date format: E.g. 2019-07-24T08:30:00.000Z",
    )
    @RequestMapping("/updates", method = [RequestMethod.GET], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun getUpdatedPortcalls(
        @RequestParam port: String?,
        @RequestParam ports: List<String>?,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) from: OffsetDateTime?,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) to: OffsetDateTime?,
        request: HttpServletRequest,
    ): List<Portcall> {
        val startDate = from?.toInstant()?.let { Date.from(it) } ?: fetchTimestamp(Date(), -30, TimeUnit.DAYS)
        val endDate = to?.toInstant()?.let { Date.from(it) } ?: Date()
        // combine port with ports for backwards compatibility
        val portsCombined: List<String>? = when {
            port == null && ports == null -> null
            port == null -> ports
            else -> (ports ?: emptyList<String>()) + port
        }
        val portcalls = portcallDataSource.getByUpdateTimes(portsCombined, startDate, endDate)
        FileLog().writeToLogFile(
            "${request.remoteHost} requested updated portcalls from: $startDate to: $to. " +
                "Returned: ${portcalls.size} portcalls. ${mapper.writeValueAsString(portcalls)}",
            "updatedPortcallsReturned.out",
        )
        return portcalls
    }

    @RequestMapping("/{portcallId}", method = [RequestMethod.GET], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun getPortcall(
        @PathVariable portcallId: String,
    ) = portcallDataSource.get(portcallId)

    /**
     * Gets the most recent 100 portcalls that are linked to the given [imoNumber]. Additional filters are optional.
     * @param from If null, defaulted to one month in the past
     */
    @ApiOperation(
        value = "Gets the most recent 100 portcalls that are linked to the given imoNumber. " +
            "Additional filters are optional. Date format: E.g. 2019-07-24T08:30:00.000Z",
    )
    @RequestMapping("/byImo/{imoNumber}", method = [RequestMethod.GET], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun getPortcallsByImo(
        @PathVariable imoNumber: String,
        @RequestParam port: String?,
        @RequestParam onlyFinished: Boolean?,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) from: OffsetDateTime?,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) to: OffsetDateTime?,
    ): TreeSet<Portcall> {
        val startDate = from?.toInstant()?.let { Date.from(it) } ?: fetchTimestamp(Date(), -30, TimeUnit.DAYS)
        val endDate = to?.toInstant()?.let { Date.from(it) }
        return portcallDataSource.getByFiltered(
            shipImo = imoNumber,
            port = port,
            startTime = startDate,
            endTime = endDate,
            finished = onlyFinished,
            limit = 100,
        )
    }

    @ApiOperation(value = "Compares the portcall in Portcall+ and Platform. Date format: E.g. 2019-07-24T08:30:00.000Z")
    @RequestMapping(path = ["/compare"], method = [RequestMethod.GET], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun comparePortcalls(
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) from: OffsetDateTime?,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) to: OffsetDateTime?,
        @RequestParam port: String?,
    ): PortcallComparison {
        val fromDate = from?.toInstant()?.let { Date.from(it) }
        val toDate = to?.toInstant()?.let { Date.from(it) }
        val platformPortcalls = portcallClient.getPortcallsInBeanr(fromDate?.time, toDate?.time)
        val portcallPlusPortcalls = portcallDataSource.getByFiltered(port = port, startTime = fromDate, endTime = toDate)

        val platformPortcallIds = platformPortcalls.map { it["portcallId"].toString() }.toSet()
        val portcallPlusPortcallIds = portcallPlusPortcalls.map { it.portcallId }.toSet()
        val overlappingPortcallIds = mutableSetOf<String>()
        val missingPortcallPlus = mutableSetOf<String>()
        val missingPlatform = mutableSetOf<String>()

        for (platformPortcallId in platformPortcallIds) {
            if (portcallPlusPortcallIds.contains(platformPortcallId)) {
                overlappingPortcallIds.add(platformPortcallId)
            } else {
                missingPortcallPlus.add(platformPortcallId)
            }
        }
        for (portcallPlusPortcallId in portcallPlusPortcallIds) {
            if (platformPortcallIds.contains(portcallPlusPortcallId)) {
                overlappingPortcallIds.add(portcallPlusPortcallId)
            } else {
                missingPlatform.add(portcallPlusPortcallId)
            }
        }
        return PortcallComparison(
            platformPortcallIds,
            portcallPlusPortcallIds,
            overlappingPortcallIds,
            missingPlatform,
            missingPortcallPlus,
        )
    }

    @ApiOperation(value = "Exports the portcalls")
    @RequestMapping(path = ["/export"], method = [RequestMethod.GET])
    fun exportPortcalls(
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) from: OffsetDateTime?,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) to: OffsetDateTime?,
        @RequestParam port: String?,
        @RequestParam vesselAgent: String?,
        response: HttpServletResponse,
    ) {
        val fromDate = from?.toInstant()?.let { Date.from(it) }
        val toDate = to?.toInstant()?.let { Date.from(it) }
        val portcalls = portcallDataSource.getByFiltered(
            port = port,
            vesselAgent = vesselAgent,
            startTime = fromDate,
            endTime = toDate,
            limit = 10000,
        )
        response.addHeader("Content-Disposition", "attachment; filename=portcalls${fetchTimestamp(Date(), "yyyy-MM-dd")}.xlsx")
        exportPortcallsToExcel(portcalls, response.outputStream)
        response.flushBuffer()
    }

    @Secured(Roles.ADMIN)
    @Deprecated("Replaced with the /nomination endpoints")
    @ApiOperation(value = "Read or create a portcall based on the given details in the payload")
    @RequestMapping(path = ["/getOrCreate"], method = [RequestMethod.PUT], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun getOrCreatePortcall(
        @RequestBody serviceModel: PortcallGeneratorModel,
    ): Portcall {
        // just check if a portcall can be created. It will throw an exception if it cannot
        portcallGeneratorService.convertToPortcall(serviceModel, serviceModel.shipImo)
        val result = portcallGeneratorService.updatePortcall(serviceModel).firstOrNull()
        return result?.updatedPortcall
            ?: result?.currentPortcall
            ?: throw PreconditionException("Oops, Portcall could not be found/generated for the given details!")
    }

    @ApiOperation(value = "Get the portcall that is related to information given")
    @RequestMapping(path = ["/nomination"], method = [RequestMethod.GET], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun getNominationPortcalls(
        @ApiParam("The imo of the ship", required = true) @RequestParam shipImo: String,
        @ApiParam("time the ship is expected", required = true) @RequestParam
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) orderTime: OffsetDateTime,
        @ApiParam("The port of the portcall", required = true) @RequestParam port: String,
        @ApiParam("The id of the portcall") @RequestParam portcallId: String?,
    ): List<Portcall> {
        val orderTimeDate = Date.from(orderTime.toInstant())
        return portcallGeneratorService.getNominations(PortcallGeneratorModel(shipImo, orderTimeDate, port, portcallId))
    }

    @Secured(Roles.ADMIN)
    @ApiOperation(
        value = "Create the portcall with the given information without questioning it, will return an " +
            "conflict error if it already exists",
    )
    @RequestMapping(path = ["/nomination"], method = [RequestMethod.POST], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun nominatePortcall(
        @ApiParam("The imo of the ship", required = true) @RequestParam shipImo: String,
        @ApiParam("time the ship is expected", required = true) @RequestParam
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) orderTime: OffsetDateTime,
        @ApiParam("The port of the portcall", required = true) @RequestParam port: String,
        @ApiParam("The id of the portcall", required = true) @RequestParam portcallId: String?,
        @ApiParam("Which customer uses this id", required = false) @RequestParam portcallAlias: PortcallAliasName?,
    ): Portcall {
        val orderTimeDate = Date.from(orderTime.toInstant())
        // just check if a portcall can be created. It will throw an exception if it cannot
        return portcallGeneratorService.createNomination(
            PortcallGeneratorModel(shipImo, orderTimeDate, port, portcallId), portcallAlias,
        ).updatedPortcall ?: throw PreconditionException("Oops, Portcall could not be created for the given details!")
    }

    @Secured(Roles.ADMIN)
    @ApiOperation(value = "Update the portcall received from the get")
    @RequestMapping(path = ["/nomination"], method = [RequestMethod.PUT], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun updateNominatedPortcall(
        @ApiParam("The imo of the ship", required = true) @RequestParam shipImo: String,
        @ApiParam("time the ship is expected", required = true) @RequestParam
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) orderTime: OffsetDateTime,
        @ApiParam("The port of the portcall", required = true) @RequestParam port: String,
        @ApiParam("The id of the portcall", required = true) @RequestParam portcallId: String?,
        @ApiParam("Which customer uses this id", required = false) @RequestParam portcallAlias: PortcallAliasName?,
    ): Portcall {
        val orderTimeDate = Date.from(orderTime.toInstant())
        return portcallGeneratorService.updateNomination(
            PortcallGeneratorModel(shipImo, orderTimeDate, port, portcallId), portcallAlias,
        ).updatedPortcall
            ?: throw PreconditionException("Oops, Portcall could not be found/updated for the given details!")
    }

    /**
     * Search the portcallId of a specific service e.g. Simply5 specific portcallId or Portbase portcallId
     * @return returns a mapping of the internal known portcallId to the service's portcallId
     */
    @ApiOperation(value = "search the portcallId of a specific service e.g. Simply5 specific portcallId or Portbase portcallId")
    @RequestMapping("/alias/{aliasName}/{searchPattern}", method = [RequestMethod.GET], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun getPortcallIdByAlias(
        @PathVariable aliasName: PortcallAliasName,
        @PathVariable searchPattern: String,
    ): Map<String, String?> {
        val portcalls = portcallDataSource.searchByAlias(searchPattern, aliasName)
        if (portcalls.isNullOrEmpty()) {
            throw ModelNotFoundException("No alias found by aliasName: $aliasName for portcallId: $searchPattern")
        }
        return portcalls.associate {
                portcall ->
            portcall.portcallId to portcall.portcallAlias.firstOrNull { it.source == aliasName }?.alias
        }
    }

    /**
     * Get the portcallId of a specific service e.g. Simply5 specific portcallId or Portbase portcallId
     */
    @ApiOperation(
        value = "Get the mapping from portcallId to portcallId of a specific service e.g. Simply5 specific portcallId or Portbase portcallId" +
            " (Bulk operation)",
    )
    @RequestMapping("/alias/{aliasName}", method = [RequestMethod.POST], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun getPortcallIdsByAlias(
        @PathVariable aliasName: PortcallAliasName,
        @RequestBody portcallIds: Set<String>,
    ): Map<String, String?> {
        val portcallsByAlias = portcallDataSource.getByIdsAndAlias(portcallIds, aliasName)
        return getPortcallByAlias(portcallsByAlias, portcallIds, aliasName)
    }
}
