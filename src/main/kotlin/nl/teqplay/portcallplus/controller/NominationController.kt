package nl.teqplay.portcallplus.controller

import io.swagger.annotations.ApiOperation
import nl.teqplay.portcallplus.api.model.PortcallAliasName
import nl.teqplay.portcallplus.datasource.NominationDataSource
import nl.teqplay.portcallplus.model.service.Nomination
import nl.teqplay.portcallplus.utils.fetchTimestamp
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.OffsetDateTime
import java.util.Date
import java.util.concurrent.TimeUnit

@RestController
@RequestMapping("/v1/nomination")
class NominationController(
    private val portcallController: Port<PERSON>l<PERSON>ontroller,
    private val nominationDataSource: NominationDataSource,
) {
    @ApiOperation(value = "Gets all the nominations ")
    @RequestMapping(method = [RequestMethod.GET], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun getNominations(
        @RequestParam provider: PortcallAliasName?,
        @RequestParam portcallId: String?,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) from: OffsetDateTime?,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) to: OffsetDateTime?,
    ): List<Nomination> {
        val startDate = from?.toInstant()?.let { Date.from(it) } ?: fetchTimestamp(Date(), -30, TimeUnit.DAYS)
        val endDate = to?.toInstant()?.let { Date.from(it) } ?: Date()
        val nominations = nominationDataSource.getByType(portcallId, provider?.name, startDate, endDate)
        if (nominations.isEmpty() && provider != null && portcallId != null) {
            val alias = portcallController.getPortcallIdsByAlias(provider, setOf(portcallId))[portcallId]
            if (alias != null) {
                return nominationDataSource.getByType(alias, provider.name, startDate, endDate)
            }
        }
        return nominations
    }
}
