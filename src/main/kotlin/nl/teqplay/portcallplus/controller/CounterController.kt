package nl.teqplay.portcallplus.controller

import io.swagger.annotations.ApiOperation
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.datasource.ServiceFetchCounterDataSource
import nl.teqplay.portcallplus.model.ScheduledTaskTypeFieldsProxy
import nl.teqplay.portcallplus.model.data.UpdateType
import nl.teqplay.portcallplus.model.httpResponse.ServiceFetchCounterResult
import nl.teqplay.portcallplus.utils.getServiceFetchCounters
import nl.teqplay.portcallplus.utils.getStartOfTheDay
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.OffsetDateTime
import java.util.Date

@RestController
@RequestMapping("/v1/serviceFetchCounter")
class CounterController {
    @Autowired
    private lateinit var serviceFetchCounterDataSource: ServiceFetchCounterDataSource

    @Autowired
    private lateinit var scheduledTaskTypeFieldsProxy: ScheduledTaskTypeFieldsProxy

    @ApiOperation(value = "Fetches the NxtPort counters for the given parameters. Date format: E.g. 2019-07-24T08:30:00.000Z")
    @RequestMapping(method = [RequestMethod.GET], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun getCount(
        @RequestParam detailed: Boolean?,
        @RequestParam updateType: UpdateType?,
        @RequestParam taskType: ScheduledTaskType?,
        @RequestParam onlyPortcall: Boolean?,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) startDate: OffsetDateTime?,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) endDate: OffsetDateTime?,
        @RequestBody portcallIds: Set<String>? = null,
    ): ServiceFetchCounterResult {
        val start = startDate?.toInstant()?.let { Date.from(it) } ?: getStartOfTheDay(Date())
        val end = endDate?.toInstant()?.let { Date.from(it) } ?: Date()
        val serviceCounters = serviceFetchCounterDataSource.getCounters(
            portcallIds ?: setOf(),
            taskType,
            start,
            end,
        )
        return getServiceFetchCounters(
            serviceCounters,
            portcallIds ?: setOf(),
            updateType,
            detailed ?: false,
            onlyPortcall ?: false,
            scheduledTaskTypeFieldsProxy,
        )
    }
}
