package nl.teqplay.portcallplus.controller

import io.swagger.annotations.ApiOperation
import nl.teqplay.portcallplus.PreconditionException
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.CORPUS_CHRISTI
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.DIGITRAFFIC
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.ENIGMA_SCRAPER_INCOMING
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.ENIGMA_SCRAPER_OUTGOING
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.HARBORLIGHTS
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.IAMCONNECTED_PORTBASE
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.LIS_SCRAPPER
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.MARITEAM_PORTBASE
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.MTURK
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.NM10TO110_8HOURS
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.NM110_24HOURS
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.NM20_12HOURS
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.NM20_2HOURS_MOVING
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.NM20_4HOURS_NO_BERTH
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.NM20_8HOURS
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.NM240_2HOURS
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.NXTPORT_V2
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.OUDKERK_PORTBASE
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.PORTCALL_GENERATOR
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.PORTCALL_SYNC
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.S5_PORTBASE
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.SG_MDH_DUE_TO_ARRIVE
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.SG_MDH_DUE_TO_DEPART
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.SG_MDH_VISIT_ARRIVAL_DECLARATION
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.SIMPLY5_NOMINATION
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.SMARTFLEET
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.TMA_NOMINATION
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.UNKNOWN
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.VOPAK_NOMINATION
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.VOPAK_PORTBASE
import nl.teqplay.portcallplus.config.CacheConfiguration
import nl.teqplay.portcallplus.service.external.CorpusChristiService
import nl.teqplay.portcallplus.service.external.DigitrafficService
import nl.teqplay.portcallplus.service.external.EnigmaScraperService
import nl.teqplay.portcallplus.service.external.LisScraperService
import nl.teqplay.portcallplus.service.external.PortcallSyncService
import nl.teqplay.portcallplus.service.external.SgMdhService
import nl.teqplay.portcallplus.service.external.nxtport.VesselStayServiceV2
import nl.teqplay.portcallplus.service.external.portbase.IAmConnectedPortBaseService
import nl.teqplay.portcallplus.service.external.portbase.MariteamPortBaseService
import nl.teqplay.portcallplus.service.external.portbase.OudkerkPortBaseService
import nl.teqplay.portcallplus.service.external.portbase.S5PortBaseService
import nl.teqplay.portcallplus.service.external.portbase.VopakPortBaseService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.cache.CacheManager
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

object GenericControllerEndpoints {
    const val PREFIX = "/v1"
    const val FORCE_RUN = "/forceRun/{taskType}"
    const val FORCE_STOP = "/forceStop/{taskType}"
}

/**
 * General controller for maintaining and monitoring the status of this application
 */
@RestController
@RequestMapping(GenericControllerEndpoints.PREFIX)
class GenericController {
    @Autowired
    private lateinit var vesselStayServiceV2: VesselStayServiceV2

    @Autowired
    private lateinit var lisScraperService: LisScraperService

    @Autowired
    private lateinit var enigmaScraperService: EnigmaScraperService

    @Autowired
    private lateinit var sgMdhService: SgMdhService

    @Autowired
    private lateinit var portcallSyncService: PortcallSyncService

    @Autowired
    private lateinit var vopakPortbaseService: VopakPortBaseService

    @Autowired
    private lateinit var s5PortBaseService: S5PortBaseService

    @Autowired
    private lateinit var oudkerkPortBaseService: OudkerkPortBaseService

    @Autowired
    private lateinit var iamconnectedPortBaseService: IAmConnectedPortBaseService

    @Autowired
    private lateinit var mariteamPortBaseService: MariteamPortBaseService

    @Autowired
    private lateinit var digitrafficService: DigitrafficService

    @Autowired
    private lateinit var corpusChristiService: CorpusChristiService

    @Autowired
    private lateinit var cacheManager: CacheManager

    @ApiOperation(
        value = "Force triggers a task to be executed now!. Can be used to coarse correct some task, without " +
            "waiting for the task to be executed in the next run",
    )
    @RequestMapping(
        GenericControllerEndpoints.FORCE_RUN,
        method = [RequestMethod.POST],
        produces = [
            MediaType
                .APPLICATION_JSON_VALUE,
        ],
    )
    fun forceRunTask(
        @PathVariable taskType: ScheduledTaskType,
    ): Boolean {
        return when (taskType) {
            LIS_SCRAPPER -> lisScraperService.triggerRunnableTask(taskType, true)

            ENIGMA_SCRAPER_INCOMING,
            ENIGMA_SCRAPER_OUTGOING,
            -> enigmaScraperService.triggerRunnableTask(taskType, true)

            PORTCALL_SYNC -> portcallSyncService.triggerRunnableTask(taskType, true)

            VOPAK_PORTBASE -> vopakPortbaseService.triggerRunnableTask(taskType, true)
            S5_PORTBASE -> s5PortBaseService.triggerRunnableTask(taskType, true)
            OUDKERK_PORTBASE -> oudkerkPortBaseService.triggerRunnableTask(taskType, true)
            IAMCONNECTED_PORTBASE -> iamconnectedPortBaseService.triggerRunnableTask(taskType, true)
            MARITEAM_PORTBASE -> mariteamPortBaseService.triggerRunnableTask(taskType, true)

            SG_MDH_DUE_TO_ARRIVE,
            SG_MDH_VISIT_ARRIVAL_DECLARATION,
            SG_MDH_DUE_TO_DEPART,
            -> sgMdhService.triggerRunnableTask(taskType, true)

            NXTPORT_V2 -> vesselStayServiceV2.triggerRunnableTask(taskType)

            DIGITRAFFIC -> digitrafficService.triggerRunnableTask(taskType, true)

            CORPUS_CHRISTI -> corpusChristiService.triggerRunnableTask(taskType, true)

            PORTCALL_GENERATOR, SIMPLY5_NOMINATION, MTURK, SMARTFLEET, TMA_NOMINATION, HARBORLIGHTS, UNKNOWN,
            NM10TO110_8HOURS, NM240_2HOURS, NM110_24HOURS, NM20_12HOURS,
            NM20_2HOURS_MOVING, NM20_4HOURS_NO_BERTH, NM20_8HOURS, VOPAK_NOMINATION,
            ->
                throw PreconditionException("Unhandled taskType", taskType.name)
        }
    }

    @ApiOperation(value = "Force triggers a task to be stopped now!")
    @RequestMapping(
        GenericControllerEndpoints.FORCE_STOP,
        method = [RequestMethod.POST],
        produces = [
            MediaType
                .APPLICATION_JSON_VALUE,
        ],
    )
    fun forceStopTask(
        @PathVariable taskType: ScheduledTaskType,
    ): Boolean {
        return when (taskType) {
            LIS_SCRAPPER -> lisScraperService.stopTask(taskType, true)

            ENIGMA_SCRAPER_INCOMING,
            ENIGMA_SCRAPER_OUTGOING,
            -> enigmaScraperService.stopTask(taskType, true)

            PORTCALL_SYNC -> portcallSyncService.stopTask(taskType, true)

            VOPAK_PORTBASE -> vopakPortbaseService.stopTask(taskType, true)
            S5_PORTBASE -> s5PortBaseService.stopTask(taskType, true)
            OUDKERK_PORTBASE -> oudkerkPortBaseService.stopTask(taskType, true)
            IAMCONNECTED_PORTBASE -> iamconnectedPortBaseService.stopTask(taskType, true)
            MARITEAM_PORTBASE -> mariteamPortBaseService.stopTask(taskType, true)

            SG_MDH_DUE_TO_ARRIVE,
            SG_MDH_VISIT_ARRIVAL_DECLARATION,
            SG_MDH_DUE_TO_DEPART,
            -> sgMdhService.stopTask(taskType, true)

            NXTPORT_V2 -> vesselStayServiceV2.stopTask(taskType, true)

            DIGITRAFFIC -> digitrafficService.stopTask(taskType, true)

            CORPUS_CHRISTI -> corpusChristiService.stopTask(taskType, true)

            PORTCALL_GENERATOR, SIMPLY5_NOMINATION, MTURK, SMARTFLEET, TMA_NOMINATION, HARBORLIGHTS, UNKNOWN,
            NM10TO110_8HOURS, NM240_2HOURS, NM110_24HOURS, NM20_12HOURS,
            NM20_2HOURS_MOVING, NM20_4HOURS_NO_BERTH, NM20_8HOURS, VOPAK_NOMINATION,
            ->
                throw PreconditionException("Unhandled taskType", taskType.name)
        }
    }

    @ApiOperation(value = "Fetch all task details that are currently running and when they are expected to run next!")
    @RequestMapping("/task/details", method = [RequestMethod.GET], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun fetchTaskDetails() = lisScraperService.getScheduledTaskDetails()

    @ApiOperation(value = "Fetch all cache details")
    @RequestMapping("/cache", method = [RequestMethod.GET, RequestMethod.POST], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun fetchCacheDetails(
        @RequestParam cacheName: String?,
        @RequestBody cacheKey: Any?,
    ): Map<String, Map<Any, Any>?> {
        return if (cacheName != null) {
            mapOf(cacheName to CacheConfiguration.getCache(cacheManager, cacheName))
        } else {
            cacheManager.cacheNames.associateWith {
                CacheConfiguration.getCache(cacheManager, it)
            }
        }
    }

    @ApiOperation(value = "Evict cache for the given cacheKey")
    @RequestMapping("/cache/{cacheName}", method = [RequestMethod.DELETE], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun evictCache(
        @PathVariable cacheName: String,
    ): Map<Any, Any>? {
        return CacheConfiguration.evictCache(cacheManager, cacheName)
    }
}
