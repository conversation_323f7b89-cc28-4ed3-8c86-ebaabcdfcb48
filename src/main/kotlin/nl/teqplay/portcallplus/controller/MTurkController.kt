package nl.teqplay.portcallplus.controller

import io.swagger.annotations.ApiOperation
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import nl.teqplay.portcallplus.model.data.mturk.HitStatus
import nl.teqplay.portcallplus.model.data.mturk.WorkersNotification
import nl.teqplay.portcallplus.model.service.MTurkHIT
import nl.teqplay.portcallplus.service.external.MTurkService
import nl.teqplay.portcallplus.utils.log
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import software.amazon.awssdk.services.mturk.model.EventType
import io.swagger.v3.oas.annotations.parameters.RequestBody as SwaggerRequestBody

@RestController
@RequestMapping("/v1/mturk")
@ApiOperation(value = "Callbacks used to handle MTurk assignment related information")
class MTurkController {
    @Autowired
    private lateinit var mTurkService: MTurkService

    /**
     * Get the information from MTurk assignment via SNS configured here:
     * https://console.aws.amazon.com/sns/v3/home?region=us-east-1#/topic/arn:aws:sns:us-east-1:050356841556:PortcallPlus_Agent_callback_via_MTurk
     */
    @ApiOperation(value = "Callback used to handle MTurk assignment to update agent name for the related portcall")
    @RequestMapping("/vesselAgent", method = [RequestMethod.POST])
    fun getMTurkCallback(
        @RequestBody callbackPayload: String,
    ) {
        log(message = "MTurk assignment response seen: $callbackPayload")
        mTurkService.updatePortcall(callbackPayload)
    }

    /**
     * Estimate the HITs processing at the current time.
     */
    @ApiOperation(value = "Estimate the HITs processing at the current time.")
    @RequestMapping("/estimateHITsProcessing", method = [RequestMethod.GET])
    fun estimateHITsProcessing() {
        CoroutineScope(Job() + Dispatchers.IO).launch {
            mTurkService.estimateHITsProcessing()
        }
    }

    /**
     * Process the given list of HITs.
     */
    @ApiOperation(value = "Process the given hits.")
    @RequestMapping("/process", method = [RequestMethod.POST])
    fun processHITs(
        @RequestBody
        @SwaggerRequestBody(description = "HIT ids to process (update HIT, its assignments and related portcall.")
        hits: Set<String>,
    ) {
        log.info { "Processing HITs $hits by request." }
        CoroutineScope(Job() + Dispatchers.IO).launch {
            mTurkService.processHits(hits)
        }
    }

    @ApiOperation(value = "Creates multiple MTurk assignments to update agent name for the given details")
    @RequestMapping(method = [RequestMethod.POST], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun createHITs(
        @RequestBody mTurkHITs: Set<MTurkHIT>,
    ) = mTurkService.handleHITRequests(mTurkHITs)

    @ApiOperation(
        value = "Remove multiple MTurk assignments from MTurk, " +
            "can only be done after it has been expired or all answers have been given and Accepted/Rejected",
    )
    @RequestMapping("/delete", method = [RequestMethod.DELETE], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun deleteHITs(
        @RequestBody hitIds: Set<String>,
    ) {
        mTurkService.deleteHITs(hitIds)
    }

    @ApiOperation(value = "Remove multiple MTurk assignments from MTurk")
    @RequestMapping(method = [RequestMethod.DELETE], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun expireHITs(
        @RequestBody hitIds: Set<String>,
    ) {
        mTurkService.expireHITs(hitIds)
    }

    @ApiOperation(value = "Gets all the HITs and its status in MTurk")
    @RequestMapping(method = [RequestMethod.GET], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun getHITs(
        @RequestParam hitId: String?,
        @RequestParam assignmentId: String?,
        @RequestParam workerId: String?,
        @RequestParam shipImo: String?,
        @RequestParam portcallId: String?,
    ) = mTurkService.getHITs(hitId, assignmentId, workerId, shipImo, portcallId)

    @ApiOperation(value = "Gets all the HITs and its status in MTurk")
    @RequestMapping(
        "/assignment/checkApproval/{hitId}",
        method = [RequestMethod.PUT],
        produces = [MediaType.APPLICATION_JSON_VALUE],
    )
    fun checkAssignmentApproval(
        @PathVariable hitId: String,
        @RequestParam(defaultValue = "false") force: Boolean,
    ) = mTurkService.assessHITAssignment(hitId, force)

    @ApiOperation(value = "Sends test event")
    @RequestMapping(
        "/assignment/test/{eventType}",
        method = [RequestMethod.POST],
        produces = [MediaType.APPLICATION_JSON_VALUE],
    )
    fun sendTestEventType(
        @PathVariable eventType: EventType,
    ) = mTurkService.sendTestEvent(eventType)

    @ApiOperation(value = "Get status html page")
    @RequestMapping(
        "/html/status",
        method = [RequestMethod.GET],
        produces = [MediaType.TEXT_HTML_VALUE],
    )
    fun getHtmlStatusPage(
        @RequestParam status: HitStatus?,
        @RequestParam onlyAvailableTasks: Boolean?,
        @RequestParam onlyAllTasksCompleted: Boolean?,
        @RequestParam onlyExpiredHITs: Boolean?,
    ) = mTurkService.getHtmlStatus(
        status = status,
        onlyAvailableTasks = onlyAvailableTasks ?: false,
        onlyAllTasksCompleted = onlyAllTasksCompleted ?: false,
        onlyExpiredHITs = onlyExpiredHITs ?: false,
    )

    @ApiOperation(value = "Get status html page")
    @RequestMapping(
        "/answers/{hitId}",
        method = [RequestMethod.GET],
        produces = [MediaType.APPLICATION_JSON_VALUE],
    )
    fun getHITAnswers(
        @PathVariable hitId: String,
    ) = mTurkService.getHITAnswers(hitId)

    @ApiOperation(value = "Approve the an answer to a HIT by assignmentId")
    @RequestMapping(
        "/answers/approve/{assignmentId}",
        method = [RequestMethod.GET],
        produces = [MediaType.APPLICATION_JSON_VALUE],
    )
    fun approveAssignment(
        @PathVariable assignmentId: String,
        @RequestParam overrideRejection: Boolean?,
    ) = mTurkService.manuallyApprove(
        assignmentId = assignmentId,
        overrideRejection = overrideRejection ?: false,
    )

    @ApiOperation(value = "Reject the an answer to a HIT by assignmentId")
    @RequestMapping(
        "/answers/reject/{assignmentId}",
        method = [RequestMethod.GET],
        produces = [MediaType.APPLICATION_JSON_VALUE],
    )
    fun rejectAssignment(
        @PathVariable assignmentId: String,
    ) = mTurkService.manuallyReject(assignmentId)

    @ApiOperation(value = "Get a list of all blocked workers and the reason")
    @RequestMapping(
        "/workers/blocked",
        method = [RequestMethod.GET],
        produces = [MediaType.APPLICATION_JSON_VALUE],
    )
    fun getBlockedWorkers(
        @RequestParam limit: Int,
    ) = mTurkService.getBlockedWorkers(limit)

    @ApiOperation(value = "Send workers a message based on their workerId")
    @RequestMapping(
        "/workers/notify",
        method = [RequestMethod.POST],
        produces = [MediaType.APPLICATION_JSON_VALUE],
    )
    fun getBlockedWorkers(
        @RequestBody workersNotification: WorkersNotification,
    ) = mTurkService.notifyWorkers(workersNotification)

    @ApiOperation(value = "List all HITs in the reviewable state, excludes the Reviewing state")
    @RequestMapping(
        "/list/reviewableHITs",
        method = [RequestMethod.GET],
        produces = [MediaType.APPLICATION_JSON_VALUE],
    )
    fun listReviewableHITs() = mTurkService.listReviewableHits()

    @ApiOperation(value = "List all Hits")
    @RequestMapping(
        "/list/HITs",
        method = [RequestMethod.GET],
        produces = [MediaType.APPLICATION_JSON_VALUE],
    )
    fun listHITs(
        @RequestParam status: HitStatus?,
        @RequestParam onlyAvailableTasks: Boolean?,
        @RequestParam onlyAllTasksCompleted: Boolean?,
        @RequestParam onlyExpiredHITs: Boolean?,
    ) = mTurkService.listHits(
        status = status,
        onlyAvailableTasks = onlyAvailableTasks ?: false,
        onlyAllTasksCompleted = onlyAllTasksCompleted ?: false,
        onlyExpiredHITs = onlyExpiredHITs ?: false,
    )

    @RequestMapping(
        "/list/hitassignments/{hitId}",
        method = [RequestMethod.GET],
        produces = [MediaType.APPLICATION_JSON_VALUE],
    )
    fun fetchAssignments(
        @PathVariable hitId: String,
    ) = mTurkService.fetchAssignmentsByHit(hitId = hitId)
}
