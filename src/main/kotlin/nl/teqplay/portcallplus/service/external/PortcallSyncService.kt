package nl.teqplay.portcallplus.service.external

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.portcallplus.PreconditionException
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.ServiceModel
import nl.teqplay.portcallplus.datasource.ActivityDataSource
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.datasource.ScheduledTaskRunDataSource
import nl.teqplay.portcallplus.datasource.ServiceFetchCounterDataSource
import nl.teqplay.portcallplus.model.ScheduledTaskTypeFieldsProxy
import nl.teqplay.portcallplus.properties.PortcallSyncProperties
import nl.teqplay.portcallplus.service.common.EventService
import nl.teqplay.portcallplus.service.common.TaskService
import nl.teqplay.portcallplus.service.internal.PoMaService
import nl.teqplay.portcallplus.service.internal.UserService
import nl.teqplay.portcallplus.utils.executeGetCall
import nl.teqplay.portcallplus.utils.fetchTimestamp
import nl.teqplay.portcallplus.utils.log
import okhttp3.Credentials
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import java.util.Date
import java.util.concurrent.TimeUnit

/**
 * Service class housing all the logic and scheduling mechanism for creating/updating [Portcall] for all ports that
 * this project instance is not enabled for.
 * E.g. NxtPort [VesselStayService] could be enabled in one server and data could be shared.
 */
@Service
class PortcallSyncService(
    scheduledTaskRunDataSource: ScheduledTaskRunDataSource,
    activityDataSource: ActivityDataSource,
    serviceFetchCounterDataSource: ServiceFetchCounterDataSource,
    portcallDataSource: PortcallDataSource,
    val mapper: ObjectMapper,
    private val portcallSyncProperties: PortcallSyncProperties,
    scheduledTaskTypeFieldsProxy: ScheduledTaskTypeFieldsProxy,
    userService: UserService,
    eventService: EventService,
    pomaService: PoMaService,
) : TaskService(
    scheduledTaskRunDataSource,
    activityDataSource,
    serviceFetchCounterDataSource,
    portcallDataSource,
    scheduledTaskTypeFieldsProxy,
    userService,
    pomaService,
    eventService,
) {
    private val TIMESTAMP_FORMAT = "yyyy-MM-dd'T'HH:mm:ss'.000Z'"

    override fun getTasks(): List<ScheduledTaskType> = listOf(ScheduledTaskType.PORTCALL_SYNC)

    override val isEnabled: Boolean = portcallSyncProperties.enable
    override val allowOtherSourcePortcallUpdate = portcallSyncProperties.allowOtherSourcePortcallUpdate
    private val okhttp = okhttp3.OkHttpClient()

    override fun getServiceModels(taskType: ScheduledTaskType): Collection<Pair<String, ServiceModel>> {
        if (taskType == ScheduledTaskType.PORTCALL_SYNC) {
            val fiveHoursAgo = fetchTimestamp(Date(), -5, TimeUnit.HOURS)
            val url = "${portcallSyncProperties.url}?from=${fetchTimestamp(fiveHoursAgo, TIMESTAMP_FORMAT)}"
            val credentials = Credentials.basic(portcallSyncProperties.username, portcallSyncProperties.password)
            val headers = mapOf("Authorization" to credentials)
            val response = okhttp.executeGetCall(url, emptyMap(), headers)
            val body = response.body?.string()
            val portcalls: List<Portcall>? = when {
                body.isNullOrEmpty() -> null
                response.code == HttpStatus.OK.value() -> mapper.readValue(body)
                response.code == HttpStatus.NO_CONTENT.value() -> emptyList()
                else -> null
            }
            if (portcalls == null) {
                log(message = "Issue with connecting to Portcall sync service ($url). Received status code: ${response.code} with reason: $body")
            }
            // for all the ships detected by AIS and qualified to be updated with NxtPort, make an async call
            val portsEnabled = portcallSyncProperties.ports
            return portcalls
                ?.filter { portsEnabled.contains(it.port) }
                ?.map { Pair(it.imo, it) }
                ?: emptyList()
        } else {
            throw PreconditionException("${this::class.java.simpleName} is invoked for unhandled taskType: $taskType")
        }
    }

    /**
     * Convert the given [serviceModel] received from this external service to an internal representation in [Portcall]
     * @param serviceModel The model received from the this external service
     * @param imoNumber The vessel imoNumber for which this data refers to
     * @param taskType The taskType is requesting this action
     */
    override fun convertToPortcall(
        serviceModel: ServiceModel,
        imoNumber: String,
        taskType: ScheduledTaskType,
    ): Portcall {
        if (serviceModel is Portcall) {
            return serviceModel
        } else {
            throw PreconditionException("${this::class.java.simpleName} is invoked for unhandled model: ${serviceModel::class}")
        }
    }
}
