package nl.teqplay.portcallplus.service.external.nxtport

import com.fasterxml.jackson.module.kotlin.readValue
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import nl.teqplay.portcallplus.PreconditionException
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.Portcall.Companion.IDPREFIX_ANR
import nl.teqplay.portcallplus.api.model.PortcallAlias
import nl.teqplay.portcallplus.api.model.PortcallAliasName
import nl.teqplay.portcallplus.api.model.PortcallStatus
import nl.teqplay.portcallplus.api.model.PortcallVisit
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.ServiceModel
import nl.teqplay.portcallplus.api.model.UpdateType
import nl.teqplay.portcallplus.datasource.ActivityDataSource
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.datasource.ScheduledTaskRunDataSource
import nl.teqplay.portcallplus.datasource.ServiceFetchCounterDataSource
import nl.teqplay.portcallplus.model.ScheduledTaskTypeFieldsProxy
import nl.teqplay.portcallplus.model.service.MTurkHIT
import nl.teqplay.portcallplus.model.service.PortcallUpdateResult
import nl.teqplay.portcallplus.model.service.StayV2
import nl.teqplay.portcallplus.properties.MTurkProperties
import nl.teqplay.portcallplus.properties.NxtPortV2Properties
import nl.teqplay.portcallplus.service.common.AuthToken
import nl.teqplay.portcallplus.service.common.EventService
import nl.teqplay.portcallplus.service.common.TaskService
import nl.teqplay.portcallplus.service.external.MTurkService
import nl.teqplay.portcallplus.service.internal.PoMaService
import nl.teqplay.portcallplus.service.internal.UserService
import nl.teqplay.portcallplus.utils.executeGetCall
import nl.teqplay.portcallplus.utils.executePostCallAsForm
import nl.teqplay.portcallplus.utils.fetchTimestamp
import nl.teqplay.portcallplus.utils.fetchTimestampOrNull
import nl.teqplay.portcallplus.utils.getProperBerthName
import nl.teqplay.portcallplus.utils.isValid
import nl.teqplay.portcallplus.utils.log
import nl.teqplay.portcallplus.utils.mapper
import nl.teqplay.portcallplus.utils.removeSpecialChars
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import java.util.Date
import java.util.concurrent.TimeUnit
import java.util.concurrent.TimeUnit.HOURS

/**
 * Service class housing all the logic and scheduling mechanism for updating [Portcall] based on NxtPort's VesselStay data
 */
@Service
class VesselStayServiceV2(
    private val scheduledTaskRunDataSource: ScheduledTaskRunDataSource,
    private val portcallDataSource: PortcallDataSource,
    private val mTurkService: MTurkService,
    activityDataSource: ActivityDataSource,
    serviceFetchCounterDataSource: ServiceFetchCounterDataSource,
    private val nxtPortV2Properties: NxtPortV2Properties,
    private val mTurkProperties: MTurkProperties,
    private val scheduledTaskTypeFieldsProxy: ScheduledTaskTypeFieldsProxy,
    private val userService: UserService,
    pomaService: PoMaService,
    eventService: EventService,
    private val portOfAntwerpBrugesService: PortOfAntwerpBrugesService,
) : TaskService(
    scheduledTaskRunDataSource,
    activityDataSource,
    serviceFetchCounterDataSource,
    portcallDataSource,
    scheduledTaskTypeFieldsProxy,
    userService,
    pomaService,
    eventService,
) {
    override val portcallAliasName = PortcallAliasName.NXTPORT

    override fun getTasks(): List<ScheduledTaskType> = listOf(ScheduledTaskType.NXTPORT_V2)

    override val isEnabled: Boolean = nxtPortV2Properties.enable
    override val allowOtherSourcePortcallUpdate: Boolean = nxtPortV2Properties.allowOtherSourcePortcallUpdate

    private lateinit var authToken: AuthToken
    private val loginUrl = nxtPortV2Properties.loginUrl
    private val nextPortUrl = nxtPortV2Properties.url
    private val subscriptionKey = nxtPortV2Properties.subscriptionKey
    private val okhttp = okhttp3.OkHttpClient()

    private val loginData = mapOf(
        "username" to nxtPortV2Properties.userName,
        "password" to nxtPortV2Properties.password,
        "client_id" to nxtPortV2Properties.clientId,
        "client_secret" to nxtPortV2Properties.clientSecret,
        "grant_type" to "password",
    )
    private val disposeAfterExpiring = mTurkProperties.disposeAfterExpiring

    val NEW_PORTCALL_INTERVAL = TimeUnit.DAYS.toMillis(nxtPortV2Properties.newPortcallIntervalInDays)

    override fun getServiceModels(taskType: ScheduledTaskType): Collection<Pair<String, ServiceModel>> {
        if (taskType.isVesselStayTask()) {
            val date = scheduledTaskRunDataSource.get(taskType)?.lastRun ?: fetchTimestamp(Date(), -5, HOURS)
            val stays = fetchStays(date)
            return stays.mapNotNull { stay ->
                stay.imo?.let { it to stay }
            }
        } else {
            throw PreconditionException("${this::class.java.simpleName} is invoked for unhandled taskType: $taskType")
        }
    }

    /**
     * Convert the given [serviceModel] received from this external service to an internal representation in [Portcall]
     * @param serviceModel The model received from the this external service
     * @param imoNumber The vessel imoNumber for which this data refers to
     * @param taskType The taskType is requesting this action
     */
    override fun convertToPortcall(
        serviceModel: ServiceModel,
        imoNumber: String,
        taskType: ScheduledTaskType,
    ): Portcall {
        if (serviceModel is StayV2 && serviceModel.stayNumber != null) {
            // eta is not expected to be null. Erroneous Stay's like this must be filtered out before this
            val startTime = fetchTimestamp(serviceModel.eta!!)
            val existingPortcall = portcallDataSource.searchByAlias(serviceModel.stayNumber, portcallAliasName)
                .firstOrNull()
                ?: portcallDataSource.getNearestByImoAndDateInterval(imoNumber, IDPREFIX_ANR, startTime, NEW_PORTCALL_INTERVAL)?.let {
                    log(it, "Nearest portcall found since no alias found with stay ${serviceModel.stayNumber}")
                    it
                }

            val visits = mutableListOf<PortcallVisit>()
            // add a visit if a berth ata is found in the stay
            if (serviceModel.berthArrival != null) {
                visits.add(
                    PortcallVisit(
                        berthEta = fetchTimestampOrNull(serviceModel.eta),
                        berthName = serviceModel.berthArrival.getProperBerthName(),
                        visitType = UpdateType.BERTH,
                    ),
                )
            }
            // if a berth atd is found, append the berth atd time as the endDate
            if (serviceModel.berthDeparture != null) {
                // if departure is from a berth different from the initial arrival berth, add a visit to reflect shifting
                if (serviceModel.berthArrival != serviceModel.berthDeparture) {
                    // no eta available, so should be later
                    visits.add(
                        // trigger a nomination visitType in PC+. Platform should ideally translate this to a
                        // berthvisit.add event
                        PortcallVisit(
                            berthName = serviceModel.berthDeparture.getProperBerthName(),
                            visitType = UpdateType.NOMINATION,
                        ),
                    )
                }
            }

            val currentStatus = existingPortcall?.status ?: PortcallStatus.INBOUND
            val vesselAgentsByFormattedKey = portcallDataSource.getVesselAgentsByFormattedKey()
            val vesselAgent = fetchVesselAgent(imoNumber, serviceModel.stayNumber, vesselAgentsByFormattedKey) ?: existingPortcall?.vesselAgent
            val updatedPortcall: Portcall = existingPortcall?.getUpdatedPortcall(
                source = taskType,
                status = currentStatus,
                newVisits = visits,
                startTimeType = UpdateType.BERTH,
                portcallAlias = setOf(PortcallAlias(portcallAliasName, serviceModel.stayNumber)),
                vesselAgent = vesselAgent,
            ) ?: Portcall(
                portcallId = portcallDataSource.generateId(IDPREFIX_ANR, imoNumber, startTime),
                status = currentStatus,
                portcallAlias = setOf(PortcallAlias(portcallAliasName, serviceModel.stayNumber)),
                port = IDPREFIX_ANR,
                imo = imoNumber,
                source = taskType,
                startTime = startTime,
                startTimeType = UpdateType.BERTH,
                originUnlocode = serviceModel.origin,
                destinationUnlocode = serviceModel.destination,
                // we update visits as is with NxtPort.
                visits = visits,
                vesselAgent = vesselAgent,
            )

            if (mTurkService.isMTurkEnabled()) {
                handleMTurk(
                    scrapedVesselAgent = vesselAgent,
                    imo = updatedPortcall.imo,
                    alias = serviceModel.stayNumber,
                    portcallId = updatedPortcall.portcallId,
                    vesselAgentsByFormattedKey,
                )
            }
            return updatedPortcall
        } else {
            throw PreconditionException("${this::class.java.simpleName} is invoked for unhandled model: ${serviceModel::class}")
        }
    }

    /**
     * This method will check if the vesselAgent has been provided and possibly create a HIT
     * If vesselAgent is provided and the hit check exists, then it will expire and possibly dispose the HIT
     * if vesselAgent is not provided, then create a new HIT if HIT does not exist
     */
    internal fun handleMTurk(
        scrapedVesselAgent: String?,
        imo: String,
        alias: String,
        portcallId: String,
        vesselAgentsByFormattedKey: Map<String, String>,
    ) {
        val existingHIT = mTurkService.getHITByImoAndPortcallAlias(imo, alias)
        if (scrapedVesselAgent == null) {
            if (existingHIT == null) {
                CoroutineScope(Job() + Dispatchers.IO).launch {
                    log(portcallId = portcallId, message = "Handling vesselAgent update via MTurk for imo/alias $imo/$alias")
                    mTurkService.handleHITRequest(MTurkHIT(imo, alias))
                }
            }
        } else {
            if (existingHIT != null && !existingHIT.expired) {
                log(portcallId = portcallId, message = "Expiring hit for imo/alias $imo/$alias")
                val updatedHIT = mTurkService.processAssignments(existingHIT, vesselAgentsByFormattedKey)
                    .first.copy(expired = true)
                mTurkService.expireHIT(updatedHIT.hitId)
                if (disposeAfterExpiring) {
                    log(portcallId = portcallId, message = "Disposing after expiring hit for imo/alias $imo/$alias")
                    mTurkService.deleteHIT(updatedHIT.hitId)
                }
                mTurkService.updateHitDetails(updatedHIT)
            }
        }
    }

    // scrape the response from the external website and check with the provided stayNumber which agentName is provided in the response
    fun fetchVesselAgent(
        imoNumber: String,
        stayNumber: String,
        vesselAgentsByFormattedKey: Map<String, String>? = null,
    ): String? {
        // Fetch if possible, the vesselAgent by scraping an endpoint and checking if the agent is found based on the stayNumber
        // log(message = "Scraping from $portOfAntwerpBrugesUrl with imo $imoNumber ")
        // Just a remark that even though it's a variable in the service, we will ALWAYS query this shipType (until we need a different type).
        val shipType = "seaship"
        val vesselAgentsByFormattedKey = vesselAgentsByFormattedKey ?: portcallDataSource.getVesselAgentsByFormattedKey()
        val vesselAgent = portOfAntwerpBrugesService.getStay(shipType, imoNumber)?.let { responseObject ->
            responseObject.stayDetail?.find { stayDetail ->
                stayDetail.stayId == stayNumber
            }?.agentName?.let { agentName ->
                // if no agent is found in the mapping, we still want the original agent in the portcall
                vesselAgentsByFormattedKey[removeSpecialChars(agentName)] ?: agentName.also {
                    log.info { "Did not find an existing vesselAgent, non agent ($agentName) is found and returned" }
                }
            }
        }
        return vesselAgent
    }

    override fun updatePortcall(
        serviceModels: Collection<Pair<String, ServiceModel>>,
        taskType: ScheduledTaskType,
    ): List<PortcallUpdateResult> {
        return super.updatePortcall(
            serviceModels = serviceModels.mapNotNull { (imo, stay) ->
                if (stay is StayV2 && stay.eta != null && stay.stayNumber != null) {
                    imo to stay
                } else {
                    null
                }
            },
            taskType = taskType,
        )
    }

    private fun fetchStays(date: Date): List<StayV2> {
        val url = "$nextPortUrl?date=${fetchTimestamp(date)}"
        val headers = mapOf(
            "Authorization" to "Bearer ${getToken()}",
            "Ocp-Apim-Subscription-Key" to subscriptionKey,
        )
        val response = okhttp.executeGetCall(url, emptyMap(), headers)
        val body = response.body?.string()
        val stays = when {
            body.isNullOrBlank() || response.code != HttpStatus.OK.value() -> null
            else -> mapper.readValue<List<StayV2>>(body)
        }
        if (stays == null) {
            val message = "Issue with connecting to NxtPort vesselStay service. ($nextPortUrl) Received status code: " +
                "${response.code} with the body: $body"
            log(message = message)
            throw PreconditionException(message)
        }
        /* Just for logging purposes. */
        stays.logInvalidImoStays()
        return stays
    }

    /**
     *  Just for logging purposes.
     */
    private fun List<StayV2>.logInvalidImoStays() {
        val invalidImosStays = this
            .filter { !it.imo.validImo() }
            .map { it.stayNumber }
        if (invalidImosStays.isNotEmpty()) {
            log(message = "The following ${invalidImosStays.size} stay(s) didn't have a valid imo: ${invalidImosStays.joinToString(", ")}")
        }
    }

    private fun String?.validImo() = (this != null && this.matches(Regex("^[0-9]{7}$")))

    private fun getToken(): String {
        if (!this::authToken.isInitialized || !this.authToken.isValid()) {
            this.authToken = login()
        }
        return this.authToken.token
    }

    private fun login(): AuthToken {
        log(message = "Logging in to NxtPort.. ")
        val response = okhttp.executePostCallAsForm(loginUrl, loginData)
        val body = response.body?.string()
        val authToken = when {
            body.isNullOrBlank() || response.code != HttpStatus.OK.value() -> null
            else -> mapper.readValue<AuthToken>(body).copy(createdAt = Date())
        }
        if (authToken == null) {
            val message = "Issue with connecting to NxtPort vesselStay service. ($nextPortUrl) Received status code: " +
                "${response.code} with reason: $body"
            log(message = message)
            throw PreconditionException(message)
        }
        return authToken
    }
}
