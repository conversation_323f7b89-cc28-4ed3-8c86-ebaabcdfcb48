package nl.teqplay.portcallplus.service.external

import nl.teqplay.portcallplus.PreconditionException
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallAliasName
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.ServiceModel
import nl.teqplay.portcallplus.api.model.UpdateType
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.model.service.PortcallGeneratorModel
import nl.teqplay.portcallplus.model.service.PortcallUpdateResult
import nl.teqplay.portcallplus.service.common.EventService
import nl.teqplay.portcallplus.service.common.SourceService
import nl.teqplay.portcallplus.service.internal.PLATFORM_PORTS
import nl.teqplay.portcallplus.service.internal.PlatformService
import nl.teqplay.portcallplus.service.internal.PoMaService
import nl.teqplay.portcallplus.service.internal.UserService
import nl.teqplay.portcallplus.utils.convertPlatformPortcall
import nl.teqplay.portcallplus.utils.fetchTimestamp
import nl.teqplay.portcallplus.utils.isTimeInInterval
import nl.teqplay.portcallplus.utils.log
import nl.teqplay.portcallplus.utils.setPortcallAlias
import nl.teqplay.skeleton.common.exception.BadGatewayException
import nl.teqplay.skeleton.common.exception.ConflictException
import nl.teqplay.skeleton.common.exception.NotFoundException
import org.springframework.context.annotation.Lazy
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.web.client.HttpClientErrorException
import java.util.Date
import java.util.concurrent.TimeUnit
import kotlin.math.abs

/**
 * The logic to use when the an external application requests to read or create a portcall. E.g. Port Publisher
 */
@Service
class PortcallGeneratorService(
    @Lazy private val platformService: PlatformService,
    private val portcallDataSource: PortcallDataSource,
    private val userService: UserService,
    pomaService: PoMaService,
    eventService: EventService,
) : SourceService(portcallDataSource, pomaService, eventService) {
    // this service's flag is not configurable.
    override val allowOtherSourcePortcallUpdate: Boolean = true

    // the amount of milliseconds to look in the past and the future to match a portcall
    private val portcallInterval = TimeUnit.DAYS.toMillis(10)

    /**
     * Returns an emptyList as no portcalls are fetched from any external endpoint. It is created internally
     * using the [SourceService.updatePortcall]
     */
    override fun getServiceModels(taskType: ScheduledTaskType): Collection<Pair<String, ServiceModel>> = emptyList()

    override fun convertToPortcall(
        serviceModel: ServiceModel,
        imoNumber: String,
    ): Portcall {
        if (serviceModel is PortcallGeneratorModel) {
            if (serviceModel.portcallId != null) {
                portcallDataSource.get(serviceModel.portcallId)?.let { portcall ->
                    return when {
                        portcall.imo != imoNumber -> throw PreconditionException("This portcall id already exists for another imo")
                        portcall.port != serviceModel.port -> throw PreconditionException(
                            "This portcall id already exists for another port",
                        )
                        // if the portcall has already entered the port, do not allow an updated startTime
                        portcall.portAtaTime != null && portcall.portAtdTime == null -> portcall.copy(
                            source = serviceModel.source,
                            startTimeType = UpdateType.NOMINATION,
                        )
                        // if the portcall has not entered/left the port, allow an updated startTime
                        portcall.portAtaTime == null && portcall.portAtdTime == null -> {
                            val startTime = if (portcall.startTime != serviceModel.orderTime) {
                                log(
                                    portcall,
                                    "Updated startTime of ${portcall.portcallId} to ${serviceModel.orderTime} by ${userService.getCurrentUsername()} ",
                                )
                                serviceModel.orderTime
                            } else {
                                portcall.startTime
                            }
                            portcall.copy(
                                startTime = startTime,
                                source = serviceModel.source,
                                startTimeType = UpdateType.NOMINATION,
                            )
                        }
                        else -> createNewPortcall(serviceModel, portcallAliasName)
                    }
                }
            }

            getNearestPortcall(serviceModel)?.let { portcall ->
                val startTime = if (portcall.startTime != serviceModel.orderTime) {
                    log(
                        portcall,
                        "Updated startTime of ${portcall.portcallId} to ${serviceModel.orderTime} by ${userService.getCurrentUsername()} ",
                    )
                    serviceModel.orderTime
                } else {
                    portcall.startTime
                }
                return portcall.copy(
                    startTime = startTime,
                    source = serviceModel.source,
                    startTimeType = UpdateType.NOMINATION,
                )
            }

            fetchNearestFromPlatform(serviceModel)?.let {
                if (isTimeInInterval(
                        Date(it.startTime),
                        Date(serviceModel.orderTime.time - portcallInterval),
                        Date(serviceModel.orderTime.time + portcallInterval),
                    )
                ) {
                    return convertPlatformPortcall(it, serviceModel.source)
                }
            }
            return createNewPortcall(serviceModel, portcallAliasName, serviceModel.portcallId)
        }
        throw PreconditionException("${this::class.simpleName} is invoked for unhandled model: ${serviceModel::class}")
    }

    /**
     * Proxy call to convert the given [generatorModel] to a [Portcall] and save it in the db
     */
    fun updatePortcall(generatorModel: PortcallGeneratorModel): List<PortcallUpdateResult> =
        super.updatePortcall(listOf(generatorModel.shipImo to generatorModel))

    /**
     * Get the portcalls that are most likely requested
     */
    fun getNominations(serviceModel: PortcallGeneratorModel): List<Portcall> {
        val portcalls = mutableListOf<Portcall>()
        if (serviceModel.portcallId != null) {
            ( // Try to fetch the portcall from the database
                portcallDataSource.get(serviceModel.portcallId)
                    // Else fetch the portcall from platform
                    ?: fetchFromPlatformById(serviceModel)
                )?.let { platformPortcall ->
                portcalls.add(platformPortcall)
            }
        }
        // Try to get the nearest portcall
        getPossiblePortcalls(serviceModel).forEach { foundPortcall ->
            if (portcalls.none { it.portcallId == foundPortcall.portcallId }) {
                portcalls.add(foundPortcall)
            }
        }
        // Try to get the nearest portcall from platform if it exists
        fetchNearestFromPlatform(serviceModel)?.let { nearestPlatformPortcall ->
            if (isTimeInInterval(
                    Date(nearestPlatformPortcall.startTime),
                    Date(serviceModel.orderTime.time - portcallInterval),
                    Date(serviceModel.orderTime.time + portcallInterval),
                ) &&
                portcalls.none { it.portcallId == nearestPlatformPortcall.portcallId }
            ) {
                portcalls.add(convertPlatformPortcall(nearestPlatformPortcall, serviceModel.source))
            }
        }
        return portcalls
    }

    /**
     * Try to create a new portcall
     * Give a Conflict error if the portcallId already exists
     */
    fun createNomination(
        serviceModel: PortcallGeneratorModel,
        portcallAlias: PortcallAliasName?,
    ): PortcallUpdateResult {
        if (serviceModel.portcallId != null) {
            portcallDataSource.get(serviceModel.portcallId)?.let { throw ConflictException("Portcall already exists") }
            platformService.getPortcallById(serviceModel.portcallId)?.let { throw ConflictException("Portcall already exists") }
        }
        return updatePortcall(serviceModel).firstOrNull() ?: throw NotFoundException("Portcall not found")
    }

    /**
     * Used after getting a portcall and approving it.
     * It will directly update the portcall without checking it as someone with knowledge already approved it
     */
    fun updateNomination(
        serviceModel: PortcallGeneratorModel,
        portcallAliasName: PortcallAliasName?,
    ): PortcallUpdateResult {
        if (serviceModel.portcallId == null) {
            throw NotFoundException("PortcallId not given")
        }
        return updatePortcall(serviceModel).firstOrNull() ?: throw NotFoundException("Portcall not found")
    }

    private fun fetchFromPlatformById(serviceModel: PortcallGeneratorModel): Portcall? {
        if (serviceModel.port in PLATFORM_PORTS) {
            serviceModel.portcallId?.let { portcallId ->
                try {
                    return platformService.getPortcallById(portcallId)
                        ?.let { convertPlatformPortcall(it, serviceModel.source) }
                } catch (e: HttpClientErrorException) {
                    when (e.statusCode) {
                        HttpStatus.NOT_FOUND -> log(message = "Platform portcall not found")
                        HttpStatus.BAD_GATEWAY -> throw BadGatewayException("Platform is offline")
                        else -> throw e
                    }
                }
            }
        }
        return null
    }

    private fun fetchNearestFromPlatform(serviceModel: PortcallGeneratorModel): nl.teqplay.platform.model.Portcall? {
        if (serviceModel.port in PLATFORM_PORTS) {
            try {
                return platformService.getPortcallsByImo(serviceModel.shipImo, serviceModel.port)
                    .filter { it.startTime != null && (it.finished == null || it.finished == false) }
                    .minByOrNull { abs(it.startTime - serviceModel.orderTime.time) }
            } catch (e: HttpClientErrorException) {
                when (e.statusCode) {
                    HttpStatus.NOT_FOUND -> log(message = "Platform portcall not found")
                    HttpStatus.BAD_GATEWAY -> throw BadGatewayException("Platform is offline")
                    else -> throw e
                }
            }
        }
        return null
    }

    private fun getNearestPortcall(serviceModel: PortcallGeneratorModel): Portcall? {
        return portcallDataSource.getNearestByImoAndDateInterval(
            serviceModel.shipImo,
            serviceModel.port,
            serviceModel.orderTime,
            portcallInterval,
        )
    }

    private fun getPossiblePortcalls(serviceModel: PortcallGeneratorModel): Set<Portcall> {
        return portcallDataSource.getByFiltered(
            serviceModel.shipImo,
            serviceModel.port,
            startTime = fetchTimestamp(serviceModel.orderTime, -portcallInterval, TimeUnit.MILLISECONDS),
            endTime = fetchTimestamp(serviceModel.orderTime, portcallInterval, TimeUnit.MILLISECONDS),
        )
    }

    private fun createNewPortcall(
        serviceModel: PortcallGeneratorModel,
        portcallAliasName: PortcallAliasName?,
        portcallId: String? = null,
    ): Portcall {
        if (serviceModel.orderTime.before(getTimestampFrom())) {
            throw PreconditionException("Not creating portcall as startTime: ${serviceModel.orderTime} is in the past")
        }
        val portcall = portcallId?.let { portcallDataSource.get(portcallId) } ?: Portcall(
            portcallId = portcallId ?: portcallDataSource.generateId(
                serviceModel.port, serviceModel.shipImo, serviceModel.orderTime,
            ),
            portcallAlias = emptySet(),
            port = serviceModel.port,
            imo = serviceModel.shipImo,
            source = serviceModel.source,
            startTime = serviceModel.orderTime,
        )
        return portcall.copy(
            portcallAlias = setPortcallAlias(portcall, portcallAliasName, serviceModel.portcallId),
            startTimeType = UpdateType.NOMINATION,
        )
    }
}
