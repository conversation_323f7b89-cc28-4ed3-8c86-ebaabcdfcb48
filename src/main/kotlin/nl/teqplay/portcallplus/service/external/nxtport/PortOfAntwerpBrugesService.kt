package nl.teqplay.portcallplus.service.external.nxtport

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.portcallplus.config.annotations.AntwerpRestTemplate
import nl.teqplay.portcallplus.properties.NxtPortV2Properties
import nl.teqplay.portcallplus.service.common.PortOfAntwerpBrugesResponse
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.getForObject

@Service
class PortOfAntwerpBrugesService(
    @AntwerpRestTemplate private val restTemplate: RestTemplate,
    private val config: NxtPortV2Properties,
) {
    private val log = KotlinLogging.logger { this.javaClass.simpleName }

    private val stayNumberUrl = config.portOfAntwerp.stayNumberUrl

    /**
     * Method named as '[getStay] because of historical reasons:
     * It seems to return a very similar NxtPort (single) stay information, but from indirectly via PortOfAntwerpBruges site.
     */
    fun getStay(
        shipType: String,
        imo: String,
    ): PortOfAntwerpBrugesResponse? {
        return runCatching {
            restTemplate.getForObject<PortOfAntwerpBrugesResponse>(
                "$stayNumberUrl?shipType={shipType}&number={number}",
                mapOf(
                    "shipType" to shipType,
                    "number" to imo,
                ),
            )
        }.onFailure {
            log.error { "Error requesting stay info for IMO $imo: ${it.message}" }
        }.onSuccess {
            log.debug { "Successfully obtained stay info for IMO $imo: $it" }
        }.getOrNull()
    }
}
