package nl.teqplay.portcallplus.service.external

import nl.teqplay.portcallplus.PreconditionException
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallStatus
import nl.teqplay.portcallplus.api.model.PortcallVisit
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.ENIGMA_SCRAPER_INCOMING
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.ENIGMA_SCRAPER_OUTGOING
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.LIS_SCRAPPER
import nl.teqplay.portcallplus.api.model.ServiceModel
import nl.teqplay.portcallplus.api.model.UpdateType
import nl.teqplay.portcallplus.datasource.ActivityDataSource
import nl.teqplay.portcallplus.datasource.EnigmaDataSource
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.datasource.ScheduledTaskRunDataSource
import nl.teqplay.portcallplus.datasource.ServiceFetchCounterDataSource
import nl.teqplay.portcallplus.model.ScheduledTaskTypeFieldsProxy
import nl.teqplay.portcallplus.model.data.enigma.EnigmaTravels
import nl.teqplay.portcallplus.model.service.EnigmaTravel
import nl.teqplay.portcallplus.properties.EnigmaProperties
import nl.teqplay.portcallplus.service.common.EventService
import nl.teqplay.portcallplus.service.common.TaskService
import nl.teqplay.portcallplus.service.internal.PoMaService
import nl.teqplay.portcallplus.service.internal.UserService
import nl.teqplay.portcallplus.utils.NL_TIMEZONE
import nl.teqplay.portcallplus.utils.fetchTimestamp
import nl.teqplay.portcallplus.utils.getScheldePort
import nl.teqplay.portcallplus.utils.log
import nl.teqplay.portcallplus.utils.removeBraces
import nl.teqplay.skeleton.common.exception.NotFoundException
import org.jsoup.Jsoup
import org.jsoup.nodes.Document
import org.springframework.stereotype.Service
import java.util.Date
import java.util.TimeZone
import java.util.concurrent.TimeUnit

const val ENIGMA_DATE_FORMAT = "dd-MM-yyyy HH:mm"
val ENIGMA_TIMEZONE: TimeZone = NL_TIMEZONE
private const val incomingUrl = "https://www.northseaport.com/enigma/detail/19/verwachte-opvaarten"
private const val outgoingUrl = "https://www.northseaport.com/enigma/detail/21/verwachte-afvaarten"

@Service
class EnigmaScraperService(
    private val dataSource: EnigmaDataSource,
    scheduledTaskRunDataSource: ScheduledTaskRunDataSource,
    activityDataSource: ActivityDataSource,
    serviceFetchCounterDataSource: ServiceFetchCounterDataSource,
    private val portcallDataSource: PortcallDataSource,
    private val enigmaProperties: EnigmaProperties,
    private val scheduledTaskTypeFieldsProxy: ScheduledTaskTypeFieldsProxy,
    private val userService: UserService,
    pomaService: PoMaService,
    eventService: EventService,
) : TaskService(
    scheduledTaskRunDataSource,
    activityDataSource,
    serviceFetchCounterDataSource,
    portcallDataSource,
    scheduledTaskTypeFieldsProxy,
    userService,
    pomaService,
    eventService,
) {
    val NEW_PORTCALL_INTERVAL = TimeUnit.DAYS.toMillis(enigmaProperties.newPortcallInterval)

    override fun getTasks(): List<ScheduledTaskType> = listOf(ENIGMA_SCRAPER_INCOMING, ENIGMA_SCRAPER_OUTGOING)

    override val isEnabled: Boolean = enigmaProperties.enable
    override val allowOtherSourcePortcallUpdate: Boolean = enigmaProperties.allowOtherSourcePortcallUpdate

    override fun getServiceModels(taskType: ScheduledTaskType): Collection<Pair<String, ServiceModel>> {
        return when (taskType) {
            ENIGMA_SCRAPER_INCOMING -> {
                val incomingVessels = getIncomingVessels()
                incomingVessels.map { Pair(it.shipImo, it) }
            }
            ENIGMA_SCRAPER_OUTGOING -> {
                val outgoingVessels = getOutgoingVessels()
                outgoingVessels.map { Pair(it.shipImo, it) }
            }
            else -> throw PreconditionException("${this::class.java.simpleName} is invoked for unhandled taskType: $taskType")
        }
    }

    /**
     * Convert the given [serviceModel] received from this external service to an internal representation in [Portcall]
     * @param serviceModel The model received from the this external service
     * @param imoNumber The vessel imoNumber for which this data refers to
     * @param taskType The taskType is requesting this action
     */
    override fun convertToPortcall(
        serviceModel: ServiceModel,
        imoNumber: String,
        taskType: ScheduledTaskType,
    ): Portcall {
        if (serviceModel is EnigmaTravel) {
            val portcallInterval = NEW_PORTCALL_INTERVAL
            return when (taskType) {
                ENIGMA_SCRAPER_INCOMING -> convertToIncomingPortcall(serviceModel, portcallInterval)
                ENIGMA_SCRAPER_OUTGOING -> convertToOutgoingPortcall(serviceModel)
                else -> throw PreconditionException("EnigmaScraperService is invoked for unhandled taskType: $taskType")
            }.copy(source = taskType)
        } else {
            throw PreconditionException("${this::class.java.simpleName} is invoked for unhandled model: ${serviceModel::class}")
        }
    }

    /**
     * Convert the given incoming [enigmaTravel] to a [Portcall] instance
     * @param enigmaTravel The incoming travel
     * @param portcallInterval The interval in millis for which an existing (if any) portcall must be matched
     */
    private fun convertToIncomingPortcall(
        enigmaTravel: EnigmaTravel,
        portcallInterval: Long,
    ): Portcall {
        val eventTime = fetchTimestamp(enigmaTravel.orderTime, ENIGMA_DATE_FORMAT, ENIGMA_TIMEZONE)

        val yearAgo = fetchTimestamp(Date(), -365, TimeUnit.DAYS)
        val lastOpenPortcall = portcallDataSource.getNearestByImoAndDate(
            enigmaTravel.shipImo,
            enigmaTravel.port,
            eventTime,
            yearAgo,
            eventTime,
        )

        // if a previous portcall is open (without an atd), so it might be shifting
        val existingPortcall = if (lastOpenPortcall != null &&
            lastOpenPortcall.startTime.before(Date()) &&
            lastOpenPortcall.portAtaTime != null &&
            lastOpenPortcall.portAtdTime == null
        ) {
            // ignore the enigmaTravel object if the ship is in the port and receives an enigmaTravel with a matching berthName
            val visit = lastOpenPortcall.visits.firstOrNull { it.berthName == enigmaTravel.berth }
            if (isExpectedToBeInThisVisit(lastOpenPortcall, visit, eventTime)) {
                val message = "Portcall of ${enigmaTravel.shipImo} already has a visit with " +
                    "${enigmaTravel.berth} in it, postponing adding the visit till ${enigmaTravel.shipName} " +
                    "leaves ${enigmaTravel.port}"
                throw PreconditionException(message, lastOpenPortcall.portcallId)
            }
            lastOpenPortcall
        } else {
            portcallDataSource.getNearestByImoAndDateInterval(
                enigmaTravel.shipImo,
                enigmaTravel.port,
                eventTime,
                portcallInterval,
            )
        }
        // The Lis data is more accurate than Enigma so whenever it has been updated by lis last, ignore the update
        if (existingPortcall != null && existingPortcall.source == LIS_SCRAPPER) {
            throw PreconditionException(
                "EnigmaScraperService can't update berthEta of ${enigmaTravel.berth} to " +
                    "${enigmaTravel.orderTime} because it has been updated last by Lis Scraper",
            )
        }

        // prepare the visit based on enigma
        val newVisit = PortcallVisit(
            berthEta = eventTime,
            berthName = enigmaTravel.berth,
            visitType = UpdateType.BERTH,
        )
        return existingPortcall?.getUpdatedPortcall(
            newVisits = listOf(newVisit),
            startTimeType = UpdateType.BERTH,
            vesselAgent = existingPortcall.vesselAgent ?: enigmaTravel.submitter,
        ) ?: Portcall(
            portcallId = portcallDataSource.generateId(enigmaTravel.port, enigmaTravel.shipImo, eventTime),
            portcallAlias = emptySet(),
            port = enigmaTravel.port,
            imo = enigmaTravel.shipImo,
            source = ENIGMA_SCRAPER_INCOMING,
            startTime = eventTime,
            startTimeType = UpdateType.BERTH,
            status = PortcallStatus.INBOUND,
            vesselAgent = enigmaTravel.submitter,
            destinationUnlocode = enigmaTravel.port,
            visits = listOf(newVisit),
        )
    }

    /**
     * [eventTime] of the new enigmaTravel object
     * Check if the [eventTime] is expected to be in this [visit]
     * to do this we check if it is between [visit] eta and the next visit eta or
     * [visit] eta and the etd
     */
    private fun isExpectedToBeInThisVisit(
        portcall: Portcall,
        visit: PortcallVisit?,
        eventTime: Date,
    ): Boolean {
        if (visit?.berthEta == null) {
            return false
        }
        if (visit.berthEtd == null) {
            val indexOfVisit: Int = portcall.visits.indexOf(visit)
            val nextVisit = if (portcall.visits.size > indexOfVisit + 1) portcall.visits[indexOfVisit + 1] else null
            if (nextVisit?.berthEta == null) {
                return eventTime.after(visit.berthEta)
            }
            return eventTime.after(visit.berthEta) && eventTime.before(nextVisit.berthEta)
        }
        return eventTime.after(visit.berthEta) && eventTime.before(visit.berthEtd)
    }

    /**
     * Convert the given outgoing [enigmaTravel] to a [Portcall] instance
     * @param enigmaTravel The outgoing travel
     */
    private fun convertToOutgoingPortcall(enigmaTravel: EnigmaTravel): Portcall {
        val eventTime = fetchTimestamp(enigmaTravel.orderTime, ENIGMA_DATE_FORMAT, ENIGMA_TIMEZONE)
        val resultPortcall = portcallDataSource.getNearestByImoAndDate(
            enigmaTravel.shipImo,
            enigmaTravel.port,
            eventTime,
            fetchTimestamp(Date(), addTime = -365, unit = TimeUnit.DAYS),
            eventTime,
        )
        // update the etd only if it belongs to the existing portcall
        if (resultPortcall != null && resultPortcall.etd != eventTime) {
            log(resultPortcall, message = "Appending etd: $eventTime to portcall")
            // update the agent if missing
            return resultPortcall.getUpdatedPortcall(
                newVisits = listOf(PortcallVisit(berthEtd = eventTime, berthName = enigmaTravel.berth)),
                vesselAgent = resultPortcall.vesselAgent ?: enigmaTravel.submitter,
            )
        } else {
            throw PreconditionException(
                "No portcall or no change in eventTime. Ignoring etd ${enigmaTravel.orderTime}",
                enigmaTravel.shipImo,
            )
        }
    }

    private fun getIncomingVessels(): List<EnigmaTravel> {
        return getData(incomingUrl, true)
    }

    private fun getOutgoingVessels(): List<EnigmaTravel> {
        return getData(outgoingUrl, false)
    }

    private fun getData(
        url: String,
        inbound: Boolean,
    ): List<EnigmaTravel> {
        val doc = Jsoup.connect(url).get()
        val selectedShips = fetchTravels(doc, inbound)
        log(
            message = "Number of updates for inbound=$inbound: ${selectedShips.size}. Imos: ${selectedShips.map { it.shipImo }.removeBraces()}",
        )
        // Save current run in the database
        val type = if (inbound) {
            PortcallStatus.INBOUND
        } else {
            PortcallStatus.OUTBOUND
        }
        dataSource.createOrUpdate(EnigmaTravels(type, selectedShips))
        return selectedShips
    }

    /**
     * Fetch the EnigmaTravel objects from the site
     */
    fun fetchTravels(
        doc: Document,
        inbound: Boolean,
    ): List<EnigmaTravel> {
        val selectedShips = ArrayList<EnigmaTravel>()
        for (table in doc.select("#enigmadata")) {
            val tBody = table.selectFirst("tbody") ?: throw NotFoundException("Element tbody wasn't found.")
            log(message = "Amount of rows: " + tBody.select("tr").count())
            for (row in tBody.select("tr")) {
                val tds = row.select("td")
                val shipImo = tds.select(".schipnr").text().substring(1)
                var fromHarbor: String? = null
                var toHarbor: String? = null
                val port: String
                val destination = tds.select(".aanloophaven").text()
                if (inbound) {
                    toHarbor = getScheldePort(destination)
                    port = toHarbor ?: destination
                } else {
                    fromHarbor = getScheldePort(destination)
                    port = fromHarbor ?: destination
                }
                val orderTime = tds.select(".tijdstip").text()
                val shipName = tds.select(".schip").text()
                val berth = tds.select(".ligplaats").text()
                val submitter = tds.select(".inschrijver").text()
                if (orderTime.isNotEmpty() && shipImo.isNotEmpty() && port.isNotEmpty()) {
                    val travel = EnigmaTravel(fromHarbor, toHarbor, shipName, shipImo, orderTime, berth, submitter, port)
                    selectedShips.add(travel)
                }
            }
        }
        return selectedShips
    }
}
