package nl.teqplay.portcallplus.service.external

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.poma.api.v1.Berth
import nl.teqplay.portcallplus.PreconditionException
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.Portcall.Companion.IDPREFIX_USCRP
import nl.teqplay.portcallplus.api.model.PortcallAlias
import nl.teqplay.portcallplus.api.model.PortcallPurpose
import nl.teqplay.portcallplus.api.model.PortcallStatus
import nl.teqplay.portcallplus.api.model.PortcallVisit
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.ServiceModel
import nl.teqplay.portcallplus.api.model.UpdateType
import nl.teqplay.portcallplus.api.model.UpdateType.BERTH
import nl.teqplay.portcallplus.api.model.UpdateType.NOMINATION
import nl.teqplay.portcallplus.config.annotations.CorpusChristiS3BucketClient
import nl.teqplay.portcallplus.datasource.ActivityDataSource
import nl.teqplay.portcallplus.datasource.CorpusChristiProcessedFilesDataSource
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.datasource.ScheduledTaskRunDataSource
import nl.teqplay.portcallplus.datasource.ServiceFetchCounterDataSource
import nl.teqplay.portcallplus.model.ScheduledTaskTypeFieldsProxy
import nl.teqplay.portcallplus.model.data.corpuschristi.FileParsingResult
import nl.teqplay.portcallplus.model.data.corpuschristi.FileParsingSummary
import nl.teqplay.portcallplus.model.data.corpuschristi.Movement
import nl.teqplay.portcallplus.model.data.corpuschristi.RawMovement
import nl.teqplay.portcallplus.properties.CorpusChristiProperties
import nl.teqplay.portcallplus.service.common.EventService
import nl.teqplay.portcallplus.service.common.S3BucketClient
import nl.teqplay.portcallplus.service.common.TaskService
import nl.teqplay.portcallplus.service.internal.PoMaService
import nl.teqplay.portcallplus.service.internal.UserService
import nl.teqplay.portcallplus.utils.READABLE_FORMAT
import nl.teqplay.portcallplus.utils.fetchTimestamp
import nl.teqplay.portcallplus.utils.log
import nl.teqplay.skeleton.common.exception.InternalErrorException
import org.springframework.stereotype.Service
import java.time.Duration
import java.time.Instant
import java.util.Date
import java.util.TimeZone
import java.util.concurrent.TimeUnit
import java.util.logging.Level
import kotlin.reflect.KProperty0
import nl.teqplay.portcallplus.api.model.PortcallAliasName.CORPUS_CHRISTI as CORPUS_CHRISTI_ALIAS
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.CORPUS_CHRISTI as CORPUS_CHRISTI_TASK_TYPE

@Service
class CorpusChristiService(
    @CorpusChristiS3BucketClient
    private val s3BucketClient: S3BucketClient,
    private val processedFilesDataSource: CorpusChristiProcessedFilesDataSource,
    private val corpusChristiProperties: CorpusChristiProperties,
    val mapper: ObjectMapper,
    scheduledTaskRunDataSource: ScheduledTaskRunDataSource,
    activityDataSource: ActivityDataSource,
    serviceFetchCounterDataSource: ServiceFetchCounterDataSource,
    private val portcallDataSource: PortcallDataSource,
    private val scheduledTaskTypeFieldsProxy: ScheduledTaskTypeFieldsProxy,
    private val userService: UserService,
    private val pomaService: PoMaService,
    private val eventService: EventService,
) : TaskService(
    scheduledTaskRunDataSource,
    activityDataSource,
    serviceFetchCounterDataSource,
    portcallDataSource,
    scheduledTaskTypeFieldsProxy,
    userService,
    pomaService,
    eventService,
) {
    private val US_CHICAGO = TimeZone.getTimeZone("America/Chicago")

    // Let's make sure that this value is set, however config[corpusChristi.s3.processedFileMaxAge] instead for the unitTestings.
    private val processedFileMaxAge: Duration = corpusChristiProperties.s3.processedFileMaxAge
    override val isEnabled: Boolean = corpusChristiProperties.enable
    override val allowOtherSourcePortcallUpdate: Boolean = corpusChristiProperties.allowOtherSourcePortcallUpdate
    val allowAgentUpdate: Boolean = corpusChristiProperties.allowAgentUpdate
    val getNearestByImoAndDateRangeDays = corpusChristiProperties.getNearestByImoAndDateRangeDays

    override fun getTasks(): List<ScheduledTaskType> = listOf(CORPUS_CHRISTI_TASK_TYPE)

    fun cleanUpS3Files() {
        val processedFileMaxAge = corpusChristiProperties.s3.processedFileMaxAge
        val now = Instant.now()
        val fileKeysInBucket = s3BucketClient.listObjects()
        processedFilesDataSource.get(fileKeysInBucket)
            .filter { now.isAfter(it.timestamp.plus(processedFileMaxAge)) }
            .forEach { s3BucketClient.delete(it.fileKey) }
    }

    /**
     * Method to process all UnreadFiles
     * Currently not being called because it's a partial feature branch.
     */
    override fun getServiceModels(taskType: ScheduledTaskType): List<Pair<String, Movement>> {
        if (taskType != CORPUS_CHRISTI_TASK_TYPE) {
            throw InternalErrorException(
                "Attempting to request ServiceModels for a task type ($taskType) different than $CORPUS_CHRISTI_TASK_TYPE",
            )
        }
        val filesToProcess = runCatching { getUnreadFileKeys() }
            .onFailure {
                log.error { "Failed to retrieve the unread files. Exception message: ${it.message}." }
            }.onSuccess {
                log.info { "${it.size } new unprocessed files retrieved: ${it.joinToString(", ")}." }
                if (it.isEmpty()) {
                    log.warn { "No new unprocessed files retrieved!" }
                }
            }.getOrThrow()
        return filesToProcess.flatMap { itFileKey ->
            val processTime = Instant.now()
            val processFileResult = processFileKey(itFileKey, processTime)
            processedFilesDataSource.createOrUpdate(processFileResult.toFileParsingSummary(itFileKey, processTime))
            processFileResult.validMovements.map { it.vesselIMO to it }
        }
    }

    override fun convertToPortcall(
        serviceModel: ServiceModel,
        imoNumber: String,
        taskType: ScheduledTaskType,
    ): Portcall {
        if (serviceModel !is Movement) {
            throw PreconditionException("${this::class.java.simpleName} is invoked for unhandled model: ${serviceModel::class}")
        }
        if (serviceModel.isCanceledMovement.equals("TRUE", true)) {
            throw PreconditionException("Movement id ${serviceModel.movementId} is a cancelled movement.")
        }
        if (serviceModel.from_stop_location_code == serviceModel.to_stop_location_code) {
            throw PreconditionException(
                "Movement id ${serviceModel.movementId} is about a ship rotation in berth ${serviceModel.from_stop_location}. Skipping!",
            )
        }
        log.debug { "Converting to Portcall the incoming movement $serviceModel" }
        val existentPortcall = getExistentPortcall(serviceModel)
        // Let's take the serviceModel's updateType only if the existent is the default (NOMINATION) value
        val existentStartTimeTime = existentPortcall?.startTimeType
        val newVisitsInfo = serviceModel.getPortcallVisits()
        val updatedStartTimeType = if (existentStartTimeTime == NOMINATION || existentStartTimeTime == null) {
            newVisitsInfo.getFirstNotNullNotNominationType()
        } else {
            existentPortcall.startTimeType
        }
        val portcall = if (existentPortcall != null) {
            log.debug { "Existent portcall found: $existentPortcall" }
            val updatedPortcall = existentPortcall.getUpdatedPortcall(
                source = taskType,
                status = serviceModel.getStatus(),
                newVisits = newVisitsInfo,
                startTimeType = updatedStartTimeType ?: NOMINATION,
                vesselAgent = serviceModel.movement_agency.takeIf { existentPortcall.vesselAgent.isNullOrBlank() || (allowAgentUpdate) }
                    ?: existentPortcall.vesselAgent,
                purpose = existentPortcall.purpose + serviceModel.getPortcallPurposes(),
                portcallAlias = existentPortcall.portcallAlias + serviceModel.getPortcallAliases(),
                product = serviceModel.product_code ?: existentPortcall.product,
                pilot = serviceModel.pilotNo ?: existentPortcall.pilot,
                // Remember that Movement.tugName actually refers to the Towing Company. See more details in the Movement model.
                towingCompany = serviceModel.tugName ?: existentPortcall.towingCompany,
            )
            // let's update the portATAtime if the serviceModel's new time is before the current one
            val status = serviceModel.getStatus()
            // Portcall ATA correction
            val serviceModelInboundTime = (serviceModel.underwayTime ?: serviceModel.offTime)
                .takeIf { status in listOf(PortcallStatus.INBOUND, PortcallStatus.SHIFTING) }
            val updatedPortAtaTime = if (updatedPortcall.portAtaTime == null || serviceModelInboundTime?.before(updatedPortcall.portAtaTime) == true) {
                serviceModelInboundTime
            } else {
                updatedPortcall.portAtaTime
            }
            // Portcall startTime correction (overriding what SourceService(Portcall).getUpdatedPortcall(...) does.)
            val updatedStartTime = listOf(existentPortcall.startTime, serviceModel.getStartTime()).min()
            updatedPortcall.copy(
                portAtaTime = updatedPortAtaTime,
                startTime = updatedStartTime,
            )
        } else {
            serviceModel.toNewPortcall(newVisitsInfo)
        }
        log.debug { "MovementId ${serviceModel.movementId} converted portcall: $portcall" }
        return portcall
    }

    /**
     * Overriding this method because CorpusChristi portcall visits are identified with arrivalMovementId (from
     * their Tidalis' movements).
     * A berth visit might be changed so we'd have to identify the visit to change by the arrivalMovementId in the
     * portcall visits.
     * When this happens, we should replace the visit.
     *  new visit, if the portcall.source is CORPUS_CHRISTI, then we try to pre-match by the visit.arrivalMovementId.
     * Hence, we should first look for a match based on that arrivalMovementId, and only when not found, fallback
     * to the overridden method (matching by berth&terminal).
     */
    override fun getMatchingVisit(
        visits: List<PortcallVisit>,
        berthId: String?,
        berth: String?,
        terminal: String?,
        refTime: Date?,
        arrivalMovementId: String?,
    ): PortcallVisit? {
        return arrivalMovementId?.let {
            visits.firstOrNull { it.arrivalMovementId == arrivalMovementId }?.also {
                log.debug { "Portcall's visit matched based on arrivalMovementId ${it.arrivalMovementId}!" }
            }
        } ?: super.getMatchingVisit(visits, berthId, berth, terminal, refTime, arrivalMovementId)
    }

    /**
     * Auxiliary method to return the existent portcall given the [serviceModel] in the following search chain:
     * - find by visitNumber alias
     * - find by visitId alias
     * - find by nearestPortcall (IMO and eventTime)
     * - null
     */
    private fun getExistentPortcall(serviceModel: Movement): Portcall? {
        val foundPortcall = findPortcallByAlias(serviceModel.visitNumber)
            ?: findPortcallByAlias(serviceModel.visitId)
            ?: getNearestPortcall(serviceModel)
        if (foundPortcall == null) {
            log.debug { "No existent Portcall found for MovementId ${serviceModel.movementId}." }
        }
        return foundPortcall
    }

    /**
     * Auxiliary method to make [getExistentPortcall] more readable.
     * It returns the portcall found by the given [CORPUS_CHRISTI_ALIAS] [alias] or null
     */
    private fun findPortcallByAlias(alias: String?): Portcall? {
        val portcallByAlias = alias?.let {
            portcallDataSource.searchByAlias(alias, CORPUS_CHRISTI_ALIAS).firstOrNull()
        }
        return portcallByAlias?.also {
            log.info { "Found ${it.portcallId} by alias ($alias)" }
        }
    }

    /**
     * Auxiliary method to make [getExistentPortcall] more readable.
     * It returns the nearest portcall given the [serviceModel] taking the eventTime the first not null of
     * underwayTime, scheduledTime, offTime and dataExportTime.
     * Returns null if not found.
     * Todo: a better eventTime decision will be set when the Movement movel gets consolidated.
     */
    private fun getNearestPortcall(serviceModel: Movement): Portcall? {
        val eventTime = with(serviceModel) { underwayTime ?: scheduledTime ?: offTime ?: dataExportTime }
        val fromDate = fetchTimestamp(eventTime, -getNearestByImoAndDateRangeDays, TimeUnit.DAYS)
        val toDate = fetchTimestamp(eventTime, getNearestByImoAndDateRangeDays, TimeUnit.DAYS)
        val nearestPortcall = portcallDataSource.getNearestByImoAndDate(
            shipImo = serviceModel.vesselIMO,
            port = IDPREFIX_USCRP,
            estimatedTime = eventTime,
            intervalFrom = fromDate,
            intervalTo = toDate,
        )
        return nearestPortcall?.also {
            log.info { "Found ${nearestPortcall.portcallId} by nearest IMO within $fromDate to $toDate" }
        }
    }

    // Auxiliary methods for [convertToPortcall]

    /**
     * Auxiliary method to make [convertToPortcall] more readable.
     * The subject [Movement] represents a Portcall's visit partial (or complete) information.
     * So this would help to update the created or updated portcall.
     * Note that it returns a list of (up to 2) PortcallVisits because a Movement can bring information of
     * - the FROM visit
     * - the TO visit
     */
    private fun Movement.getPortcallVisits(): List<PortcallVisit> {
        // By now, let's focus on berth visits
        val fromVisit = if (this.from_stop_location_code.isBerthVisit()) {
            // It's a DEPARTURE berth-Visit! So data should be taken out of the "from" fields
            val pomaBerth = this.from_stop_location.findPomaBerth()
            PortcallVisit(
                // berthEta
                // berthAta
                berthAtd = this.underwayTime,
                berthEtd = this.scheduledTime,
                terminal = pomaBerth?.terminalName,
                uniqueBerthId = pomaBerth?.uniqueId ?: this.from_stop_location_id,
                berthName = pomaBerth?.name ?: this.from_stop_location,
                // arrivalMovementId
                departureMovementId = this.movementId,
                // connected
                // pilotIncoming
                // pilotOutcoming
                // draughtIncoming
                // draughtOutgoing
                visitType = BERTH,
            ).also {
                log.debug { "Got an outgoing PortcallVisit info from ${this.from_stop_location_code}" }
            }
        } else {
            null
        }
        val toVisit = if (this.to_stop_location_code.isBerthVisit()) {
            // It's an ARRIVAL berth-Visit! So data should be taken out of the "to" fields
            val pomaBerth = this.to_stop_location.findPomaBerth()
            PortcallVisit(
                // berthEta
                berthAta = this.offTime,
                // berthAtd
                // berthEtd
                terminal = pomaBerth?.terminalName,
                uniqueBerthId = pomaBerth?.uniqueId ?: this.to_stop_location_id,
                berthName = pomaBerth?.name ?: this.to_stop_location,
                arrivalMovementId = this.movementId,
                // departureMovementId
                // connected
                // pilotIncoming
                // pilotOutcoming
                // draughtIncoming
                // draughtOutgoing
                visitType = BERTH,
            ).also {
                log.debug { "Got an incoming PortcallVisit info to ${this.to_stop_location_code}" }
            }
        } else {
            null
        }
        return listOfNotNull(fromVisit, toVisit)
    }

    /**
     * Auxiliary method to get and select the exact matched by name Poma's Berth.
     */
    private fun String.findPomaBerth(): Berth? {
        return pomaService
            .getBerths(this, IDPREFIX_USCRP)
            .also {
                log.debug { "Returned ${it.size} berths when searching for $this." }
                log.debug { "Names: ${it.joinToString(", ") { it.name }}" }
            }.firstOrNull { it.name.equals(this, true) }
    }

    private val nonBerthLocations = listOf("SEA", "ANCH", "ARANSAS TERMINAL")

    /**
     * Auxiliary method to indicate whether a string,
     * typically [Movement].[from_stop_location_code] or [to_stop_location_code],
     * relates to a berth visit.
     */
    private fun String?.isBerthVisit(): Boolean {
        return this?.uppercase() !in nonBerthLocations
    }

    /**
     * Auxiliary method to get from a list of PortcallVisits, the first updateType that's not NOMINATION or null.
     * If none is found, it returns null.
     */
    private fun List<PortcallVisit>.getFirstNotNullNotNominationType(): UpdateType? {
        return this.map { it.visitType }.firstOrNull { it != null && it != NOMINATION }
    }

    /**
     * Auxiliary method for readability purposes.
     * ToDo: When the [Movement] model consolidates, transform the [Movement.jobType] to an enum class
     */
    private fun Movement.getStatus(): PortcallStatus {
        return when (jobType?.uppercase()) {
            "ARRIVAL" -> PortcallStatus.INBOUND
            "DEPARTURE" -> PortcallStatus.OUTBOUND
            "SHIFT" -> PortcallStatus.SHIFTING
            else -> PortcallStatus.UNKNOWN
        }
    }

    /**
     * Auxiliary method for readability purposes.
     * ToDo: When the [Movement] model consolidates, transform the [Movement.cargo_function] to an enum class
     */
    private fun Movement.getPortcallPurposes(): Set<PortcallPurpose> {
        val cargo_function_purpose = when (this.cargo_function?.uppercase()) {
            "LOADING" -> PortcallPurpose.LOADING
            "DISCHARGING" -> PortcallPurpose.DISCHARGE
            "OTHER" -> PortcallPurpose.OTHER
            else -> null
        }
        val product_purpose = PortcallPurpose.DISCHARGE_CRUDE_OIL.takeIf {
            cargo_function_purpose == PortcallPurpose.DISCHARGE &&
                this.product_code?.uppercase()?.contains("CRUDE OIL") == true
        }
        return setOfNotNull(cargo_function_purpose, product_purpose)
    }

    /**
     * Auxiliary method for readability purposes.
     */
    private fun Movement.getPortcallAliases(): Set<PortcallAlias> {
        return setOfNotNull(
            PortcallAlias(CORPUS_CHRISTI_ALIAS, this.visitNumber),
            visitId?.let { PortcallAlias(CORPUS_CHRISTI_ALIAS, it) },
        )
    }

    /**
     * Auxiliary method to make [convertToPortcall] more readable.
     */
    private fun Movement.toNewPortcall(visits: List<PortcallVisit>): Portcall {
        val portcallAliases = this.getPortcallAliases()
        val status = this.getStatus()
        val portAtdTime = this.offTime?.takeIf { status == PortcallStatus.OUTBOUND }
        val portcallId = generateCorpusChristiPortcallId(this)
        log.debug { "Generating the new portcall $portcallId" }
        return Portcall(
            portcallId = portcallId,
            portcallAlias = portcallAliases,
            port = IDPREFIX_USCRP,
            imo = this.vesselIMO,
            source = CORPUS_CHRISTI_TASK_TYPE,
            startTime = this.getStartTime(),
            startTimeType = visits.getFirstNotNullNotNominationType() ?: NOMINATION,
            status = status,
            endTime = portAtdTime,
            portAtaTime = (this.underwayTime ?: this.offTime)?.takeIf {
                status in listOf(PortcallStatus.INBOUND, PortcallStatus.SHIFTING) && this.to_stop_location.isBerthVisit()
            },
            portAtdTime = portAtdTime,
            etd = this.scheduledTime?.takeIf { status == PortcallStatus.OUTBOUND },
            // enteredPBP --> not enough data to set it
            // pilotBoardingPlaceType --> not enough data to set it
            vesselAgent = this.movement_agency,
            // originUnlocode --> not enough data to set it
            destinationUnlocode = IDPREFIX_USCRP,
            purpose = this.getPortcallPurposes(),
            visits = visits,
            // eosAtaTime --> not enough data to set it
            // eosAtdTime --> not enough data to set it
            product = this.product_code,
            pilot = this.pilotNo,
            // Remember that Movement.tugName actually refers to the Towing Company. See more details in the Movement model.
            towingCompany = this.tugName,
        )
    }

    private fun Movement.getStartTime(): Date {
        val status = this.getStatus()
        return (this.underwayTime ?: this.scheduledTime ?: this.offTime).takeIf {
            status in listOf(PortcallStatus.INBOUND, PortcallStatus.SHIFTING)
        } ?: this.dataExportTime
    }

    private fun generateCorpusChristiPortcallId(movement: Movement): String {
        var attempt = 0
        var id = "$IDPREFIX_USCRP${movement.visitNumber}"
        while (true) {
            val portcall = portcallDataSource.get(id)
            if (portcall == null) {
                if (attempt > 30) {
                    log(id, movement.vesselIMO, IDPREFIX_USCRP, "Took more than 30 attempts to find id!", Level.WARNING)
                }
                return id
            }
            attempt++
            id = "$IDPREFIX_USCRP${movement.visitNumber}-$attempt"
        }
    }

    // Auxiliary methods for [getServiceModels]

    /**
     * Auxiliary method to obtain a Summary out of the [FileParsingResult].
     */
    private fun FileParsingResult.toFileParsingSummary(
        fileKey: String,
        processTime: Instant,
    ): FileParsingSummary {
        return FileParsingSummary(
            fileKey,
            processTime,
            this.invalidRawMovements.size,
            this.validMovements.size,
        )
    }

    /**
     * Method to obtain the unread files from the S3 bucket.
     */
    fun getUnreadFileKeys(): List<String> {
        val fileKeysInBucket = s3BucketClient.listObjects()
            .also { log.debug { "Amount of files from bucket: ${ it.size }. First and last: ${it.firstOrNull() }, ${ it.lastOrNull() }" } }
        val alreadyProcessedFiles = processedFilesDataSource.get(fileKeysInBucket)
            .map { it.fileKey }
        val unprocessedFiles = fileKeysInBucket - alreadyProcessedFiles
        return unprocessedFiles
    }

    /**
     * Method that process a file in the S3 bucket.
     * Currently only covering the data reading part.
     */
    fun processFileKey(
        fileKey: String,
        processTime: Instant,
    ): FileParsingResult {
        val corpusChristiVisitsByteArray = try {
            s3BucketClient.getSimpleObject(fileKey)
        } catch (exception: Exception) {
            return FileParsingResult(emptyList(), emptyList(), "Exception while getting the S3Object: ${exception.message}")
        }
        val entries = runCatching { mapper.readValue<List<Any>>(corpusChristiVisitsByteArray) }
            .getOrElse {
                return FileParsingResult(
                    emptyList(),
                    emptyList(),
                    "Exception while parsing as List<Any> the S3Object: ${it.message}",
                )
            }

        val invalidRawMovements = mutableListOf<Pair<RawMovement, List<String>>>()
        val validMovements = mutableListOf<Movement>()
        entries.forEach { itEntry ->
            val jsonEntry = mapper.writeValueAsString(itEntry)
            val rawMovement = runCatching { mapper.readValue<RawMovement>(jsonEntry) }
                .getOrNull() ?: return@forEach
            val (movement, validationErrors) = validateMovement(rawMovement)
            if (movement != null) {
                validMovements.add(movement)
            } else {
                invalidRawMovements.add(rawMovement to validationErrors)
                log.debug { jsonEntry }
            }
        }
        invalidRawMovements.logInvalidRawMovements(fileKey)
        return FileParsingResult(invalidRawMovements, validMovements, "")
    }

    /**
     * Auxiliary method to convert a [RawMovement] to a [Movement] or reporting all validation errors.
     * This is to give clarity to whatever failing case happens.
     * Once the data model is fully consolidated (nullable values and expected situations), then it'll be reduced.
     */
    fun validateMovement(rawMovement: RawMovement): Pair<Movement?, List<String>> {
        val errors = mutableListOf<String>()

        val dataExportTime = rawMovement::dataExportTime.getNotNullDateOrNullAndAddError(errors)
        val visitNumber = rawMovement::visitNumber.getNotNullStringOrNullAndAddError(errors)
        val vesselIMO = rawMovement::vesselIMO.getNotNullStringOrNullAndAddError(errors)

        val from_stop_location = rawMovement::from_stop_location.getNotNullStringOrNullAndAddError(errors)
        val to_stop_location = rawMovement::to_stop_location.getNotNullStringOrNullAndAddError(errors)
        val movement_agency = rawMovement::movement_agency.getNotNullStringOrNullAndAddError(errors)

        // These three are nullable dates, so valid values are null or an expected-well-formated* date (without timezone!)
        val (scheduledTime, validScheduledTime) = rawMovement::scheduledTime.getValidDateOrNullAndAddError(errors)
        val (underwayTime, validUnderwayTimeFormat) = rawMovement::underwayTime.getValidDateOrNullAndAddError(errors)
        val (offTime, validOffTimeFormat) = rawMovement::offTime.getValidDateOrNullAndAddError(errors)

        if (
            dataExportTime == null ||
            visitNumber == null ||
            vesselIMO == null ||
            from_stop_location == null ||
            to_stop_location == null ||
            movement_agency == null ||
            !validScheduledTime ||
            !validUnderwayTimeFormat ||
            !validOffTimeFormat
        ) {
            return null to errors
        } else {
            val movement = Movement(
                dataExportTime = dataExportTime,
                movementId = rawMovement.movementId,
                visitId = rawMovement.visitId,
                visitNumber = visitNumber,
                jobType = rawMovement.jobType,
                movement_status_type_id = rawMovement.movement_status_type_id,
                jobStatus = rawMovement.jobStatus,
                isCanceledMovement = rawMovement.isCanceledMovement,
                vesselIMO = vesselIMO,
                vesselName = rawMovement.vesselName,
                scheduledTime = scheduledTime,
                underwayTime = underwayTime,
                offTime = offTime,
                foreDraft = rawMovement.foreDraft,
                aftDraft = rawMovement.aftDraft,
                from_stop_location_id = rawMovement.from_stop_location_id,
                from_stop_location_code = rawMovement.from_stop_location_code,
                from_stop_location = from_stop_location,
                to_stop_location_id = rawMovement.to_stop_location_id,
                to_stop_location_code = rawMovement.to_stop_location_code,
                to_stop_location = to_stop_location,
                movement_agency = movement_agency,
                movement_agency_code = rawMovement.movement_agency_code,
                lineHandler = rawMovement.lineHandler,
                tugName = rawMovement.tugName?.takeIf { it.isNotBlank() },
                pilotNo = rawMovement.pilotNo?.takeIf { it.isNotBlank() },
                cargo_function = rawMovement.cargo_function,
                product_code = rawMovement.product_code?.takeIf { it != "NO CARGO" || it.isNotBlank() },
            )
            return movement to emptyList()
        }
    }

    /**
     * Auxiliary method to validate an object's String? field in as Date?
     */
    fun KProperty0<String?>.getNotNullDateOrNullAndAddError(errors: MutableList<String>): Date? {
        val itDateString = this.get()
        return if (itDateString.isNullOrBlank()) {
            errors.add("'${this.name}' can't be null.")
            null
        } else {
            runCatching {
                fetchTimestamp(itDateString, READABLE_FORMAT, US_CHICAGO)
            }.onFailure {
                errors.add("Parsing string date '${this.name}' as '$itDateString' failed: ${it.localizedMessage}.")
            }.getOrNull()
        }
    }

    /**
     * Auxiliary method to validate an object's String? field in as Date
     */
    fun KProperty0<String?>.getValidDateOrNullAndAddError(errors: MutableList<String>): Pair<Date?, Boolean> {
        val itDateString = this.get()
        return if (itDateString.isNullOrBlank()) {
            null to true
        } else {
            try {
                fetchTimestamp(itDateString, READABLE_FORMAT, US_CHICAGO) to true
            } catch (it: Exception) {
                errors.add("Parsing string date '${this.name}' as '$itDateString' failed: ${it.localizedMessage}.")
                null to false
            }
        }
    }

    /**
     * Auxiliary method to validate an object's String? field in as String
     */
    fun KProperty0<String?>.getNotNullStringOrNullAndAddError(errors: MutableList<String>): String? {
        if (this.get().isNullOrBlank()) {
            errors.add("'${this.name}' can't be null or blank.")
        }
        return this.get()
    }

    /**
     * Auxiliary method to log invalid Raw Movements obtained from the S3 bucket.
     */
    private fun List<Pair<RawMovement, List<String>>>.logInvalidRawMovements(fileKey: String) {
        if (this.isNotEmpty()) {
            log.warn { "There were ${this.size} RawMovements invalid as Movements in the file $fileKey" }
            this.forEach { (invalidRawMovement, errorMessages) ->
                log.warn { "RawMovement id ${invalidRawMovement.movementId} invalid as Movement because of:" }
                errorMessages.forEach { log.warn { " - $it " } }
                log.debug { "Invalid RawMovement: $invalidRawMovement" }
            }
        }
    }
}
