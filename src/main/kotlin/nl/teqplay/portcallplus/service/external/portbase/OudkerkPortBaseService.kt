package nl.teqplay.portcallplus.service.external.portbase

import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.datasource.ActivityDataSource
import nl.teqplay.portcallplus.datasource.PortBaseAgentMappingDataSource
import nl.teqplay.portcallplus.datasource.PortBaseDataSource
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.datasource.ScheduledTaskRunDataSource
import nl.teqplay.portcallplus.datasource.ServiceFetchCounterDataSource
import nl.teqplay.portcallplus.model.ScheduledTaskTypeFieldsProxy
import nl.teqplay.portcallplus.properties.PortBaseProperties
import nl.teqplay.portcallplus.service.common.EventService
import nl.teqplay.portcallplus.service.internal.PoMaService
import nl.teqplay.portcallplus.service.internal.UserService
import org.springframework.stereotype.Service

/**
 * This will retrieve portcalls from PortBase
 */
@Service
class OudkerkPortBaseService(
    dataSource: PortBaseDataSource,
    scheduledTaskRunDataSource: ScheduledTaskRunDataSource,
    activityDataSource: ActivityDataSource,
    serviceFetchCounterDataSource: ServiceFetchCounterDataSource,
    portcallDataSource: PortcallDataSource,
    private val portBaseProperties: PortBaseProperties,
    private val scheduledTaskTypeFieldsProxy: ScheduledTaskTypeFieldsProxy,
    private val userService: UserService,
    pomaService: PoMaService,
    eventService: EventService,
    agentMappingDataSource: PortBaseAgentMappingDataSource,
) : PortBaseService(
    dataSource, scheduledTaskRunDataSource, activityDataSource, serviceFetchCounterDataSource, portcallDataSource,
    ScheduledTaskType.OUDKERK_PORTBASE, portBaseProperties, portBaseProperties.oudkerk, scheduledTaskTypeFieldsProxy, userService, pomaService, eventService, agentMappingDataSource,
)
