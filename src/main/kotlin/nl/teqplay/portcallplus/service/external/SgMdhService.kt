package nl.teqplay.portcallplus.service.external

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.poma.api.v1.Berth
import nl.teqplay.portcallplus.PreconditionException
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallStatus
import nl.teqplay.portcallplus.api.model.PortcallVisit
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.SG_MDH_DUE_TO_ARRIVE
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.SG_MDH_DUE_TO_DEPART
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.SG_MDH_VISIT_ARRIVAL_DECLARATION
import nl.teqplay.portcallplus.api.model.ServiceModel
import nl.teqplay.portcallplus.api.model.UpdateType
import nl.teqplay.portcallplus.datasource.ActivityDataSource
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.datasource.ScheduledTaskRunDataSource
import nl.teqplay.portcallplus.datasource.ServiceFetchCounterDataSource
import nl.teqplay.portcallplus.model.ScheduledTaskTypeFieldsProxy
import nl.teqplay.portcallplus.model.service.SgMdhArrivalDeclaration
import nl.teqplay.portcallplus.model.service.SgMdhDueToArrive
import nl.teqplay.portcallplus.model.service.SgMdhDueToDepart
import nl.teqplay.portcallplus.properties.SgmdhProperties
import nl.teqplay.portcallplus.service.common.EventService
import nl.teqplay.portcallplus.service.common.TaskService
import nl.teqplay.portcallplus.service.internal.LocationMappingService
import nl.teqplay.portcallplus.service.internal.PoMaService
import nl.teqplay.portcallplus.service.internal.UserService
import nl.teqplay.portcallplus.utils.executeGetCall
import nl.teqplay.portcallplus.utils.fetchTimestamp
import nl.teqplay.portcallplus.utils.log
import nl.teqplay.portcallplus.utils.removeBraces
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import java.io.EOFException
import java.util.Date
import java.util.TimeZone
import java.util.concurrent.TimeUnit
import java.util.logging.Level

private const val SGSIN_DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss"
private const val SGSIN_DATE_FORMAT = "yyyy-MM-dd"
private const val AUTH_HEADER_KEY = "apikey"

@Service
class SgMdhService(
    scheduledTaskRunDataSource: ScheduledTaskRunDataSource,
    activityDataSource: ActivityDataSource,
    serviceFetchCounterDataSource: ServiceFetchCounterDataSource,
    private val portcallDataSource: PortcallDataSource,
    val mapper: ObjectMapper,
    private val sgmdhProperties: SgmdhProperties,
    private val scheduledTaskTypeFieldsProxy: ScheduledTaskTypeFieldsProxy,
    private val userService: UserService,
    pomaService: PoMaService,
    eventService: EventService,
    private val locationMappingService: LocationMappingService,
) : TaskService(
    scheduledTaskRunDataSource,
    activityDataSource,
    serviceFetchCounterDataSource,
    portcallDataSource,
    scheduledTaskTypeFieldsProxy,
    userService,
    pomaService,
    eventService,
) {
    private val SGSIN_TIMEZONE: TimeZone = TimeZone.getTimeZone("Asia/Singapore")
    val NEW_PORTCALL_INTERVAL = TimeUnit.DAYS.toMillis(sgmdhProperties.newPortcallInterval)
    val FERRY_PORTCALL_INTERVAL = TimeUnit.HOURS.toMillis(sgmdhProperties.ferryPortcallInterval)
    private val okhttp = okhttp3.OkHttpClient()

    /*
     * Singapore Maritime Data Hub of their Maritime Port Authorities URLs.
     * It needs a date in the format of SGSIN_REQUEST_DATE_FORMAT as query.
     * */
    private val SG_MDH_MARITIME_BASE_URL = "https://sg-mdh-api.mpa.gov.sg/v1/vessel"
    private val SG_MDH_MARITIME_ARRIVAL_DECLARATION_URL = "$SG_MDH_MARITIME_BASE_URL/arrivaldeclaration/pastNhours"
    private val SG_MDH_MARITIME_DUE_TO_ARRIVE_URL = "$SG_MDH_MARITIME_BASE_URL/duetoarrive/date/"
    private val SG_MDH_MARITIME_DUE_TO_DEPART_URL = "$SG_MDH_MARITIME_BASE_URL/duetodepart/date/"

    override fun getTasks(): List<ScheduledTaskType> =
        listOf(
            SG_MDH_DUE_TO_ARRIVE,
            SG_MDH_VISIT_ARRIVAL_DECLARATION,
            SG_MDH_DUE_TO_DEPART,
        )

    override val isEnabled: Boolean = sgmdhProperties.enable
    override val allowOtherSourcePortcallUpdate = sgmdhProperties.allowOtherSourcePortcallUpdate

    override fun getServiceModels(taskType: ScheduledTaskType): Collection<Pair<String, ServiceModel>> {
        return when (taskType) {
            SG_MDH_DUE_TO_ARRIVE -> createPortcallFromDueToArriveTask()
            SG_MDH_VISIT_ARRIVAL_DECLARATION -> createPortcallFromArrivalDeclarationTask()
            SG_MDH_DUE_TO_DEPART -> updatePortcallFromDueToDepartTask()
            else -> throw PreconditionException("${this::class.java.simpleName} is invoked for unhandled taskType: $taskType")
        }
    }

    /**
     * Convert the given [serviceModel] received from this external service to an internal representation in [Portcall]
     * @param serviceModel The model received from the this external service
     * @param imoNumber The vessel imoNumber for which this data refers to
     * @param taskType The taskType is requesting this action
     */
    override fun convertToPortcall(
        serviceModel: ServiceModel,
        imoNumber: String,
        taskType: ScheduledTaskType,
    ): Portcall {
        val eventTime: Date
        var portcallInterval = NEW_PORTCALL_INTERVAL

        // fetch event related details
        when (serviceModel) {
            is SgMdhDueToArrive -> {
                eventTime = fetchSGSINTimestamp(serviceModel.duetoArriveTime)
                if (serviceModel.locationTo.contains("ferry", true)) {
                    log(
                        imoNumber = imoNumber,
                        message = "Using ${FERRY_PORTCALL_INTERVAL}ms as portcall interval. Ferry location: ${serviceModel.locationTo}",
                    )
                    portcallInterval = FERRY_PORTCALL_INTERVAL
                }
            }
            is SgMdhArrivalDeclaration -> {
                eventTime = fetchSGSINTimestamp(serviceModel.reportedArrivalTime)
                if (serviceModel.agent.contains("ferry", true) ||
                    serviceModel.location.contains("ferry", true)
                ) {
                    log(
                        imoNumber = imoNumber,
                        message = "Using ${FERRY_PORTCALL_INTERVAL}ms as portcall interval. Location: ${serviceModel.location} or agent: ${serviceModel.agent}",
                    )
                    portcallInterval = FERRY_PORTCALL_INTERVAL
                }
            }
            is SgMdhDueToDepart -> {
                eventTime = fetchSGSINTimestamp(serviceModel.dueToDepart)
            }
            else -> throw PreconditionException("${this::class.java.simpleName} is invoked for unhandled model: ${serviceModel::class}")
        }
        // fetch the nearest portcall to eta within 12h interval
        val resultPortcall = portcallDataSource.getNearestByImoAndDateInterval(
            imoNumber,
            Portcall.IDPREFIX_SGSIN,
            eventTime,
            portcallInterval,
            null,
        )

        // create or update a portcall instance
        return when (serviceModel) {
            is SgMdhDueToArrive -> {
                val berth = resolveBerth(serviceModel.locationTo, SgMdhDueToArrive::class.simpleName.toString())
                val newVisits = listOf(
                    PortcallVisit(
                        berthEta = eventTime,
                        berthName = berth?.nameLong ?: serviceModel.locationTo,
                        uniqueBerthId = berth?.uniqueId,
                        visitType = getVisitType(serviceModel.locationTo),
                    ),
                )
                if (shouldCreateNewPortcall(eventTime, resultPortcall)) {
                    val nextPortcallId = portcallDataSource.generateId(Portcall.IDPREFIX_SGSIN, imoNumber, eventTime)
                    log(portcallId = nextPortcallId, message = "No portcall found. Creating new")
                    Portcall(
                        portcallId = nextPortcallId,
                        portcallAlias = emptySet(),
                        port = Portcall.IDPREFIX_SGSIN,
                        imo = imoNumber,
                        source = taskType,
                        startTime = eventTime,
                        startTimeType = getVisitType(serviceModel.locationTo),
                        status = PortcallStatus.INBOUND,
                        destinationUnlocode = Portcall.IDPREFIX_SGSIN,
                        visits = newVisits,
                    )
                } else {
                    resultPortcall!!.getUpdatedPortcall(
                        startTimeType = getVisitType(serviceModel.locationTo),
                        newVisits = newVisits,
                    )
                }
            }
            is SgMdhArrivalDeclaration -> {
                val berth = resolveBerth(serviceModel.location, SgMdhArrivalDeclaration::class.simpleName.toString())
                val newVisits = listOf(
                    PortcallVisit(
                        berthAta = eventTime,
                        berthName = berth?.nameLong ?: serviceModel.location,
                        uniqueBerthId = berth?.uniqueId,
                        visitType = getVisitType(serviceModel.location),
                    ),
                )
                if (shouldCreateNewPortcall(eventTime, resultPortcall)) {
                    val nextPortcallId = portcallDataSource.generateId(Portcall.IDPREFIX_SGSIN, imoNumber, eventTime)
                    log(portcallId = nextPortcallId, message = "No portcall found. Creating new")
                    Portcall(
                        portcallId = nextPortcallId,
                        portcallAlias = emptySet(),
                        port = Portcall.IDPREFIX_SGSIN,
                        imo = imoNumber,
                        source = taskType,
                        startTime = eventTime,
                        startTimeType = getVisitType(serviceModel.location),
                        status = PortcallStatus.INBOUND,
                        destinationUnlocode = Portcall.IDPREFIX_SGSIN,
                        vesselAgent = serviceModel.agent,
                        visits = newVisits,
                    )
                } else {
                    resultPortcall!!.getUpdatedPortcall(
                        vesselAgent = serviceModel.agent,
                        purpose = serviceModel.getPurpose(),
                        newVisits = newVisits,
                    )
                }
            }
            is SgMdhDueToDepart -> {
                if (resultPortcall != null && resultPortcall.visits.isNotEmpty()) {
                    val lastVisit = resultPortcall.visits.last()
                    // if no berthEtd is previously seen, the new etd should be after the current eta
                    val updatedEtd = if (lastVisit.berthEtd == null) {
                        if (eventTime.after(resultPortcall.startTime)) eventTime else null
                    }
                    // if berthEtd is previously seen, the new etd should be after the current eta and before a
                    // reasonable interval or a previous etd
                    else {
                        val portcallEtdTimeInterval = fetchTimestamp(lastVisit.berthEtd!!, portcallInterval)
                        if (eventTime.after(resultPortcall.startTime) && eventTime.before(portcallEtdTimeInterval)) {
                            eventTime
                        } else {
                            null
                        }
                    }
                    val berthName = resultPortcall.visits.last().berthName
                    val berth = berthName?.let { resolveBerth(it, SgMdhDueToDepart::class.simpleName.toString()) }
                    resultPortcall.getUpdatedPortcall(
                        newVisits = listOf(
                            PortcallVisit(
                                berthEtd = updatedEtd ?: lastVisit.berthEtd,
                                berthName = berth?.nameLong ?: berthName,
                                uniqueBerthId = berth?.uniqueId,
                                visitType = berthName?.let { getVisitType(it) },
                            ),
                        ),
                    )
                } else {
                    throw PreconditionException(
                        "Expected existing portcall, but not found!",
                        serviceModel.vesselParticulars.imoNumber.toString(),
                    )
                }
            }
            else -> throw PreconditionException("${this::class.java.simpleName} is invoked for unhandled model: ${serviceModel::class}")
        }
    }

    private fun createPortcallFromDueToArriveTask(): List<Pair<String, SgMdhDueToArrive>> {
        val currentTime = Date()
        // fetch all vessel details that are due to arrive
        val dueToArriveList = getDueToArriveData(currentTime)
        // log data fetched
        log(
            message = "Number of entries for due to arrive: ${dueToArriveList.size}. " +
                "Imos: ${dueToArriveList.map { it.vesselParticulars.imoNumber }.removeBraces()}",
        )
        // create a portcall instance for all received data
        return dueToArriveList.map { Pair(it.vesselParticulars.imoNumber.toString(), it) }
    }

    private fun createPortcallFromArrivalDeclarationTask(): List<Pair<String, SgMdhArrivalDeclaration>> {
        val currentTime = Date()
        val arrivalDeclarationList = getArrivalDeclarationData(currentTime)
        // log data fetched
        log(
            message = "Number of entries for arrival declaration: ${arrivalDeclarationList.size}. " +
                "Imos: ${arrivalDeclarationList.map { it.vesselParticulars.imoNumber }.removeBraces()}",
        )
        // create a portcall instance for all received data
        return arrivalDeclarationList.map { Pair(it.vesselParticulars.imoNumber.toString(), it) }
    }

    private fun updatePortcallFromDueToDepartTask(): List<Pair<String, SgMdhDueToDepart>> {
        val currentTime = Date()
        // fetch all vessel details that are due to arrive
        val dueToDepartList = getDueToDepartData(currentTime)
        // log data fetched
        log(
            message = "Number of entries for due to depart: ${dueToDepartList.size}. " +
                "Imos: ${dueToDepartList.map { it.vesselParticulars.imoNumber }.removeBraces()}",
        )
        // create a portcall instance for all received data
        return dueToDepartList.map { Pair(it.vesselParticulars.imoNumber.toString(), it) }
    }

    /**
     * This function recursively gets the list of SgMdhDueToArrive from the given [fromDate] till the a future date the
     * date exists for (Usually 10 days or so)
     * @param fromDate The date from which the due to arrival data must be fetched
     * @return this returns a list of SgMdhDueToArrive
     */
    private fun getDueToArriveData(fromDate: Date): List<SgMdhDueToArrive> {
        return fetchAllSgMdhData(SG_MDH_MARITIME_DUE_TO_ARRIVE_URL, fromDate, sgmdhProperties.dueToArriveData, object : TypeReference<List<SgMdhDueToArrive>>() {})
    }

    /**
     * This function gets a list of SgMdhArrivalDeclaration from the url
     * @param fromDate The date from which the due to arrival data must be fetched
     * @return this returns a list of SgMdhArrivalDeclaration
     */
    private fun getArrivalDeclarationData(fromDate: Date): List<SgMdhArrivalDeclaration> {
        val singaporeFormattedDate = fetchSGSINTimestamp(fromDate)
        log(
            message = "Getting arrival declaration on $SG_MDH_MARITIME_ARRIVAL_DECLARATION_URL " +
                "with query params datetime($singaporeFormattedDate) with hours(${sgmdhProperties.arrivalDeclarationPastNHours})",
            level = Level.INFO,
        )
        val allSgMdhData = mutableListOf<SgMdhArrivalDeclaration>()
        try {
            val url = "$SG_MDH_MARITIME_ARRIVAL_DECLARATION_URL?datetime=$singaporeFormattedDate&hours=${sgmdhProperties.arrivalDeclarationPastNHours}"
            val headers = mapOf(AUTH_HEADER_KEY to sgmdhProperties.token)
            val response = okhttp.executeGetCall(url, emptyMap(), headers)
            val body = response.body?.string()
            val sgMdhData = when {
                body.isNullOrBlank() -> null
                response.code == HttpStatus.OK.value() -> mapper.readValue<List<SgMdhArrivalDeclaration>>(body)
                response.code == HttpStatus.NO_CONTENT.value() -> emptyList()
                else -> null
            }
            if (sgMdhData == null) {
                log(
                    message = "Issue with connecting to Sg Mdh Datahub service ($SG_MDH_MARITIME_ARRIVAL_DECLARATION_URL)." +
                        " Received status code: ${response.code} with reason: $body",
                )
            } else {
                allSgMdhData.addAll(sgMdhData)
            }
        } catch (e: EOFException) {
            log(
                message = "Issue with connecting to Sg Mdh Datahub service ($SG_MDH_MARITIME_ARRIVAL_DECLARATION_URL)." +
                    " Received some EOFException with query params datetime(${fetchSGSINTimestamp(fromDate)}) with" +
                    " hours(${sgmdhProperties.arrivalDeclarationPastNHours})",
            )
        }
        return allSgMdhData
    }

    /**
     * This function gets a list of SgMdhDueToDepart from the url
     * @param fromDate The date from which the due to depart data must be fetched
     * @return this returns a list of SgMdhDueToDepart
     */
    private fun getDueToDepartData(fromDate: Date): List<SgMdhDueToDepart> {
        return fetchAllSgMdhData(SG_MDH_MARITIME_DUE_TO_DEPART_URL, fromDate, sgmdhProperties.dueToDepartData, object : TypeReference<List<SgMdhDueToDepart>>() {})
    }

    /**
     * Recursively fetch all data from the given [from] date, until the the future dates, until no more data is available
     * or a max of 20 days
     */
    private fun <T : ServiceModel> fetchAllSgMdhData(
        url: String,
        from: Date,
        days: Int,
        typeRef: TypeReference<List<T>>,
    ): List<T> {
        val allSgMdhData = mutableListOf<T>()
        var fetchDate = from
        val maxFutureDate = fetchTimestamp(from, days, TimeUnit.DAYS)
        do {
            // parse the date in SGSIN_DATE_FORMAT at the end of the url
            val dateForQuery = fetchSGSINTimestamp(fetchDate, SGSIN_DATE_FORMAT)

            // fetch data for the parsed date
            try {
                val completeUrl = url + dateForQuery
                val headers = mapOf(AUTH_HEADER_KEY to sgmdhProperties.token)
                val response = okhttp.executeGetCall(completeUrl, emptyMap(), headers)
                val body = response.body?.string()
                val sgMdhData = when {
                    response.code == HttpStatus.NO_CONTENT.value() || body.isNullOrBlank() -> emptyList()
                    response.code == HttpStatus.OK.value() -> mapper.readValue(body, typeRef)
                    response.code == HttpStatus.BAD_REQUEST.value() -> {
                        // TODO: Investigate the reason of this sleep and leave a comment explaining it.
                        if (body.contains("The rate limit has been exceeded")) {
                            Thread.sleep(2000)
                        }
                        emptyList()
                    }
                    else -> null
                }
                // add all retrieved data
                if (sgMdhData == null) {
                    log(level = Level.WARNING, message = "Issue with connecting to Sg Mdh Datahub service. ($url) Received status code: ${response.code} with reason: $body")
                    return emptyList()
                }
                allSgMdhData.addAll(sgMdhData)
                log(message = "Fetched ${sgMdhData.size} ${typeRef.type} for $fetchDate.")
            } catch (e: EOFException) {
                log(message = "Unable to fetch data due to possibly no content from ${url + dateForQuery}.", level = Level.WARNING)
            }
            // update the date to next day
            fetchDate = fetchTimestamp(fetchDate, 1, TimeUnit.DAYS)
        } while (fetchDate.before(maxFutureDate))
        return allSgMdhData
    }

    /**
     * A new portcall must be created if either [existingPortcall] is null, or has a recent [Portcall.endTime]/
     * [Portcall.portAtdTime]
     * @return true if a new portcall has to be created
     */
    private fun shouldCreateNewPortcall(
        eventTime: Date,
        existingPortcall: Portcall?,
    ): Boolean {
        val exitInterval = fetchTimestamp(eventTime, -1 * sgmdhProperties.portcallExitInterval, TimeUnit.HOURS)
        return existingPortcall == null ||
            existingPortcall.endTime?.before(exitInterval) == true ||
            // for backward compatibility sake. Cases where endTime is missing, but portAtd is found
            existingPortcall.portAtdTime?.before(exitInterval) == true
    }

    /**
     * Returns the given SGSIN [timestamp] in [SGSIN_DATE_TIME_FORMAT] in generic [Date] format
     */
    fun fetchSGSINTimestamp(timestamp: String) = fetchTimestamp(timestamp, SGSIN_DATE_TIME_FORMAT, SGSIN_TIMEZONE)

    /**
     * Returns the given [date] in the require [format]. Defaulted to [SGSIN_DATE_TIME_FORMAT]
     */
    fun fetchSGSINTimestamp(
        date: Date,
        format: String = SGSIN_DATE_TIME_FORMAT,
    ) = fetchTimestamp(date, format, SGSIN_TIMEZONE)

    private fun resolveBerth(
        name: String,
        sourceName: String,
    ): Berth? {
        val locationMapping = locationMappingService.getOrCreateEmptyLocation(sourceName, Portcall.IDPREFIX_SGSIN, name)
        return locationMappingService.getPomaBerthForLocation(locationMapping)
    }
}

fun getVisitType(locationTo: String): UpdateType =
    when {
        locationTo.contains("gusong boarding", true) ||
            locationTo.contains("pilot", true) -> UpdateType.PILOTBOARDINGPLACE
        locationTo.contains("anchorage", true) ||
            locationTo.contains("anch", true) -> UpdateType.ANCHORAGE
        else -> UpdateType.BERTH
    }
