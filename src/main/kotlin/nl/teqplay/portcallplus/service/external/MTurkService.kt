package nl.teqplay.portcallplus.service.external

import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import nl.teqplay.portcallplus.PreconditionException
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallAliasName
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.datasource.ActivityDataSource
import nl.teqplay.portcallplus.datasource.MTurkHITDataSource
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.datasource.ServiceFetchCounterDataSource
import nl.teqplay.portcallplus.model.data.Activity
import nl.teqplay.portcallplus.model.data.ServiceFetchCounter
import nl.teqplay.portcallplus.model.data.ServiceFetchDetail
import nl.teqplay.portcallplus.model.data.mturk.Assignment
import nl.teqplay.portcallplus.model.data.mturk.HIT
import nl.teqplay.portcallplus.model.data.mturk.HitStatus
import nl.teqplay.portcallplus.model.data.mturk.MTurkEventCallback
import nl.teqplay.portcallplus.model.data.mturk.MTurkHITDetails
import nl.teqplay.portcallplus.model.data.mturk.MTurkHITResponse
import nl.teqplay.portcallplus.model.data.mturk.WorkerBlock
import nl.teqplay.portcallplus.model.data.mturk.WorkersNotification
import nl.teqplay.portcallplus.model.service.MTurkHIT
import nl.teqplay.portcallplus.properties.MTurkProperties
import nl.teqplay.portcallplus.service.common.EventService
import nl.teqplay.portcallplus.utils.log
import nl.teqplay.portcallplus.utils.readFromString
import nl.teqplay.portcallplus.utils.removeSpecialChars
import org.apache.commons.lang.StringEscapeUtils
import org.springframework.stereotype.Service
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.mturk.MTurkClient
import software.amazon.awssdk.services.mturk.model.ApproveAssignmentRequest
import software.amazon.awssdk.services.mturk.model.AssignmentStatus
import software.amazon.awssdk.services.mturk.model.AssignmentStatus.APPROVED
import software.amazon.awssdk.services.mturk.model.AssignmentStatus.REJECTED
import software.amazon.awssdk.services.mturk.model.AssignmentStatus.SUBMITTED
import software.amazon.awssdk.services.mturk.model.AssignmentStatus.UNKNOWN_TO_SDK_VERSION
import software.amazon.awssdk.services.mturk.model.CreateAdditionalAssignmentsForHitRequest
import software.amazon.awssdk.services.mturk.model.CreateHitRequest
import software.amazon.awssdk.services.mturk.model.DeleteHitRequest
import software.amazon.awssdk.services.mturk.model.EventType
import software.amazon.awssdk.services.mturk.model.GetHitRequest
import software.amazon.awssdk.services.mturk.model.HITLayoutParameter
import software.amazon.awssdk.services.mturk.model.ListAssignmentsForHitRequest
import software.amazon.awssdk.services.mturk.model.ListWorkerBlocksRequest
import software.amazon.awssdk.services.mturk.model.NotificationSpecification
import software.amazon.awssdk.services.mturk.model.NotificationTransport
import software.amazon.awssdk.services.mturk.model.NotifyWorkersRequest
import software.amazon.awssdk.services.mturk.model.RejectAssignmentRequest
import software.amazon.awssdk.services.mturk.model.SendTestEventNotificationRequest
import software.amazon.awssdk.services.mturk.model.UpdateExpirationForHitRequest
import software.amazon.awssdk.services.mturk.model.UpdateNotificationSettingsRequest
import java.net.URI
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.Date
import java.util.logging.Level

/**
 * Service to given functionalities to create a MTurk HIT and assign it.
 * Because this is an async task (of getting a request to create a HIT and receiving an answer for it),
 * it does not directly extend a service like: TaskService
 */
@Service
class MTurkService(
    private val activityDataSource: ActivityDataSource,
    private val serviceFetchCounterDataSource: ServiceFetchCounterDataSource,
    private val portcallDataSource: PortcallDataSource,
    private val mTurkDataSource: MTurkHITDataSource,
    private val mTurkProperties: MTurkProperties,
    private val eventService: EventService,
) {
    // prevent from booting if none of these
    private var isEnabled: Boolean = isMTurkEnabled()
    private val pullAssignments: Boolean = mTurkProperties.pullAssignments
    private val pushAssignments: Boolean = mTurkProperties.pushAssignments
    private var daysConfigured: Long = mTurkProperties.daysConfigured
    private val approveInvalidAnswers = mTurkProperties.approveInvalidAnswers

    // Mainly for debugging purposes: to review HIT assignments once they're done.
    private val disposeAfterExpiring = mTurkProperties.disposeAfterExpiring
    private val minimumSameValidAnswers = mTurkProperties.minimumSameValidAnswers

    companion object {
        fun extractEventFromNotification(notificationMessage: String): String =
            StringEscapeUtils.unescapeJava(
                notificationMessage
                    .substringAfter("\"Message\" :").substringBefore(", \"Timestamp\"")
                    .trim()
                    .removePrefix("\"")
                    .removeSuffix("\"")
                    .trim(),
            )

        fun parseAnswer(answer: String): String? =
            StringEscapeUtils.unescapeXml(
                answer.split("FreeText>").getOrNull(1)
                    ?.replace("</", "")
                    ?.substringBeforeLast("(")
                    ?.trim(),
            )?.uppercase()
    }

    // Setting it in a function to use it in VesselStayServiceV2 and mock it when needed.
    fun isMTurkEnabled(): Boolean {
        return mTurkProperties.enabled
    }

    fun updatePortcall(callbackPayload: String) {
        if (!pushAssignments) {
            log.info { "MTurk push not enabled. Not processing anything." }
            return
        }
        val eventCallback = kotlin.runCatching { getEventFromNotification(callbackPayload) }
            .onFailure { log.info(it) { "[MTurk] failure to get event from notification" } }
            .getOrNull()
        if (eventCallback != null) {
            log.info { "[MTurk] eventCallback $eventCallback" }
            updatePortcall(eventCallback)
        } else {
            log.info { "[MTurk] eventCallback is null" }
        }
    }

    fun updatePortcall(eventCallback: MTurkEventCallback) {
        eventCallback.events.forEach { answerEvent ->
            if (answerEvent.answer != null) {
                val hitDetails = updateAssignmentStatus(answerEvent.hitId)
                if (hitDetails != null) {
                    val portcallId = hitDetails.request.portcallId
                    val shipImo = hitDetails.request.shipImo
                    val portcall = portcallDataSource.searchByAlias(portcallId, PortcallAliasName.NXTPORT).firstOrNull()
                        ?: run {
                            log.info { "[MTurk] Portcall not found" }
                            throw PreconditionException("Portcall not found", portcallId)
                        }
                    val correctAnswer = assessHITAssignment(
                        hitDetails,
                        portcall,
                        answerEvent.assignmentId,
                        answerEvent.answer,
                    )

                    // update the portcall with the correct vesselAgent
                    if (correctAnswer != null && portcall.vesselAgent == null) {
                        log(portcallId, shipImo, Portcall.IDPREFIX_ANR, "Updated vesselAgent: $correctAnswer")
                        val updatedPortcall = portcall.copy(vesselAgent = correctAnswer)
                        portcallDataSource.createOrUpdate(updatedPortcall)
                        eventService.onUpdatedPortcall(currentPortcall = portcall, updatedPortcall = updatedPortcall)
                    } else {
                        log(message = "[MTurk] No agent update due to $correctAnswer or ${portcall.vesselAgent}", level = Level.INFO)
                    }
                } else {
                    log(message = "[MTurk] No HIT found for: ${answerEvent.hitId}", level = Level.WARNING)
                }
            }
        }
    }

    fun handleHITRequests(hits: Set<MTurkHIT>): List<MTurkHITDetails> {
        return runBlocking {
            val hitRequests = hits.map { hit ->
                async { handleHITRequest(hit) }
            }
            hitRequests.map { it.await() }
        }
    }

    /**
     * Handles request to create a HIT
     * 1. If it is rejected, extends it
     * 2. If not, checks if the answer given is correct
     * 3. If not, creates a new HIT
     * @return A new or updated (if existing) [MTurkHITDetails]
     */
    fun handleHITRequest(hit: MTurkHIT): MTurkHITDetails {
        try {
            // check if there is existing HITs found. If so, refetch the current details from MTurk if not approved already
            val existingHIT = mTurkDataSource.get(hit.shipImo, hit.portcallId)
            val refetchedHIT = if (existingHIT != null) {
                existingHIT.approvedAnswer?.let { return existingHIT } ?: updateAssignmentStatus(existingHIT.hitId)
            } else {
                null
            }

            return if (refetchedHIT != null) {
                handleExistingHIT(refetchedHIT)
            } else {
                handleNewHIT(hit)
            }
        } catch (ex: Exception) {
            log.error(ex) { "[MTurk] Error creating HIT." }
            throw PreconditionException("Error creating HIT. Error: ${ex.message}", hit.portcallId)
        }
    }

    /**
     * Handles request to create a HIT
     * @return A new [MTurkHITDetails]
     */
    private fun handleNewHIT(hit: MTurkHIT): MTurkHITDetails {
        // submit a new HIT request
        val hitResponse = client.createHIT(builtHIT(hit))
        // persis the response in the db
        val hitDetails = hitResponse.hit()
        client.updateNotificationSettings(
            UpdateNotificationSettingsRequest.builder()
                .hitTypeId(hitDetails.hitTypeId())
                .notification(getNotificationSpecification()).build(),
        )
        // add an activity counter
        activityDataSource.insert(Activity(setOf(hit.shipImo), ScheduledTaskType.MTURK))
        // add a fetch counter
        val fetchCounter = ServiceFetchCounter(Date(), ScheduledTaskType.MTURK)
        fetchCounter.create.add(ServiceFetchDetail(hit.portcallId, fetchCounter.timestamp))
        serviceFetchCounterDataSource.createOrUpdate(fetchCounter)

        // update the actual HIT created
        return mTurkDataSource.createOrUpdate(MTurkHITDetails(hit, hitDetails))
    }

    /**
     * Handles request to create a HIT
     * 1. If it is rejected, extends it
     * 2. If not, checks if the answer given is correct
     * @return An updated (if existing) [MTurkHITDetails]
     */
    private fun handleExistingHIT(hitDetails: MTurkHITDetails): MTurkHITDetails {
        // if overall status is rejected. create a new assignment
        return if (hitDetails.overallAssignmentStatus == REJECTED) {
            log(
                hitDetails.request.portcallId,
                hitDetails.request.shipImo,
                Portcall.IDPREFIX_ANR,
                "[MTurk] Extending assignments on HIT: ${hitDetails.hitId} as all previous assignments were REJECTED",
                Level.WARNING,
            )
            extendHIT(hitDetails)
        } else {
            // assess the current status of the assignments
            assessHITAssignment(hitDetails)
        }
    }

    fun getHITByImoAndPortcallAlias(
        shipImo: String,
        portcallId: String,
    ): MTurkHITDetails? {
        return mTurkDataSource.getByFiltered(shipImo, portcallId).firstOrNull()
    }

    fun getHITs(
        hitId: String?,
        assignmentId: String?,
        workerId: String?,
        shipImo: String?,
        portcallId: String?,
    ): List<MTurkHITDetails> {
        return runBlocking {
            val hitDetails = if (hitId != null) {
                mTurkDataSource.get(hitId)?.let { listOf(it) }
                    ?: run {
                        log.info { "No HIT found for: $hitId" }
                        throw PreconditionException("No HIT found for: $hitId")
                    }
            } else {
                mTurkDataSource.getByFiltered(shipImo, portcallId)
            }

            hitDetails.map { existingHITDetails ->
                async {
                    try {
                        val updatedHITDetails = updateAssignmentStatus(existingHITDetails)
                        if (assignmentId != null || workerId != null) {
                            val filteredAssignments = updatedHITDetails.assignments.filter {
                                (assignmentId == null || assignmentId == it.assignmentId) &&
                                    (workerId == null || workerId == it.workerId)
                            }
                            updatedHITDetails.copy(assignments = filteredAssignments)
                        } else {
                            updatedHITDetails
                        }
                    } catch (ex: Exception) {
                        log(
                            existingHITDetails.request.portcallId,
                            existingHITDetails.request.shipImo,
                            Portcall.IDPREFIX_ANR,
                            "[MTurk] No HIT found for: ${existingHITDetails.hitId}. Error: ${ex.message}",
                            Level.WARNING,
                        )
                        null
                    }
                }
            }.mapNotNull { it.await() }
        }
    }

    /**
     * Updates the overall assignment status of the given [hitId] based on MTurk interface
     */
    fun updateAssignmentStatus(hitId: String): MTurkHITDetails? {
        return mTurkDataSource.get(hitId)?.let { hitDetails ->
            updateAssignmentStatus(hitDetails)
        }
    }

    /**
     * Approves/rejects all assignments linked to the given [hitId]
     * @param force Re-assesses already assessed assignment when set to true
     * @return The approved answer if any is found. Else null
     */
    fun assessHITAssignment(
        hitId: String,
        force: Boolean = false,
    ): MTurkHITDetails? {
        // fetch the current state of the assignment via MTurk
        return updateAssignmentStatus(hitId)?.let {
            assessHITAssignment(it, force)
        }
    }

    fun sendTestEvent(eventType: EventType) {
        client.sendTestEventNotification(
            SendTestEventNotificationRequest.builder()
                .notification(getNotificationSpecification())
                .testEventType(eventType)
                .build(),
        )
    }

    fun getEventFromNotification(notificationMessage: String): MTurkEventCallback? {
        return if (notificationMessage.contains("\"Type\" : \"Notification\"")) {
            val eventCallback = extractEventFromNotification(notificationMessage)
            readFromString(eventCallback, MTurkEventCallback::class.java)
        } else {
            readFromString(notificationMessage, MTurkEventCallback::class.java)
        }
    }

    /**
     * Forces the HIT to be expired as of now, so no more assignments will be submitted on it
     */
    fun expireHITs(hitIds: Set<String>) {
        runBlocking {
            hitIds.map { hitId ->
                launch { expireHIT(hitId) }
            }
        }
    }

    /**
     * Updates the overall assignment status of the given [hitDetails] based on MTurk interface
     */
    private fun updateAssignmentStatus(hitDetails: MTurkHITDetails): MTurkHITDetails {
        try {
            // fetch HIT details if its not expired yet
            if (hitDetails.expired) return hitDetails

            val hit = client.getHIT(GetHitRequest.builder().hitId(hitDetails.hitId).build()).hit()
            var updatedHITDetails = hitDetails.updateWith(hit)

            // fetch assignments for the HIT
            val assignments = client.listAssignmentsForHIT(
                ListAssignmentsForHitRequest.builder().hitId(hitDetails.hitId).build(),
            ).assignments()

            if (assignments.isNotEmpty()) {
                val responses = assignments.map {
                    MTurkHITResponse(it)
                }
                val overallAssignmentStatus = when {
                    responses.all { it.status == REJECTED } -> REJECTED
                    responses.any { it.status == APPROVED } -> APPROVED
                    responses.any { it.status == SUBMITTED } -> SUBMITTED
                    else -> UNKNOWN_TO_SDK_VERSION
                }
                // Updating assignments to APPROVED status once the overalLStatus is set to APPROVED and the answer matches the approved answer
                // Assuming here that there's only 1 correct answer, we simply find the first one
                approveCorrectAnswers(overallAssignmentStatus, hitDetails, responses)
                updatedHITDetails = updatedHITDetails.copy(assignments = responses, overallAssignmentStatus = overallAssignmentStatus)
            }
            // update HIT document if there is a change
            if (hitDetails != updatedHITDetails) {
                return mTurkDataSource.createOrUpdate(updatedHITDetails)
            }
            if (updatedHITDetails.assignments.size > mTurkProperties.maxAssignments / 2 &&
                updatedHITDetails.assignments.all { it.status == REJECTED || it.status == APPROVED }
            ) {
                expireHIT(updatedHITDetails.hitId)
                deleteHIT(updatedHITDetails.hitId)
            }
            return updatedHITDetails
        } catch (ex: Exception) {
            log.error(ex) { "[MTurk] Error fetching HIT" }
            throw PreconditionException("Error fetching HIT. Error: ${ex.message}", hitDetails.hitId)
        }
    }

    fun scheduleFetchSubmittedAnswersOnApprovedHITS() {
        if (isEnabled) {
            val listHits = mTurkDataSource.getOverallApprovedAndSubmittedAnswers(
                date = Date.from(Instant.now().minus(Duration.ofDays(daysConfigured))),
            )
            log.info { "Fetched records and found (${listHits.size}) HITS to be updated" }
            listHits.forEach {
                log.info { "Found hitId ${it.hitId}) with a SUBMITTED answer and checking if needed to approve" }
                approveCorrectAnswers(overallAssignmentStatus = it.overallAssignmentStatus, hitDetails = it, responses = it.assignments)
            }
        }
    }

    /**
     * Method to process the pending to process HITs or the given set of [hitIds].
     */
    fun processHits(hitIds: Set<String>? = null) {
        if (!isEnabled) {
            log.info { "MTurk is not enabled. Not processing anything." }
            return
        }
        if (!pullAssignments) {
            log.info { "MTurk pulling is not enabled. Not processing anything." }
            return
        }
        val hitsToProcess = hitIds?.let {
            mTurkDataSource.getAsSequence(hitIds)
        } ?: mTurkDataSource.getPendingHitsToProcessAsSequence()
        val hitsAmount = hitsToProcess.count()
        log.info { "Processing $hitsAmount HITs." }
        // Let's get all agents in the system key-ed in a map by only their alphanumeric name.
        val vesselAgentsByFormattedKey = if (hitsToProcess.any()) {
            portcallDataSource.getVesselAgentsByFormattedKey()
        } else {
            emptyMap()
        }
        hitsToProcess.forEach {
            log.info { "Processing HIT ${it.hitId}" }
            it.process(vesselAgentsByFormattedKey)
        }
        log.info { "Hits processed $hitsAmount." }
    }

    /**
     * Main method to process the subjected MTurkHITDetails.
     * For speeding purposes, [vesselAgentsByFormattedKey] map can be provided, containing the existing agents in the
     * system, key-ed by their alphanumeric name (see method [removeSpecialChars]).
     */
    private fun MTurkHITDetails.process(vesselAgentsByFormattedKey: Map<String, String>) {
        // Get updated from Amazon (fetch).
        val updateHitDetails = this.fetchMTurkData()
        if (updateHitDetails.disappearedInAmazon == true) {
            mTurkDataSource.createOrUpdate(updateHitDetails)
            return
        }
        // Process assignments/responses (including approving/rejecting assignments).
        val (processedAssignmentsHitDetails, validAgentFound) = processAssignments(
            updateHitDetails,
            vesselAgentsByFormattedKey,
        )
        // Update portcall with answers.
        val fullyProcessedHitDetails = processedAssignmentsHitDetails.setPortcallAgent(validAgentFound)
        // Update MTurkDetails with MTurk, if we're done (portcall doesn't exist, it's agent was set already or new agent was set).
        if (fullyProcessedHitDetails.expired) {
            expireHIT(fullyProcessedHitDetails.hitId)
            // Mainly for debugging purposes: to review HIT assignments once they're done.
            if (disposeAfterExpiring) {
                deleteHIT(fullyProcessedHitDetails.hitId)
            }
        }
        // Persist the processed HITDetails.
        updateHitDetails(fullyProcessedHitDetails)
    }

    fun updateHitDetails(hitToUpdate: MTurkHITDetails): MTurkHITDetails {
        return mTurkDataSource.createOrUpdate(hitToUpdate)
    }

    /**
     * Auxiliary method, used to modularize the main method [process].
     * It returns the given [MTurkHITDetails] (including its assignments) from Amazon MTurk.
     * Note that:
     * - It doesn't persist the [MTurkHITDetails]!
     * - The HIT is not updated in MTurk, only in memory!
     */
    private fun MTurkHITDetails.fetchMTurkData(): MTurkHITDetails {
        val amazonHIT = runCatching {
            val hitRequest = GetHitRequest.builder().hitId(this.hitId).build()
            client.getHIT(hitRequest).hit()
        }.onFailure {
            log.warn { "Failed to fetch the HIT ${this.hitId} details from amazon." }
        }.getOrNull()
        return if (amazonHIT != null) {
            val assignments = runCatching {
                val hitAssignmentsRequest = ListAssignmentsForHitRequest.builder().hitId(this.hitId).build()
                client.listAssignmentsForHIT(hitAssignmentsRequest).assignments()
            }.onFailure {
                log.warn { "Failed to fetch the HIT ${this.hitId} assignments from amazon." }
            }.getOrNull()
                ?.map { MTurkHITResponse(it) }
                ?: this.assignments
            this.updateWith(amazonHIT).copy(assignments = assignments)
        } else {
            log.warn { "The ${this.hitId} doesn't exist in MTurk anymore!" }
            this.copy(disappearedInAmazon = true)
        }
    }

    /**
     * Auxiliary method, used to modularize the main method [process].
     * It processes the given [MTurkHITDetails] assignments, approving or rejecting the submitted ones.
     * Those in other states (APPROVED, REJECTED or UNKNOWN_TO_SDK_VERSION) are not processed.
     * It returns the updated [MTurkHITDetails] (included its assignments) together with the first valid agent found (if any).
     * Note that:
     * - It doesn't persist the [MTurkHITDetails]!
     * - The HIT is not updated in MTurk, only in memory!
     * - The HIT's assignments are updated in MTurk!
     */
    fun processAssignments(
        mTurkHITDetails: MTurkHITDetails,
        vesselAgentsByFormattedKey: Map<String, String>,
    ): Pair<MTurkHITDetails, String?> {
        val processedAssignments = mTurkHITDetails.assignments.map { itAssignment ->
            val hitKey = "HIT (${mTurkHITDetails.hitId}) assignment (${itAssignment.assignmentId})"
            if (itAssignment.status == SUBMITTED) {
                log.debug {
                    "$hitKey answer: ${itAssignment.answer}.\nParsed answer: ${itAssignment.parsedAnswer}.\nAgent key: ${itAssignment.formattedAnswer}"
                }
                // Getting the agent by the agent's key
                val validAgentFound = itAssignment.formattedAnswer?.let { vesselAgentsByFormattedKey[it] }
                log.info { "Valid agent found: $validAgentFound" }
                // Any valid answer (or invalid, but 'approveInvalidAnswers' is true) will be approved (and paid).
                if (validAgentFound != null || approveInvalidAnswers) {
                    // let's not forget to update the in-memory assignment
                    itAssignment.approveAssignment("Thanks for your contribution!")
                        ?: itAssignment
                } else {
                    itAssignment.rejectAssignment("The reported answer is invalid.")
                        ?: itAssignment
                }
            } else { // It was already APPROVED, REJECTED or UNKNOWN_TO_SDK_VERSION, so do nothing, but log it.
                log.debug { "$hitKey status: ${itAssignment.status} (not SUBMITTED), so doing nothing." }
                itAssignment
            }
        }
        val updatedMTurkHITDetails = mTurkHITDetails.copy(assignments = processedAssignments)
        val validAgents = processedAssignments
            .sortedBy { it.submitTime }
            .mapNotNull { it.formattedAnswer?.let { vesselAgentsByFormattedKey[it] } }
        return updatedMTurkHITDetails to validAgents.firstAnswerReaching(minimumSameValidAnswers)
    }

    /**
     * Auxiliary method to approve a given [MTurkHITResponse] assignment.
     * It returns the updated MTurkHITResponse accordingly if success.
     */
    private fun MTurkHITResponse.approveAssignment(feedbackMessage: String): MTurkHITResponse? {
        val approveAssignmentRequest = ApproveAssignmentRequest.builder()
            .assignmentId(this.assignmentId).requesterFeedback(feedbackMessage).build()
        return runCatching { client.approveAssignment(approveAssignmentRequest) }
            .onSuccess {
                log.info { "Assignment ${this.assignmentId} approved!!" }
            }.onFailure {
                log.warn(it) { "Approving HIT assignment ${this.assignmentId} failed." }
            }.getOrNull()?.let {
                this.copy(status = APPROVED, approvalTime = Date(), isRejected = false, feedback = feedbackMessage)
            }
    }

    /**
     * Auxiliary method to reject a given [MTurkHITResponse] assignment.
     * It returns the updated MTurkHITResponse accordingly if success.
     */
    private fun MTurkHITResponse.rejectAssignment(feedbackMessage: String): MTurkHITResponse? {
        val rejectAssignmentRequest = RejectAssignmentRequest.builder()
            .assignmentId(this.assignmentId).requesterFeedback(feedbackMessage).build()
        return runCatching { client.rejectAssignment(rejectAssignmentRequest) }
            .onSuccess {
                log.info { "Assignment ${this.assignmentId} rejected!" }
            }.onFailure {
                log.warn(it) { "Rejecting assignment ${this.assignmentId} failed!" }
            }.getOrNull()?.let {
                this.copy(status = REJECTED, isRejected = true, feedback = feedbackMessage)
            }
    }

    fun List<String>.firstAnswerReaching(count: Int): String? {
        val answerCount = mutableMapOf<String, Int>()
        this.forEach {
            answerCount[it] = (answerCount[it] ?: 0) + 1
            if (answerCount[it] == count) {
                return it
            }
        }
        return null
    }

    /**
     * Auxiliary method, used to modularize the main method [process].
     * It sets agent (vesselAgent) to the related Portcall, according to the [MTurkHITDetails.request].portcallId (alias)
     * if it wasn't set already.
     * It returns the updated [MTurkHITDetails].
     * Note that:
     * - It doesn't persist the [MTurkHITDetails]!
     * - The [MTurkHITDetails.expired] gets updated accordingly indicating that condition.
     * - The HIT is not updated in MTurk, only in memory!
     */
    private fun MTurkHITDetails.setPortcallAgent(validAgentFound: String?): MTurkHITDetails {
        // let's evaluate an odd case: the portcall disappeared
        val alias = this.request.portcallId
        val portcall = portcallDataSource.searchByAlias(alias, PortcallAliasName.NXTPORT).firstOrNull()
        val updatedMTurkHITDetails = if (portcall == null) {
            // let's expire and delete the hit in amazon (so we don't get more responses)
            log.warn { "The related portcall by alias ($alias) wasn't found! Did the alias change?" }
            this.copy(expired = true)
        } else if (!portcall.vesselAgent.isNullOrEmpty()) {
            log.info { "The related portcall (${portcall.portcallId}) by alias ($alias) has already an agent set. No need to set." }
            this.copy(expired = true)
        } else if (validAgentFound != null) {
            // let's update the agent
            val updatedPortcall = portcallDataSource.createOrUpdate(portcall.copy(vesselAgent = validAgentFound))
            eventService.onUpdatedPortcall(currentPortcall = portcall, updatedPortcall = updatedPortcall)
            log.info { "Portcall ${portcall.portcallId}'s agent was set to $validAgentFound by HIT ${this.hitId}." }
            // Let's mark it here as expired to later expire the HIT if needed in a next stage.
            this.copy(expired = true, approvedAnswer = validAgentFound)
        } else {
            log.info { "No valid agent found. Not setting portcall." }
            this
        }
        portcall?.let { addServiceCounter(it.portcallId, validAgentFound, it.vesselAgent) }
        return updatedMTurkHITDetails
    }

    fun estimateHITsProcessing() {
        if (!isEnabled) {
            log.info { "MTurk is not enabled. Not processing anything." }
            return
        }
        log.info { "Estimating HITs processing." }
        val hitsToProcess = mTurkDataSource.getPendingHitsToProcessAsSequence()
        val vesselAgentsByFormattedKey = if (hitsToProcess.any()) {
            portcallDataSource.getVesselAgentsByFormattedKey()
        } else {
            emptyMap()
        }
        var disappearedInAmazon = 0
        var portcallsToUpdate = 0
        var assignmentsToApprove = 0
        var hitsToExpire = 0
        hitsToProcess.forEach {
            log.info { "Estimating HIT processing ${it.hitId}" }
            val (estimatedHITProcessing, toBeApproved, agentSet) = it.estimateProcessing(vesselAgentsByFormattedKey)
            if (estimatedHITProcessing.disappearedInAmazon == true) {
                disappearedInAmazon++
            }
            assignmentsToApprove += toBeApproved
            if (agentSet) {
                portcallsToUpdate++
            }
            if (!it.expired && estimatedHITProcessing.expired) {
                hitsToExpire++
            }
        }
        log.info {
            "HITs processing estimated.\n" +
                "HITQty: ${hitsToProcess.count()}.\n" +
                "HITs disappearedInAmazon: $disappearedInAmazon\n" +
                "HIT assignments to approve: $assignmentsToApprove\n" +
                "HITs to expire: $hitsToExpire\n" +
                "Portcalls to update: $portcallsToUpdate."
        }
    }

    /**
     * Returning
     * - The up-to-date memory object estimated MTurkHITDetails
     * - Amount of estimated approvals
     * - Whether the linked portcall would be updated.
     */
    private fun MTurkHITDetails.estimateProcessing(
        vesselAgentsByFormattedKey: Map<String, String>? = null,
    ): Triple<MTurkHITDetails, Int, Boolean> {
        val updateHitDetails = this.fetchMTurkData()
        if (updateHitDetails.disappearedInAmazon == true) {
            return Triple(updateHitDetails, 0, false)
        }
        val (processedAssignmentsHitDetails, validAgentFound, approved) = updateHitDetails.estimateProcessAssignments(
            vesselAgentsByFormattedKey,
        )
        val (fullyProcessedHitDetails, agentSet) = processedAssignmentsHitDetails.estimatePortcallAgentSet(validAgentFound)
        return Triple(fullyProcessedHitDetails, approved, agentSet)
    }

    /**
     * Returning
     * - The up-to-date memory object estimated MTurkHITDetails
     * - The 1st valid answer provided.
     * - Amount of estimated approvals
     */
    private fun MTurkHITDetails.estimateProcessAssignments(
        vesselAgentsByFormattedKey: Map<String, String>? = null,
    ): Triple<MTurkHITDetails, String?, Int> {
        // In case it's not provided.
        val vesselAgentsByFormattedKey = vesselAgentsByFormattedKey ?: portcallDataSource.getVesselAgentsByFormattedKey()
        var firstValidAgentFound: String? = null
        var approved = 0
        val processedAssignments = this.assignments.map { itAssignment ->
            val hitKey = "HIT (${this.hitId}) assignment (${itAssignment.assignmentId})"
            if (itAssignment.status == SUBMITTED) {
                log.debug {
                    "$hitKey answer: ${itAssignment.answer}.\nParsed answer: ${itAssignment.parsedAnswer}.\nAgent key: ${itAssignment.formattedAnswer}"
                }
                // Getting the agent by the agent's key
                val validAgentFound = itAssignment.formattedAnswer?.let { vesselAgentsByFormattedKey[it] }
                log.info { "Valid agent found: $validAgentFound" }
                // let's set the 1st valid answer!
                if (validAgentFound != null && firstValidAgentFound == null) {
                    firstValidAgentFound = validAgentFound
                }
                // Any valid answer (or invalid, but 'approveInvalidAnswers' is true) will be approved (and paid).
                if (validAgentFound != null || approveInvalidAnswers) {
                    // let's set a different feedback based on the answer.
                    val feedback = when {
                        validAgentFound != null -> "Valid answer given: $validAgentFound. Thank you!"
                        else -> "Thanks for your contribution!"
                    }
                    // let's not forget to update the in-memory assignment
                    approved++
                    itAssignment.copy(status = APPROVED, approvalTime = Date(), isRejected = false, feedback = feedback)
                } else {
                    itAssignment.copy(status = REJECTED, isRejected = true, feedback = "The reported answer is invalid.")
                }
            } else { // It was already APPROVED, REJECTED or UNKNOWN_TO_SDK_VERSION, so do nothing, but log it.
                log.debug { "$hitKey status: ${itAssignment.status} (not SUBMITTED), so doing nothing." }
                itAssignment
            }
        }
        val updatedMTurkHITDetails = this.copy(assignments = processedAssignments)
        return Triple(updatedMTurkHITDetails, firstValidAgentFound, approved)
    }

    /**
     * Returning
     * - The up-to-date memory object estimated MTurkHITDetails
     * - Whether the linked portcall would be updated.
     */
    private fun MTurkHITDetails.estimatePortcallAgentSet(validAgentFound: String?): Pair<MTurkHITDetails, Boolean> {
        // let's evaluate an odd case: the portcall disappeared
        val alias = this.request.portcallId
        val portcall = portcallDataSource.searchByAlias(alias, PortcallAliasName.NXTPORT).firstOrNull()
        return if (portcall == null) {
            // let's expire and delete the hit in amazon (so we don't get more responses)
            log.warn { "The related portcall (${this.request.portcallId}) by alias ($alias) for the wasn't found! Did the alias change?" }
            this.copy(expired = true) to false
        } else if (!portcall.vesselAgent.isNullOrEmpty()) {
            log.info { "The related portcall (${this.request.portcallId}) by alias ($alias) has already an agent set. No need to set." }
            this.copy(expired = true) to false
        } else if (validAgentFound != null) {
            // let's update the agent
            log.info { "Portcall ${portcall.portcallId}'s agent was set to $validAgentFound by HIT ${this.hitId}." }
            // Let's mark it here as expired to later expire the HIT if needed in a next stage.
            this.copy(expired = true, approvedAnswer = validAgentFound) to true
        } else {
            log.info { "No valid agent found. Not setting portcall." }
            this to false
        }
    }

    private fun approveCorrectAnswers(
        overallAssignmentStatus: AssignmentStatus,
        hitDetails: MTurkHITDetails,
        responses: List<MTurkHITResponse>,
    ) {
        if (overallAssignmentStatus != APPROVED) {
            log.warn { "overallAssignmentStatus needs to be approved to continue, return early and do nothing if it is not approved." }
            return
        }

        if (overallAssignmentStatus == APPROVED) {
            log.info {
                "[MTurk] overallAssignmentStatus is approved for ${hitDetails.hitId} " +
                    "the assignments: ${responses.map { it.assignmentId to it.status }} will be sent to the database"
            }
            // Checking if an answer is not approved and if all answers are the same
            // If found then we update the unapproved answer with approved
            // Not catching the .first() call since the overallAssignmentStatus is already Approved
            // which came from a previous APPROVED filter
            if (!responses.all { it.status == APPROVED }) {
                val firstAssignmentFound = responses.first { it.status == APPROVED }
                val correctAnswer = parseAnswer(firstAssignmentFound.answer ?: "")
                log.info {
                    "[MTurk] updating all assignments for HIT id ${hitDetails.hitId} since not all assignments are approved: ${responses.map { it.assignmentId to it.status }}"
                }
                val updatedAssignments = mutableListOf<MTurkHITResponse>()
                responses.forEach { itAssignment ->
                    val tempAnswer = parseAnswer(itAssignment.answer ?: "")
                    if (itAssignment.status != APPROVED && tempAnswer == correctAnswer) {
                        updatedAssignments.add(itAssignment.copy(status = APPROVED))
                    } else {
                        updatedAssignments.add(itAssignment)
                    }
                    try {
                        manuallyApprove(assignmentId = itAssignment.assignmentId, overrideRejection = false)
                    } catch (e: PreconditionException) {
                        // Logging this as manually approve might raise an error from MTurk due to already approved or not allowed to be approved
                        // But since we need to update the HIT afterwards, we log this as it might've exited the function if we don't
                        log.info { "[MTurk] PreconditionException assignmentId ${itAssignment.assignmentId} cannot be manually approved" }
                    }
                }
                mTurkDataSource.createOrUpdate(hitDetails.copy(assignments = updatedAssignments))
            }
        }
    }

    /**
     * Forces the HIT to be expired as of now, so no more assignments will be submitted on it
     */
    internal fun expireHIT(hitId: String) {
        try {
            client.updateExpirationForHIT(
                UpdateExpirationForHitRequest
                    .builder()
                    .hitId(hitId)
                    .expireAt(Instant.now().minus(10, ChronoUnit.MINUTES))
                    .build(),
            )
            log.info { "HIT $hitId set as expired in MTurk!" }
        } catch (ex: Exception) {
            log(message = "[MTurk] Error expiring HIT: $hitId. Error: ${ex.message}", level = Level.SEVERE)
        }
    }

    private val client: MTurkClient by lazy {
        if (isEnabled) {
            val credentials = StaticCredentialsProvider.create(
                AwsBasicCredentials.create(mTurkProperties.accessKeyId, mTurkProperties.secretAccessKey),
            )
            MTurkClient.builder()
                .region(Region.US_EAST_1)
                .endpointOverride(URI.create(mTurkProperties.uri))
                .credentialsProvider(credentials)
                .build()
        } else {
            log.info { "MTurk is not enabled in the config" }
            throw PreconditionException("MTurk is not enabled in the config")
        }
    }

    private fun getNotificationSpecification() =
        NotificationSpecification.builder()
            .eventTypes(EventType.ASSIGNMENT_SUBMITTED)
            .transport(NotificationTransport.SNS)
            .destination(mTurkProperties.snsCallbackDestination)
            .version("2014-08-15")
            .build()

    /**
     * Approves/rejects all assignments linked to the given [hitDetails]
     * @param force Re-assesses already assessed assignment when set to true
     * @return The approved answer if any is found. Else null
     */
    private fun assessHITAssignment(
        hitDetails: MTurkHITDetails,
        force: Boolean = false,
    ): MTurkHITDetails {
        var correctAnswer: String? = hitDetails.approvedAnswer
        val portcall =
            portcallDataSource.searchByAlias(hitDetails.request.portcallId, PortcallAliasName.NXTPORT).firstOrNull()
                ?: run {
                    log.info { "[MTurk] Portcall not found" }
                    throw PreconditionException("Portcall not found", hitDetails.request.portcallId)
                }
        log.info { "assessHITAssignment: $hitDetails" }
        hitDetails.assignments.forEach { assignment ->

            val isNewSubmission = force || (
                assignment.approvalTime == null && !assignment.isRejected &&
                    assignment.submitTime != null && assignment.answer != null
                )

            // assess the assignment if its a new submission
            if (isNewSubmission) {
                correctAnswer = assessHITAssignment(hitDetails, portcall, assignment.assignmentId, assignment.answer!!)
            }
        }
        return if (correctAnswer != hitDetails.approvedAnswer) {
            mTurkDataSource.createOrUpdate(hitDetails.copy(approvedAnswer = correctAnswer))
        } else {
            hitDetails
        }
    }

    /**
     * Approves/rejects the given [assignmentId] based on the [answer] corresponding to [hitDetails]
     * @return The approved answer if any is found. Else null
     */
    private fun assessHITAssignment(
        hitDetails: MTurkHITDetails,
        portcall: Portcall,
        assignmentId: String,
        answer: String,
    ): String? {
        val parsedAnswer = parseAnswer(answer)
        if (parsedAnswer != null) {
            val portcallId = hitDetails.request.portcallId
            val shipImo = hitDetails.request.shipImo
            val hitId = hitDetails.hitId

            var correctAnswer: String? = null
            val formattedAnswer = removeSpecialChars(parsedAnswer)
            var vesselAgent = portcall.vesselAgent
            val formattedVesselAgent = vesselAgent?.let { removeSpecialChars(it) }

            // check if agent is valid (if a vesselAgent is seen in NxtPort portcall, then compare)
            val isAgentValid = if (formattedVesselAgent != null) {
                formattedVesselAgent == formattedAnswer
            }
            // else just check if the vesselAgent has a matching entry in the previous portcalls
            else {
                val formattedVesselAgents = portcallDataSource.getVesselAgentsByFormattedKey()
                vesselAgent = formattedVesselAgents[formattedAnswer]
                formattedVesselAgents.containsKey(formattedAnswer)
            }

            try {
                if (isAgentValid) {
                    client.approveAssignment(
                        ApproveAssignmentRequest.builder().assignmentId(assignmentId)
                            .requesterFeedback("Valid answer given: $parsedAnswer. Thank you!")
                            .build(),
                    )
                    log(
                        portcallId,
                        shipImo,
                        Portcall.IDPREFIX_ANR,
                        "Answer $parsedAnswer on hitId: $hitId is auto-approved for $assignmentId",
                    )
                    correctAnswer = parsedAnswer

                    // update the portcall with the correct vesselAgent
                    if (portcall.vesselAgent == null) {
                        log(portcallId, shipImo, Portcall.IDPREFIX_ANR, "Updated vesselAgent: $correctAnswer")
                        portcallDataSource.createOrUpdate(portcall.copy(vesselAgent = correctAnswer))
                    }
                    // expire the HIT so more assignments cannot be submitted
                    expireHIT(hitId)
                } else {
                    val correctAnswerMessage = (vesselAgent ?: correctAnswer)?.let { "Correct answer is: $it" } ?: ""
                    val feedback = "Invalid answer given: $parsedAnswer. $correctAnswerMessage"
                    if (mTurkProperties.autoReject) {
                        client.rejectAssignment(
                            RejectAssignmentRequest.builder().assignmentId(assignmentId).requesterFeedback(feedback)
                                .build(),
                        )
                    }
                    log(
                        portcallId,
                        shipImo,
                        Portcall.IDPREFIX_ANR,
                        "[MTurk] $feedback on hitId: $hitId is auto-rejected: ${mTurkProperties.autoReject}",
                    )
                }
            } catch (ex: Exception) {
                log.info { "[MTurk] Error assessing HIT for: $hitId. Error: ${ex.message}" }
                throw PreconditionException(
                    "Error assessing HIT for: $hitId. Error: ${ex.message}",
                    portcallId,
                )
            }
            // add a fetch counter
            addServiceCounter(portcall.portcallId, correctAnswer, vesselAgent)
            return correctAnswer
        }
        return null
    }

    fun addServiceCounter(
        portcallId: String,
        correctAnswer: String?,
        vesselAgent: String?,
    ): ServiceFetchCounter {
        val fetchCounter = ServiceFetchCounter(Date(), ScheduledTaskType.MTURK)
        val formattedCorrectAnswer = correctAnswer?.let { removeSpecialChars(it) }
        val formattedVesselAgent = vesselAgent?.let { removeSpecialChars(it) }

        if (formattedCorrectAnswer?.isNotBlank() == true) {
            if (formattedVesselAgent != null && formattedVesselAgent != formattedCorrectAnswer) {
                fetchCounter.failedImoNumbers.add(portcallId)
            } else {
                fetchCounter.agentUpdate.add(portcallId)
            }
        } else {
            if (mTurkProperties.autoReject) {
                fetchCounter.noUpdates.add(portcallId)
            } else {
                fetchCounter.emptyResult.add(portcallId)
            }
        }
        serviceFetchCounterDataSource.createOrUpdate(fetchCounter)
        return fetchCounter
    }

    /**
     * Creates a new HIT request to MTurk
     */
    private fun builtHIT(hit: MTurkHIT): CreateHitRequest {
        log.info {
            "[MTurk] creating HIT with durationInSeconds ${mTurkProperties.assignmentDuration.seconds} and lifetimeInSeconds " +
                "${mTurkProperties.assignmentLifetime.seconds} and autoApprovalDelay ${mTurkProperties.autoApprovalDelay.seconds}"
        }
        return CreateHitRequest.builder()
            .hitLayoutId(mTurkProperties.hitLayoutId)
            .reward("0.01")
            .title("Collect the agent name from a website")
            .description("Fill in the Ship name on the given website and copy-paste the Agent name that's returned")
            .assignmentDurationInSeconds(mTurkProperties.assignmentDuration.seconds)
            .lifetimeInSeconds(mTurkProperties.assignmentLifetime.seconds)
            .autoApprovalDelayInSeconds(mTurkProperties.autoApprovalDelay.seconds)
            .maxAssignments(mTurkProperties.maxAssignments)
            .hitLayoutParameters(
                HITLayoutParameter.builder().name("imoNumber").value(hit.shipImo).build(),
                HITLayoutParameter.builder().name("portcallId").value(hit.portcallId).build(),
            )
            .build()
    }

    /**
     * Extends an existing HIT with more assignments
     */
    private fun extendHIT(hitDetails: MTurkHITDetails): MTurkHITDetails {
        val portcallId = hitDetails.request.portcallId
        val imoNumber = hitDetails.request.shipImo
        val extendBy = mTurkProperties.maxAssignments - 1
        try {
            log(portcallId, imoNumber, Portcall.IDPREFIX_ANR, "Extending HIT: ${hitDetails.hitId} by $extendBy")
            client.createAdditionalAssignmentsForHIT(
                CreateAdditionalAssignmentsForHitRequest.builder()
                    .hitId(hitDetails.hitId)
                    .numberOfAdditionalAssignments(extendBy)
                    .build(),
            )
            return mTurkDataSource.createOrUpdate(hitDetails.copy(overallAssignmentStatus = UNKNOWN_TO_SDK_VERSION))
        } catch (ex: Exception) {
            log.info { "[MTurk] Error extending HIT: ${hitDetails.hitId}. Error: ${ex.message}" }
            throw PreconditionException("Error extending HIT: ${hitDetails.hitId}. Error: ${ex.message}", portcallId)
        }
    }

    fun getHtmlStatus(
        status: HitStatus?,
        onlyAvailableTasks: Boolean,
        onlyAllTasksCompleted: Boolean,
        onlyExpiredHITs: Boolean,
    ): String {
        val hits = listHits(status, onlyAvailableTasks, onlyAllTasksCompleted, onlyExpiredHITs)
        return "<html>" +
            "<body>" +
            "<table width=\"100%\" style=\"text-align:center;\">" +
            getHtmlHeadOfHit() +
            hits.joinToString { getHtmlRowOfHit(it) } +
            "</table>" +
            "</body>" +
            "</html>"
    }

    private fun getHtmlHeadOfHit() =
        "<thead>" +
            "<tr>" +
            "<th>Id</th>" +
            "<th>Layout Id</th>" +
            "<th>Group Id</th>" +
            "<th>Type Id</th>" +
            "<th>Status</th>" +
            "<th>Duration (sec)</th>" +
            "<th>Auto approval delay (sec)</th>" +
            "<th>Creation Time</th>" +
            "<th>Description</th>" +
            "<th>Expiration</th>" +
            "<th>Review Status</th>" +
            "<th>Keywords</th>" +
            "<th>Max Assignments</th>" +
            "<th>Available</th>" +
            "<th>Completed</th>" +
            "<th>Pending</th>" +
            "<th>Requester Annotation</th>" +
            "<th>Reward</th>" +
            "<th>Title</th>" +
            "</tr>" +
            "</thead>"

    private fun getHtmlRowOfHit(hit: HIT): String {
        return "<tr>" +
            "<td style=\"font-size:50%\">${hit.hitId}</td>" +
            "<td style=\"font-size:50%\">${hit.hitLayoutId}</td>" +
            "<td style=\"font-size:50%\">${hit.hitGroupId}</td>" +
            "<td style=\"font-size:50%\">${hit.hitTypeId}</td>" +
            "<td>${hit.hitStatus}</td>" +
            "<td>${hit.assignmentDurationInSeconds}</td>" +
            "<td>${hit.autoApprovalDelayInSeconds}</td>" +
            "<td>${hit.creationTime}</td>" +
            "<td>${hit.description}</td>" +
            "<td>${hit.expiration}</td>" +
            "<td>${hit.hitReviewStatus}</td>" +
            "<td>${hit.keywords}</td>" +
            "<td>${hit.maxAssignments}</td>" +
            "<td>${hit.numberOfAssignmentsAvailable}</td>" +
            "<td>${hit.numberOfAssignmentsCompleted}</td>" +
            "<td>${hit.numberOfAssignmentsPending}</td>" +
            "<td>${hit.requesterAnnotation}</td>" +
            "<td>${hit.reward}</td>" +
            "<td>${hit.title}</td>" +
            "</tr>"
    }

    /**
     * Deletes the HIT, can only be done after it has been expired or all answers have been given and Accepted/Rejected
     */
    fun deleteHITs(hitIds: Set<String>) {
        runBlocking {
            hitIds.map { hitId ->
                launch { deleteHIT(hitId) }
            }
        }
    }

    /**
     * Deletes the HIT, can only be done after it has been expired or all answers have been given and Accepted/Rejected
     */
    internal fun deleteHIT(hitId: String) {
        try {
            client.deleteHIT(
                DeleteHitRequest
                    .builder()
                    .hitId(hitId)
                    .build(),
            )
            log.info { "HIT $hitId was disposed in MTurk!" }
        } catch (ex: Exception) {
            log(message = "[MTurk] Error deleting HIT: $hitId. Error: ${ex.message}", level = Level.SEVERE)
        }
    }

    fun getHITAnswers(hitId: String): List<Assignment> {
        return try {
            client.listAssignmentsForHIT(
                ListAssignmentsForHitRequest
                    .builder()
                    .hitId(hitId)
                    .build(),
            ).assignments().map { it.convert() }
        } catch (ex: Exception) {
            log(message = "[MTurk] Error getting HIT answers: $hitId. Error: ${ex.message}", level = Level.SEVERE)
            throw PreconditionException("[MTurk] Error getting HIT answers: $hitId. Error: ${ex.message}")
        }
    }

    fun manuallyApprove(
        assignmentId: String,
        overrideRejection: Boolean,
    ) {
        try {
            client.approveAssignment(
                ApproveAssignmentRequest
                    .builder()
                    .overrideRejection(overrideRejection)
                    .assignmentId(assignmentId)
                    .build(),
            )
        } catch (ex: Exception) {
            log(
                message = "[MTurk] Error approving assignment: $assignmentId. Error: ${ex.message}",
                level = Level.SEVERE,
            )
            throw PreconditionException("[MTurk] Error approving assignment. Error: ${ex.message}")
        }
    }

    fun manuallyReject(assignmentId: String) {
        try {
            client.rejectAssignment(
                RejectAssignmentRequest
                    .builder()
                    .assignmentId(assignmentId)
                    .build(),
            )
        } catch (ex: Exception) {
            log(
                message = "[MTurk] Error rejecting assignment: $assignmentId. Error: ${ex.message}",
                level = Level.SEVERE,
            )
            throw PreconditionException("[MTurk] Error rejecting assignment. Error: ${ex.message}")
        }
    }

    fun getBlockedWorkers(maxResults: Int): List<WorkerBlock> {
        return try {
            client.listWorkerBlocks(
                ListWorkerBlocksRequest
                    .builder()
                    .maxResults(maxResults)
                    .build(),
            ).workerBlocks().map { it.convert() }
        } catch (ex: Exception) {
            log(
                message = "[MTurk] Error getting Blocker workers. Error: ${ex.message}",
                level = Level
                    .SEVERE,
            )
            throw PreconditionException("[MTurk] Error getting Blocker workers. Error: ${ex.message}")
        }
    }

    fun notifyWorkers(workersNotification: WorkersNotification) {
        try {
            client.notifyWorkers(
                NotifyWorkersRequest
                    .builder()
                    .workerIds(workersNotification.workerIds)
                    .subject(workersNotification.subject)
                    .messageText(workersNotification.message)
                    .build(),
            )
        } catch (ex: Exception) {
            log(
                message = "[MTurk] Error notifying users with: subject:$workersNotification.subject; " +
                    "message:$workersNotification.message" +
                    "Error: ${ex.message}",
                level = Level.SEVERE,
            )
        }
    }

    /**
     * Returns all HITs in the Reviewable state.
     * Note: If you are reviewing a HIT and you don't want to show it in this list you will need to change the
     * status to Reviewing. Then for Mturk it won't mean anything other than that it is not shown in this
     * call anymore
     */
    fun listReviewableHits(): List<HIT> {
        return try {
            client.listReviewableHITs().hiTs().map {
                it.convert()
            }
        } catch (ex: Exception) {
            log(message = "[MTurk] Error getting Reviewable HITs. Error: ${ex.message}", level = Level.SEVERE)
            throw PreconditionException("[MTurk] Error getting Reviewable HITs. Error: ${ex.message}")
        }
    }

    fun listHits(
        status: HitStatus?,
        onlyAvailableTasks: Boolean,
        onlyAllTasksCompleted: Boolean,
        onlyExpiredHITs: Boolean,
    ): List<HIT> {
        return try {
            client.listHITs().hiTs()
                .asSequence()
                .map { it.convert() }
                // only show the hits of the status you want or if null show all
                .filter { status == null || (it.hitStatus != null && it.hitStatus.uppercase() == status.name) }
                .filter {
                    !onlyAvailableTasks ||
                        (it.numberOfAssignmentsAvailable != null && it.numberOfAssignmentsAvailable > 0)
                }
                .filter {
                    !onlyAllTasksCompleted ||
                        (
                            (it.numberOfAssignmentsAvailable != null && it.numberOfAssignmentsAvailable == 0) &&
                                (it.numberOfAssignmentsPending != null && it.numberOfAssignmentsPending == 0)
                            )
                }
                .filter { it.expiration != null && it.expiration.isBefore(Instant.now()) }
                .toList()
        } catch (ex: Exception) {
            log(message = "[MTurk] Error getting HITs. Error: ${ex.message}", level = Level.SEVERE)
            throw PreconditionException("[MTurk] Error getting HITs. Error: ${ex.message}")
        }
    }

    private fun software.amazon.awssdk.services.mturk.model.HIT.convert(): HIT =
        HIT(
            hitId(),
            hitTypeId(),
            hitGroupId(),
            hitLayoutId(),
            creationTime(),
            title(),
            description(),
            question(),
            keywords(),
            hitStatusAsString(),
            maxAssignments(),
            reward(),
            autoApprovalDelayInSeconds(),
            expiration(),
            assignmentDurationInSeconds(),
            requesterAnnotation(),
            hitReviewStatusAsString(),
            numberOfAssignmentsPending(),
            numberOfAssignmentsAvailable(),
            numberOfAssignmentsCompleted(),
        )

    private fun software.amazon.awssdk.services.mturk.model.Assignment.convert(): Assignment =
        Assignment(
            assignmentId(),
            workerId(),
            hitId(),
            assignmentStatus().name,
            autoApprovalTime(),
            acceptTime(),
            submitTime(),
            approvalTime(),
            rejectionTime(),
            deadline(),
            answer(),
            requesterFeedback(),
        )

    private fun software.amazon.awssdk.services.mturk.model.WorkerBlock.convert(): WorkerBlock =
        WorkerBlock(
            workerId(),
            reason(),
        )

    fun fetchAssignmentsByHit(hitId: String): List<Assignment> {
        val assignments = client.listAssignmentsForHIT(
            ListAssignmentsForHitRequest.builder().hitId(hitId).build(),
        ).assignments()
        return assignments.map { it.convert() }.toList()
    }
}
