package nl.teqplay.portcallplus.service.external

import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import nl.teqplay.csi.model.ship.info.ShipRegisterInfo
import nl.teqplay.platform.model.ShipInfo
import nl.teqplay.portcallplus.PreconditionException
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallStatus
import nl.teqplay.portcallplus.api.model.PortcallVisit
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.ServiceModel
import nl.teqplay.portcallplus.api.model.UpdateType
import nl.teqplay.portcallplus.datasource.ActivityDataSource
import nl.teqplay.portcallplus.datasource.LisDataSource
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.datasource.ScheduledTaskRunDataSource
import nl.teqplay.portcallplus.datasource.ServiceFetchCounterDataSource
import nl.teqplay.portcallplus.model.ScheduledTaskTypeFieldsProxy
import nl.teqplay.portcallplus.model.data.nxtport.LisTravels
import nl.teqplay.portcallplus.model.service.LisTravel
import nl.teqplay.portcallplus.properties.LisProperties
import nl.teqplay.portcallplus.service.common.EventService
import nl.teqplay.portcallplus.service.common.TaskService
import nl.teqplay.portcallplus.service.internal.CsiService
import nl.teqplay.portcallplus.service.internal.PlatformService
import nl.teqplay.portcallplus.service.internal.PoMaService
import nl.teqplay.portcallplus.service.internal.UserService
import nl.teqplay.portcallplus.utils.NL_TIMEZONE
import nl.teqplay.portcallplus.utils.SCHELDE_CENTER
import nl.teqplay.portcallplus.utils.fetchTimestamp
import nl.teqplay.portcallplus.utils.getScheldePort
import nl.teqplay.portcallplus.utils.log
import nl.teqplay.portcallplus.utils.removeBraces
import nl.teqplay.skeleton.util.FileLog
import nl.teqplay.skeleton.util.haversineDistance
import org.jsoup.Jsoup
import org.jsoup.nodes.Document
import org.springframework.stereotype.Service
import java.lang.Math.abs
import java.time.Duration
import java.time.Instant
import java.util.Date
import java.util.TimeZone
import java.util.concurrent.TimeUnit
import java.util.logging.Level

const val LIS_DATE_FORMAT = "dd/MM/yy HH:mm"
val ONE_MINUTE_IN_MS = TimeUnit.MINUTES.toMillis(1)
val LIS_TIMEZONE: TimeZone = NL_TIMEZONE
private const val incomingUrl = "https://lis.loodswezen.be/Lis/VerwachteReizen.aspx"

/**
 * Scraper to get data from LIS
 * <AUTHOR>
 */
@Service
class LisScraperService(
    private val lisDataSource: LisDataSource,
    private val platformService: PlatformService,
    private val csiService: CsiService,
    scheduledTaskRunDataSource: ScheduledTaskRunDataSource,
    activityDataSource: ActivityDataSource,
    serviceFetchCounterDataSource: ServiceFetchCounterDataSource,
    private val portcallDataSource: PortcallDataSource,
    private val lisProperties: LisProperties,
    private val scheduledTaskTypeFieldsProxy: ScheduledTaskTypeFieldsProxy,
    private val userService: UserService,
    private val pomaService: PoMaService,
    eventService: EventService,
) : TaskService(
    scheduledTaskRunDataSource,
    activityDataSource,
    serviceFetchCounterDataSource,
    portcallDataSource,
    scheduledTaskTypeFieldsProxy,
    userService,
    pomaService,
    eventService,
) {
    override fun getTasks(): List<ScheduledTaskType> = listOf(ScheduledTaskType.LIS_SCRAPPER)

    override val isEnabled: Boolean = lisProperties.enable
    override val allowOtherSourcePortcallUpdate: Boolean = lisProperties.allowOtherSourcePortcallUpdate

    override fun getServiceModels(taskType: ScheduledTaskType): Collection<Pair<String, ServiceModel>> {
        if (taskType == ScheduledTaskType.LIS_SCRAPPER) {
            val incomingVessels = getLisInboundData()
            // collect all arrival data received from LIS
            return incomingVessels
                .filter { it.shipImo != null }
                .map { Pair(it.shipImo!!, it) }
        } else {
            throw PreconditionException("${this::class.java.simpleName} is invoked for unhandled taskType: $taskType")
        }
    }

    /**
     * Convert the given [serviceModel] received from this external service to an internal representation in [Portcall]
     * @param serviceModel The model received from the this external service
     * @param imoNumber The vessel imoNumber for which this data refers to
     * @param taskType The taskType is requesting this action.
     */
    override fun convertToPortcall(
        serviceModel: ServiceModel,
        imoNumber: String,
        taskType: ScheduledTaskType,
    ): Portcall {
        if (serviceModel is LisTravel) {
            val shipImo = serviceModel.shipImo
                ?: throw PreconditionException("No shipImo found for ${serviceModel.shipName}")

            // detect where is vessel is heading. If no known destination is matched. Try matching it to BEANR
            val fromPort: String? = getScheldePort(serviceModel.from)
            // Splitting a to port string and getting the first occurrence if it is found else it will search the whole string
            // This search is only done if the local mapping returns null
            val searchPortName: String = serviceModel.to.split(": ").first()
            val toPort: String = getScheldePort(serviceModel.to) ?: pomaService.getPorts(searchPattern = searchPortName).let { listPorts ->
                log.info { "Searched destination: ($searchPortName) on PoMa since this is not found in our getScheldePort mapping" }
                listPorts.firstOrNull()?.unlocode ?: Portcall.IDPREFIX_ANR
            }

            // only process the entry if the orderTime is in the future
            val orderTime = serviceModel.orderTime.let { orderTimeString ->
                getOrderTime(orderTimeString)?.takeIf {
                    it > Date()
                } ?: throw PreconditionException(
                    "Ignoring entry for ${serviceModel.shipName}. OrderTime: $orderTimeString is not in the future",
                    shipImo,
                )
            }

            // fetch any linked portcall to the imo. Both finished and active portcalls
            val linkedPortcall = portcallDataSource.getMostRecentByImo(imoNumber, toPort)
            return if (linkedPortcall != null) {
                if (linkedPortcall.startTime != orderTime) {
                    FileLog().writeToLogFile(serviceModel.toString(), "lis-incoming.out")
                    log(
                        imoNumber = imoNumber,
                        message = "Order time changed in LIS, not matching portcall. Updating eta: $orderTime",
                    )
                }

                val firstBerthName = linkedPortcall.visits.firstOrNull()?.berthName
                if (firstBerthName != null) {
                    val newVisit = PortcallVisit(
                        berthEta = orderTime,
                        berthName = firstBerthName,
                        visitType = UpdateType.BERTH,
                    )
                    linkedPortcall.getUpdatedPortcall(
                        source = taskType,
                        startTimeType = UpdateType.BERTH,
                        newVisits = listOf(newVisit),
                    )
                } else {
                    linkedPortcall
                }
            } else {
                getFileLog().writeToLogFile(serviceModel.toString(), "lis-incoming.out")
                val nextPortcallId = portcallDataSource.generateId(toPort, shipImo, orderTime)
                Portcall(
                    portcallId = nextPortcallId,
                    portcallAlias = emptySet(),
                    port = toPort,
                    imo = shipImo,
                    source = taskType,
                    startTime = orderTime,
                    startTimeType = UpdateType.PILOTBOARDINGPLACE,
                    status = PortcallStatus.INBOUND,
                    originUnlocode = fromPort,
                    destinationUnlocode = toPort,
                )
            }
        } else {
            throw PreconditionException("${this::class.java.simpleName} is invoked for unhandled model: ${serviceModel::class}")
        }
    }

    fun getFileLog(): FileLog {
        // Constructing it here so it's better mockable for unit testing
        return FileLog()
    }

    fun getLisInboundData(): List<LisTravel> {
        val doc = Jsoup.connect(incomingUrl).get()
        // collect all the ship names: <draught, length>
        val selectedShips = fetchLisTravels(doc)
        val currentTime = Date()

        val result = runBlocking {
            val asyncResult = selectedShips
                .filter {
                    val orderTime = getOrderTime(it.orderTime)
                    if (orderTime != null && orderTime >= currentTime) {
                        true
                    } else {
                        log.info { "Ignoring entry for ${it.shipName}. OrderTime: ${it.orderTime} is not in the future" }
                        false
                    }
                }
                .map { lisTravel ->
                    async {
                        val shipInfo = selectBestShipInfo(
                            csiService.getShipsByName(lisTravel.shipName),
                            lisTravel,
                        )
                        // Add to not found cache if no ShipInfo is resolved
                        if (shipInfo != null) {
                            lisTravel.copy(shipImo = shipInfo.identifiers.imo)
                        } else {
                            null
                        }
                    }
                }
            asyncResult.mapNotNull { it.await() }
        }
        log.info { "Number of updates: ${result.size}. Imos: ${result.map { it.shipImo }.removeBraces()}" }
        lisDataSource.createOrUpdate(LisTravels(PortcallStatus.INBOUND, result))
        return result
    }

    fun fetchLisTravels(doc: Document): Collection<LisTravel> {
        val selectedShips = hashMapOf<String, LisTravel>()
        for (table in doc.select("#ctl00_ContentPlaceHolder1_ctl01_list_gv")) {
            log(message = "Amount of rows: " + table.select("tr").count())
            for (row in table.select("tr")) {
                val tds = row.select("td")
                if (tds.size > 6) {
                    // Set all variables for the LisTravel object
                    val shipName = filterShipName(tds[4].text())
                    val length = tds[5].text().replace(",", ".").toDoubleOrNull()
                    val draught = tds[6].text().replace(",", ".").toDoubleOrNull()
                    if (selectedShips.containsKey(shipName)) {
                        log(message = "Duplicate ship! $shipName")
                    }
                    selectedShips[shipName] = LisTravel(
                        tds[0].text(), tds[1].text(), shipName, null, tds[3].text(), length, draught,
                    )
                } else {
                    log(message = "Row doesn't contain 6 columns ")
                }
            }
        }
        return selectedShips.values
    }

    fun filterShipName(lisShipName: String): String {
        return lisShipName.replace(Regex("\\[.*?]"), "")
            .replace(Regex("\\(.*?\\)"), "")
            .replace(Regex("[^A-Za-z0-9 ]"), "")
            .replace("  ", " ")
            .trim()
    }

    fun getOrderTime(orderTime: String): Date? {
        return try {
            fetchTimestamp(orderTime, LIS_DATE_FORMAT, LIS_TIMEZONE)
        } catch (ex: Exception) {
            log(message = ex.message ?: "Error in parsing date: $orderTime", level = Level.SEVERE)
            null
        }
    }

    /**
     * Selecting the best [ShipInfo] by filtering and fetched detailed [ShipInfo] to match based on dimensions
     */
    fun selectBestShipInfo(
        shipInfos: List<ShipRegisterInfo>,
        lisTravel: LisTravel,
    ): ShipRegisterInfo? {
        // Filter for sea vessels, the precise ship name and no older then a week
        val seaVessels = shipInfos.filter {
            it.identifiers.imo != null && it.identifiers.name == lisTravel.shipName
        }

        val currentInfo = platformService.getCurrentByMmsis(
            seaVessels.mapNotNull { it.identifiers.mmsi }.toSet(),
        ).associateBy { it.mmsi.toInt() }

        val activeSeaVessels = seaVessels.filter {
            val lastUpdate = currentInfo[it.identifiers.mmsi?.toInt()]?.messageTime ?: Instant.MIN
            Duration.between(lastUpdate, Instant.now()).toDays() <= 7
        }

        if (activeSeaVessels.isNotEmpty()) {
            // If only one, it's a match
            if (activeSeaVessels.size == 1) {
                val seaVessel = activeSeaVessels.first()
                log(imoNumber = seaVessel.identifiers.imo ?: "", message = "Matched shipinfo for ${seaVessel.identifiers.name}")
                return seaVessel
            }
            // First try to match on ship length
            for (shipInfo in activeSeaVessels) {
                if (lisTravel.shipLength != null) {
                    val length = shipInfo.dimensions.length

                    if (length != null && abs(length - lisTravel.shipLength) < 5) {
                        log(
                            imoNumber = shipInfo.identifiers.imo ?: "",
                            message = "Matched shipinfo for ${shipInfo.identifiers.name} by shipLength",
                        )
                        return shipInfo
                    }
                }
            }
            // detect where is vessel is heading
            val toPort = getScheldePort(lisTravel.to)
            // Try match based on destination
            val match = seaVessels.map {
                it to currentInfo[it.identifiers.mmsi?.toInt()]
            }.sortedByDescending { (_, info) ->
                info?.messageTime
            }.sortedBy { (_, info) ->
                info?.location?.let { location -> haversineDistance(SCHELDE_CENTER, location) } ?: Double.MAX_VALUE
            }.firstOrNull { (_, info) ->
                toPort != null && getScheldePort(info?.destination) == toPort
            }
            val seaVessel = match?.first
            val info = match?.second
            if (seaVessel != null) {
                log(
                    imoNumber = seaVessel.identifiers.imo ?: "",
                    message = "Matched shipinfo for ${seaVessel.identifiers.name} by destination match: ${info?.destination} to $toPort",
                )
            } else {
                log(message = "No Matched shipinfo for ${lisTravel.shipName}", level = Level.WARNING)
            }
            return seaVessel
        }
        return null
    }
}
