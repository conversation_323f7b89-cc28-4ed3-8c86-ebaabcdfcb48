package nl.teqplay.portcallplus.service.external

import DigitrafficPortcallUpdate
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.portcallplus.PreconditionException
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.Portcall.Companion.IDPREFIX_FIHEL
import nl.teqplay.portcallplus.api.model.PortcallAliasName
import nl.teqplay.portcallplus.api.model.PortcallStatus
import nl.teqplay.portcallplus.api.model.PortcallVisit
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.DIGITRAFFIC
import nl.teqplay.portcallplus.api.model.ServiceModel
import nl.teqplay.portcallplus.api.model.UpdateType
import nl.teqplay.portcallplus.datasource.ActivityDataSource
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.datasource.ScheduledTaskRunDataSource
import nl.teqplay.portcallplus.datasource.ServiceFetchCounterDataSource
import nl.teqplay.portcallplus.model.ScheduledTaskTypeFieldsProxy
import nl.teqplay.portcallplus.model.service.DigitrafficPortcall
import nl.teqplay.portcallplus.properties.DigitrafficProperties
import nl.teqplay.portcallplus.service.common.EventService
import nl.teqplay.portcallplus.service.common.TaskService
import nl.teqplay.portcallplus.service.internal.CsiService
import nl.teqplay.portcallplus.service.internal.PoMaService
import nl.teqplay.portcallplus.service.internal.UserService
import nl.teqplay.portcallplus.utils.executeGetCall
import nl.teqplay.portcallplus.utils.log
import nl.teqplay.portcallplus.utils.millisToIso
import nl.teqplay.portcallplus.utils.setPortcallAlias
import okhttp3.OkHttpClient
import okhttp3.Response
import org.springframework.stereotype.Service
import java.time.Duration
import java.util.Date
import java.util.concurrent.TimeUnit
import java.util.logging.Level

@Service
class DigitrafficService(
    scheduledTaskRunDataSource: ScheduledTaskRunDataSource,
    activityDataSource: ActivityDataSource,
    serviceFetchCounterDataSource: ServiceFetchCounterDataSource,
    val portcallDataSource: PortcallDataSource,
    val csiService: CsiService,
    val mapper: ObjectMapper,
    private val digitrafficProperties: DigitrafficProperties,
    private val scheduledTaskTypeFieldsProxy: ScheduledTaskTypeFieldsProxy,
    private val userService: UserService,
    pomaService: PoMaService,
    eventService: EventService,
) : TaskService(
    scheduledTaskRunDataSource,
    activityDataSource,
    serviceFetchCounterDataSource,
    portcallDataSource,
    scheduledTaskTypeFieldsProxy,
    userService,
    pomaService,
    eventService,
) {
    /**
     * The amount of time to check around a portcall to see if there is already a portcall for that vessel when the
     * portcall isn't found by the id
     */
    val NEW_PORTCALL_INTERVAL = TimeUnit.HOURS.toMillis(digitrafficProperties.newPortcallInterval)

    override fun getTasks(): List<ScheduledTaskType> = listOf(DIGITRAFFIC)

    override val isEnabled: Boolean = digitrafficProperties.enable
    override val allowOtherSourcePortcallUpdate: Boolean = digitrafficProperties.allowOtherSourcePortcallUpdate

    /**
     * The endpoint to be called
     */
    val digitrafficUrl = digitrafficProperties.url

    /**
     * The amount of ms a day means
     */
    val HourInMs = Duration.ofHours(1).toMillis()

    /**
     * Process the response received from a single call to Digitraffic
     * Map it into portcalls and save the updates in the database
     */
    override fun getServiceModels(taskType: ScheduledTaskType): List<Pair<String, DigitrafficPortcall>> {
        val fromIsoTimestamp = millisToIso(System.currentTimeMillis() - (HourInMs))
        val toIsoTimestamp = millisToIso(System.currentTimeMillis())
        val fetchResult = fetchDigitrafficPortcalls(fromIsoTimestamp, toIsoTimestamp)

        val statusCode = fetchResult.code
        if (isRunning(DIGITRAFFIC)) {
            if (statusCode == 200) {
                try {
                    val body = fetchResult.body?.string()
                        ?: throw PreconditionException("Response body is null")
                    val mappedResponse = mapper.readValue<DigitrafficPortcallUpdate>(body)
                    return mappedResponse.portCalls.mapNotNull { digiPortcall ->
                        val imo = getImo(digiPortcall)
                        if (imo != null) {
                            imo to digiPortcall
                        } else {
                            null
                        }
                    }
                } catch (e: Exception) {
                    val runMessage = "[DigitrafficService] failed to get portcalls, ${e.message}"
                    log(message = runMessage, level = Level.SEVERE)
                    throw e
                }
            }
        } else {
            log(message = "Response ignored as task was preempted", level = Level.WARNING)
        }
        return listOf()
    }

    /**
     * fetch Digitraffic portcalls from their system
     */
    fun fetchDigitrafficPortcalls(
        fromIsoTimestamp: String,
        toIsoTimestamp: String,
    ): Response {
        val queryParams = mapOf(
            "from" to fromIsoTimestamp,
            "to" to toIsoTimestamp,
        )
        return OkHttpClient().executeGetCall(digitrafficUrl, queryParams, emptyMap())
    }

    override fun convertToPortcall(
        serviceModel: ServiceModel,
        imoNumber: String,
        taskType: ScheduledTaskType,
    ): Portcall {
        if (serviceModel is DigitrafficPortcall) {
            val visits = serviceModel.portAreaDetails
            val startTime = visits.firstOrNull()?.ata ?: visits.firstOrNull()?.eta
            if (startTime != null) {
                return getUpdatedPortcall(getPortcall(serviceModel, imoNumber, startTime), serviceModel)
            }
        }
        throw PreconditionException("Model type not handled")
    }

    /**
     * Add the changes in visits
     */
    private fun getUpdatedPortcall(
        portcall: Portcall,
        serviceModel: DigitrafficPortcall,
    ): Portcall {
        val visits = serviceModel.portAreaDetails.map { portAreaDetails ->
            PortcallVisit(
                berthEta = portAreaDetails.eta,
                berthAta = portAreaDetails.ata,
                berthAtd = portAreaDetails.atd,
                berthEtd = portAreaDetails.etd,
                berthName = portAreaDetails.berthName ?: portAreaDetails.berthCode,
                visitType = UpdateType.BERTH,
            )
        }
        return portcall.getUpdatedPortcall(newVisits = visits, startTimeType = UpdateType.BERTH)
    }

    /**
     * Fetch or create Portcall and attach the portcallAlias if it is needed
     */
    private fun getPortcall(
        serviceModel: DigitrafficPortcall,
        imoNumber: String,
        startTime: Date,
    ): Portcall {
        val port = getPortUnloCode(serviceModel)
        val portcall = (
            portcallDataSource.searchByAlias(serviceModel.portCallId.toString(), PortcallAliasName.DIGITRAFFIC)
                .firstOrNull()
                ?: portcallDataSource.getNearestByImoAndDateInterval(imoNumber, port, startTime, NEW_PORTCALL_INTERVAL)
                ?: createNewPortcall(serviceModel, imoNumber, startTime)
            )
        return portcall.copy(
            portcallAlias = setPortcallAlias(portcall, PortcallAliasName.DIGITRAFFIC, serviceModel.portCallId.toString()),
            status = getPortcallStatus(serviceModel),
        )
    }

    fun getPortUnloCode(serviceModel: DigitrafficPortcall): String {
        return if (serviceModel.portToVisit.uppercase() == IDPREFIX_FIHEL &&
            serviceModel.portAreaDetails.firstOrNull()?.portAreaCode?.uppercase() == "VUOS"
        ) {
            "FIVSS"
        } else {
            serviceModel.portToVisit.uppercase()
        }
    }

    /**
     * Create a portcall without visits with the data received
     */
    private fun createNewPortcall(
        serviceModel: DigitrafficPortcall,
        imoNumber: String,
        startTime: Date,
    ): Portcall {
        val port = getPortUnloCode(serviceModel)
        return Portcall(
            portcallDataSource.generateId(port, imoNumber, startTime),
            setOf(),
            port,
            imoNumber,
            DIGITRAFFIC,
            startTime,
            UpdateType.NOMINATION,
            getPortcallStatus(serviceModel),
            serviceModel.portAreaDetails.lastOrNull()?.atd,
            serviceModel.portAreaDetails.firstOrNull()?.ata,
            serviceModel.portAreaDetails.lastOrNull()?.atd,
            serviceModel.portAreaDetails.lastOrNull()?.etd,
            vesselAgent = serviceModel.agentInfo.firstOrNull()?.name,
        )
    }

    /**
     * Check which status the portcall is in
     */
    private fun getPortcallStatus(serviceModel: DigitrafficPortcall): PortcallStatus {
        return when {
            serviceModel.portAreaDetails.firstOrNull()?.ata == null -> PortcallStatus.INBOUND
            serviceModel.portAreaDetails.any { it.ata != null && it.atd == null } -> PortcallStatus.ALONGSIDE
            serviceModel.portAreaDetails.any { it.ata == null && it.atd == null } -> PortcallStatus.SHIFTING
            serviceModel.portAreaDetails.lastOrNull()?.atd != null -> PortcallStatus.OUTBOUND
            else -> PortcallStatus.UNKNOWN
        }
    }

    private fun getImo(digiPortcall: DigitrafficPortcall): String? {
        return if (digiPortcall.imoLloyds == null || digiPortcall.imoLloyds == 0) {
            if (digiPortcall.mmsi != 0) {
                csiService.getShipByMmsi(digiPortcall.mmsi.toString())?.identifiers?.imo
            } else {
                null
            }
        } else {
            digiPortcall.imoLloyds.toString()
        }
    }
}
