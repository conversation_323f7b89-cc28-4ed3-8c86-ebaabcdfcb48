package nl.teqplay.portcallplus.service.external.portbase

import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.platform.model.area.PilotBoardingPlaceType
import nl.teqplay.portcallplus.PreconditionException
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallAlias
import nl.teqplay.portcallplus.api.model.PortcallAliasName
import nl.teqplay.portcallplus.api.model.PortcallPurpose
import nl.teqplay.portcallplus.api.model.PortcallStatus
import nl.teqplay.portcallplus.api.model.PortcallVisit
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.ServiceModel
import nl.teqplay.portcallplus.api.model.UpdateType
import nl.teqplay.portcallplus.datasource.ActivityDataSource
import nl.teqplay.portcallplus.datasource.PortBaseAgentMappingDataSource
import nl.teqplay.portcallplus.datasource.PortBaseDataSource
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.datasource.ScheduledTaskRunDataSource
import nl.teqplay.portcallplus.datasource.ServiceFetchCounterDataSource
import nl.teqplay.portcallplus.model.ScheduledTaskTypeFieldsProxy
import nl.teqplay.portcallplus.model.data.portbase.PortbaseAgentMappingDataModel
import nl.teqplay.portcallplus.model.data.portbase.PortbaseDataModel
import nl.teqplay.portcallplus.model.httpResponse.PortBaseListenResponse
import nl.teqplay.portcallplus.model.service.PortBasePortCall
import nl.teqplay.portcallplus.properties.PortBaseClientProperties
import nl.teqplay.portcallplus.properties.PortBaseProperties
import nl.teqplay.portcallplus.service.common.EventService
import nl.teqplay.portcallplus.service.common.TaskService
import nl.teqplay.portcallplus.service.internal.PoMaService
import nl.teqplay.portcallplus.service.internal.UserService
import nl.teqplay.portcallplus.utils.executePostCall
import nl.teqplay.portcallplus.utils.fetchTimestamp
import nl.teqplay.portcallplus.utils.fetchTimestampOrNull
import nl.teqplay.portcallplus.utils.getFirstVisitStartTime
import nl.teqplay.portcallplus.utils.log
import nl.teqplay.portcallplus.utils.mapper
import okhttp3.OkHttpClient
import org.springframework.http.HttpStatus
import java.io.IOException
import java.util.logging.Level

/**
 * This will retrieve portcalls from PortBase
 * Model properties can be found in
 * https://portvisit.sc.portbase.com/api-documentation/index.html#tag/Tracking-API/operation/_01_listenForVisitUpdates
 * under the response samples where updates need to be expanded
 *
 */
abstract class PortBaseService(
    private val dataSource: PortBaseDataSource,
    private val scheduledTaskRunDataSource: ScheduledTaskRunDataSource,
    activityDataSource: ActivityDataSource,
    serviceFetchCounterDataSource: ServiceFetchCounterDataSource,
    portcallDataSource: PortcallDataSource,
    private val scheduledTaskType: ScheduledTaskType,
    private val portBaseProperties: PortBaseProperties,
    private val portBaseClientProperties: PortBaseClientProperties,
    private val scheduledTaskTypeFieldsProxy: ScheduledTaskTypeFieldsProxy,
    private val userService: UserService,
    pomaService: PoMaService,
    eventService: EventService,
    private val agentMappingDataSource: PortBaseAgentMappingDataSource,
) : TaskService(
    scheduledTaskRunDataSource,
    activityDataSource,
    serviceFetchCounterDataSource,
    portcallDataSource,
    scheduledTaskTypeFieldsProxy,
    userService,
    pomaService,
    eventService,
) {
    override val portcallAliasName = PortcallAliasName.PORTBASE

    override fun getTasks(): List<ScheduledTaskType> = listOf(scheduledTaskType)

    override val isEnabled: Boolean = portBaseProperties.enable && portBaseClientProperties.enable
    override val allowOtherSourcePortcallUpdate: Boolean = portBaseClientProperties.allowOtherSourcePortcallUpdate

    // Specific client to do calls with a timeout
    private val okhttpTimeoutClient: OkHttpClient

    // For PortBase calls that don't need the timeout
    private val okhttpClient: OkHttpClient

    private val headers = mapOf(
        "Portbase-access-key-id" to portBaseClientProperties.accessKey,
        "Portbase-secret-access-key" to portBaseClientProperties.secretAccessKey,
    )

    /**
     * The id we use to connect to Portbase ( this can be anything as long as we do it consistent )
     */
    private val clientId = portBaseClientProperties.clientId

    /**
     * Max number of responses we get
     */
    private val maxSizeRequested: Int = 32 // Their default is 32

    /**
     * Max time we want to wait for a response
     */
    private val maxRequestTimeOut: Long = portBaseProperties.maxRequestTimeOutMs // In ms, their default is 10000

    /**
     * The url to connect to
     */
    private val baseUrl = portBaseProperties.url

    /**
     * The endpoint url to disconnect the client
     * should be called if we receive an error or if we suddenly stop the process
     */
    private val disconnectUrl = baseUrl + "portvisit/disconnectClient"

    /**
     * The url of the listen endpoint
     */
    private val listenUrl = baseUrl + "portvisit/listen"

    /**
     * The endpoint url to update the last fetched id
     */
    private val updatePositionUrl = baseUrl + "portvisit/updatePosition"

    init {
        okhttpClient = OkHttpClient()
        okhttpTimeoutClient = OkHttpClient()
            .newBuilder()
            // Divide by 1000 to convert to seconds
            .callTimeout(maxRequestTimeOut / 1000, java.util.concurrent.TimeUnit.SECONDS)
            .build()
    }

    /**
     * This will disconnect from Portbase and stop the requestData loop from running
     */
    override fun stopTask(
        taskType: ScheduledTaskType,
        forceStop: Boolean,
    ): Boolean {
        disconnectClient(clientId, headers)
        return super.stopTask(taskType, forceStop)
    }

    /**
     * As this is an infinitely running task, we always return an empty result,
     * after fetching all portcalls and updating it in the db
     */
    override fun getServiceModels(taskType: ScheduledTaskType): Collection<Pair<String, ServiceModel>> {
        if (taskType == scheduledTaskType && isEnabled) {
            requestPortbase()
        } else {
            throw PreconditionException("${this::class.java.simpleName} is invoked for unhandled taskType: $taskType")
        }
        return listOf()
    }

    /**
     * Request the portcall data from portbase endlessly as long as the running is true
     */
    private fun requestPortbase() {
        var clientSegment = listOf<Int>()
        var lastFetchIndex = scheduledTaskRunDataSource.get(scheduledTaskType)?.lastFetchIndex

        // Run it continuously. A timeout of 10 secs is added to the requestDatas
        while (true) {
            val timestamp = System.currentTimeMillis()
            try {
                val result = requestData(clientId, headers, clientSegment, lastFetchIndex)
                if (result != null) {
                    processResponse(result)
                    clientSegment = result.clientSegment
                    lastFetchIndex = result.lastIndex ?: lastFetchIndex
                    saveLastRun(lastFetchIndex = lastFetchIndex, timeTaken = System.currentTimeMillis() - timestamp)
                }
            } catch (ex: Exception) {
                // connection issues
                saveLastRun(
                    lastFetchIndex = lastFetchIndex,
                    failureMessage = ex.toString(),
                    timeTaken = System.currentTimeMillis() - timestamp,
                )
                disconnectClient(clientId, headers)
                // throw exception, parent class should retry in increments
                throw ex
            }
        }
    }

    /**
     * Request the portcall data from portbase endlessly as long as the running is true
     */
    private fun requestData(
        clientId: String,
        headers: Map<String, String>,
        clientSegment: List<Int>,
        lastFetchIndex: String?,
    ): PortBaseListenResponse? {
        // update the last fetch Index in Portbase only if its non-null
        lastFetchIndex?.let { updatePosition(headers, it, clientSegment) }
        log(message = "Requesting portbase: $listenUrl from index: $lastFetchIndex with clientId: $clientId", level = Level.FINE)
        val postRequestMap = mapOf(
            "clientId" to clientId,
            "version" to 1,
            "maxSize" to maxSizeRequested,
            "maxTimeout" to maxRequestTimeOut,
        )
        val response = try {
            okhttpTimeoutClient.executePostCall(listenUrl, postRequestMap, headers)
        } catch (exception: IOException) {
            throw PreconditionException("Timeout because connection was closed. No messages within the timeout limits")
        }
        val body = response.body?.string()
            ?: throw PreconditionException("statusCode: ${response.code} with null response.")
        return when (response.code) {
            HttpStatus.OK.value(),
            HttpStatus.PARTIAL_CONTENT.value(),
            -> {
                runCatching {
                    mapper.readValue<PortBaseListenResponse>(body)
                }.getOrElse {
                    throw PreconditionException("[PortBaseService] failed to deserialize PortBaseListenResponse '$body', ${it.message}")
                }
            }
            HttpStatus.REQUEST_TIMEOUT.value(),
            HttpStatus.GATEWAY_TIMEOUT.value(),
            -> null
            else -> throw PreconditionException("statusCode: ${response.code} with response: '$body'")
        }
    }

    private fun saveLastRun(
        lastFetchIndex: String? = null,
        failureMessage: String? = null,
        timeTaken: Long,
    ) {
        if (lastFetchIndex != null) {
            val scheduledTaskTypeFields = scheduledTaskTypeFieldsProxy.getFields(scheduledTaskType)
            if (failureMessage == null) {
                saveLastRun(scheduledTaskTypeFields, lastTimeTakenToRun = timeTaken, lastFetchIndex = lastFetchIndex)
            } else {
                saveLastRun(scheduledTaskTypeFields, failureMessage, timeTaken, lastFetchIndex, true)
            }
        }
    }

    /**
     * Convert the service model to the Portcall model
     */
    override fun convertToPortcall(
        serviceModel: ServiceModel,
        imoNumber: String,
        taskType: ScheduledTaskType,
    ): Portcall {
        if (serviceModel is PortBasePortCall) {
            val startTime = serviceModel.visitDeclaration.portVisit.ataPort
                ?: serviceModel.visitDeclaration.portVisit.etaPort

            val portcallPurpose = hashSetOf<PortcallPurpose>()
            val newVisits = serviceModel.visitDeclaration.portVisit.berthVisits?.map { berthVisit ->
                if (berthVisit.visitPurposes != null) {
                    visitPurposesToPortcallPurposes(berthVisit.visitPurposes).forEach { purpose ->
                        portcallPurpose.add(purpose)
                    }
                }
                PortcallVisit(
                    berthEta = fetchTimestampOrNull(berthVisit.eta),
                    berthAta = fetchTimestampOrNull(berthVisit.ata),
                    berthAtd = fetchTimestampOrNull(berthVisit.atd),
                    berthEtd = fetchTimestampOrNull(berthVisit.etd),
                    berthName = berthVisit.berth?.name,
                    visitType = UpdateType.BERTH,
                )
            } ?: listOf()
            // Check if the berth visits is not empty else we look at the starttime
            // if both berthVisits and the startTime are null then we raise an error
            val visitStartTime = getFirstVisitStartTime(newVisits)
            val startDateTime = visitStartTime
                ?: startTime?.let { fetchTimestamp(startTime) }
                ?: throw PreconditionException("[PortBaseService] failed to convert portcall no startTime found from portVisit and the berthVisits")

            // The client reference number, as the portcall is known to the specific client
            val externalReferenceAlias = serviceModel.visitDeclaration.clientReferenceNumber?.let {
                setOf(PortcallAlias(PortcallAliasName.PORTBASE_CLIENT_REFERENCE_NUMBER, it))
            } ?: setOf()

            val vesselAgent: String? = serviceModel.declarant?.fullName?.let { itDeclarantFullname ->
                val port = serviceModel.portOfCall.port.locationUnCode?.uppercase()
                agentMappingDataSource.getByAgentDescription(
                    description = itDeclarantFullname,
                    port = port,
                )?.let { itAgentMapping ->
                    if (itAgentMapping.agent != null) {
                        itAgentMapping.agent
                    } else {
                        // Mapping found but no agent has been set in the database yet, just warn and fill in the found declarant fullName
                        log(
                            serviceModel.crn,
                            serviceModel.vessel.imoCode,
                            serviceModel.portOfCall.port.locationUnCode ?: "",
                            "Portcall agent name is empty for mapping on '$itDeclarantFullname'",
                            Level.WARNING,
                        )
                        itDeclarantFullname
                    }
                } ?: run {
                    log(
                        serviceModel.crn,
                        serviceModel.vessel.imoCode,
                        port ?: "",
                        "Portcall agent mapping not found for '$itDeclarantFullname' and '$port', thus creating new one",
                        Level.WARNING,
                    )
                    agentMappingDataSource.insert(
                        PortbaseAgentMappingDataModel(
                            agentDescription = itDeclarantFullname,
                            port = port,
                        ),
                    )
                    itDeclarantFullname
                }
            } ?: run {
                log(
                    serviceModel.crn,
                    serviceModel.vessel.imoCode,
                    serviceModel.portOfCall.port.locationUnCode ?: "",
                    "Portcall declarant fullName is null",
                    Level.WARNING,
                )
                null
            }

            return Portcall(
                portcallId = serviceModel.crn.uppercase(),
                portcallAlias = setOf(PortcallAlias(PortcallAliasName.PORTBASE, serviceModel.crn.uppercase())) + externalReferenceAlias,
                port = serviceModel.portOfCall.port.locationUnCode!!.uppercase(),
                imo = serviceModel.vessel.imoCode, source = taskType,
                startTime = startDateTime,
                startTimeType = UpdateType.PORT,
                status = getStatus(serviceModel),
                endTime = fetchTimestampOrNull(serviceModel.visitDeclaration.portVisit.atdPort),
                portAtaTime = fetchTimestampOrNull(serviceModel.visitDeclaration.portVisit.ataPort),
                portAtdTime = fetchTimestampOrNull(serviceModel.visitDeclaration.portVisit.atdPort),
                etd = fetchTimestampOrNull(serviceModel.visitDeclaration.portVisit.etdPort),
                enteredPBP = false,
                pilotBoardingPlaceType = PilotBoardingPlaceType.UNKNOWN,
                vesselAgent = vesselAgent,
                purpose = portcallPurpose,
                visits = newVisits,
                originUnlocode = serviceModel.visitDeclaration.previousPorts?.last()?.port?.locationUnCode,
                destinationUnlocode = serviceModel.portOfCall.port.locationUnCode,
            )
        } else {
            throw PreconditionException("${this::class.java.simpleName} is invoked for unhandled model: ${serviceModel::class}")
        }
    }

    /**
     * Map the found visitPurposes to the known PortcallPurposes
     * if it is not found map it to UNKNOWN
     *
     * @param visitPurposes List of Strings from a portbase model
     * @return A list of mapped PortcallPurposes
     */
    private fun visitPurposesToPortcallPurposes(visitPurposes: List<String>): MutableList<PortcallPurpose> {
        return visitPurposes.map {
            try {
                PortcallPurpose.valueOf(it)
            } catch (e: Exception) {
                PortcallPurpose.UNKNOWN
            }
        }.toMutableList()
    }

    /**
     * After the response has been gotten try to process it and create or update the corresponding portcalls
     */
    internal fun processResponse(portBaseListenResponse: PortBaseListenResponse) {
        val serviceModels: MutableList<PortBasePortCall> = mutableListOf()
        for (portBaseUpdate in portBaseListenResponse.updates) {
            val portBasePortCall = portBaseUpdate.after ?: continue
            // only consider if locationUnCode is not null
            if (portBasePortCall.portOfCall.port.locationUnCode != null) {
                serviceModels.add(portBasePortCall)
                dataSource.createOrUpdate(PortbaseDataModel(portBasePortCall.crn, portBasePortCall))
            } else {
                log(
                    portBasePortCall.crn,
                    portBasePortCall.vessel.imoCode,
                    portBasePortCall.portOfCall.port.locationUnCode ?: "",
                    "Port locationUnCode missing",
                    Level.WARNING,
                )
            }
        }
        if (serviceModels.size > 0) {
            super.updatePortcall(serviceModels.map { it.vessel.imoCode to it }, scheduledTaskType)
        }
    }

    /**
     * return the status that the ship is in based on the available times
     */
    internal fun getStatus(portBasePortCall: PortBasePortCall): PortcallStatus {
        val berthVisits = portBasePortCall.visitDeclaration.portVisit.berthVisits ?: emptyList()
        if (portBasePortCall.visitDeclaration.portVisit.atdPort != null || berthVisits.all { it.atd != null }) {
            return PortcallStatus.OUTBOUND
        }
        // if any berthvisit has started and not ended yet
        if (berthVisits.any { it.ata != null && it.atd == null }) {
            return PortcallStatus.ALONGSIDE
        }
        // if there are berthvisits that are not done yet AND none are in berth AND there has been a berthvisit before
        if (berthVisits.any { it.ata == null && it.atd == null } &&
            berthVisits.none { it.ata != null && it.atd == null } &&
            berthVisits.any { it.ata != null && it.atd != null }
        ) {
            return PortcallStatus.SHIFTING
        }
        return PortcallStatus.INBOUND
    }

    /**
     * Does a call to portbase to update them on which portcalls we want to receive
     */
    private fun updatePosition(
        headers: Map<String, String>,
        lastFetchIndex: String,
        clientSegment: List<Int> = listOf(),
    ) {
        val postRequestMap = mapOf("lastIndex" to lastFetchIndex, "clientSegment" to clientSegment)
        okhttpClient.executePostCall(updatePositionUrl, postRequestMap, headers)
    }

    /**
     * If an error has occured or if we want to stop calling to portbase we should call this function which will
     * call their disconnect endpoint
     */
    private fun disconnectClient(
        clientId: String,
        headers: Map<String, String>,
    ) {
        val postRequestMap = mapOf("clientId" to clientId)
        okhttpClient.executePostCall(disconnectUrl, postRequestMap, headers)
    }
}
