package nl.teqplay.portcallplus.service.common

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.poma.api.v1.Berth
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallAlias
import nl.teqplay.portcallplus.api.model.PortcallAliasName
import nl.teqplay.portcallplus.api.model.PortcallPurpose
import nl.teqplay.portcallplus.api.model.PortcallStatus
import nl.teqplay.portcallplus.api.model.PortcallVisit
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.ServiceModel
import nl.teqplay.portcallplus.api.model.UpdateType
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.model.service.PortcallUpdateResult
import nl.teqplay.portcallplus.service.internal.PoMaService
import nl.teqplay.portcallplus.utils.fetchTimestamp
import nl.teqplay.portcallplus.utils.getStartTime
import nl.teqplay.portcallplus.utils.isNotValidNewVisit
import nl.teqplay.portcallplus.utils.log
import java.util.Date
import java.util.concurrent.TimeUnit
import java.util.logging.Level

/**
 * Gives a template for all services that need the following features:
 * 1. Handle portcall details from an external service
 */
abstract class SourceService(
    private val portcallDataSource: PortcallDataSource,
    private val pomaService: PoMaService,
    private val eventService: EventService,
) {
    private val log = KotlinLogging.logger("SourceService")

    abstract val allowOtherSourcePortcallUpdate: Boolean

    open val portcallAliasName: PortcallAliasName? = null

    // The reference timestamp for fetching past portcalls
    fun getTimestampFrom() = fetchTimestamp(Date(), -10, TimeUnit.DAYS)

    abstract fun getServiceModels(taskType: ScheduledTaskType): Collection<Pair<String, ServiceModel>>

    /**
     * Convert the given [serviceModel] received from this external service to an internal representation in [Portcall]
     * @param serviceModel The model received from the this external service
     * @param imoNumber The vessel imoNumber for which this data refers to
     */
    abstract fun convertToPortcall(
        serviceModel: ServiceModel,
        imoNumber: String,
    ): Portcall

    /**
     * Update the existing and portcall received from this external service
     * @param serviceModels Actual <imoNumber, [ServiceModel]> received from the external service
     * @return The list of all portcalls converted/updated
     */
    protected open fun updatePortcall(serviceModels: Collection<Pair<String, ServiceModel>>): List<PortcallUpdateResult> {
        val portcallsFromService = mutableListOf<Portcall>()
        serviceModels.forEach { (imo, serviceModel) ->
            try {
                // get a mapping of all <imoNumber, Portcall> from this external service
                val portcall = convertToPortcall(serviceModel, imo)
                portcallsFromService.add(portcall)
            } catch (ex: Exception) {
                log(
                    imoNumber = imo,
                    message = "Not processing ${serviceModel::class.java.simpleName} update: $serviceModel. Message: ${ex.localizedMessage}",
                    level = Level.WARNING,
                )
            }
        }
        return updatePortcall(portcallsFromService)
    }

    /**
     * Resolve the Poma [Berth] via the berthName on the visit and port. Fallback only using the berthName.
     *
     * @param visit The visit we want to find the [Berth] for
     */
    private fun resolveBerthForVisit(
        visit: PortcallVisit,
        port: String,
    ): Berth? {
        // TODO check how this performs, cache request if needed
        val berthsViaPort = pomaService.getBerths(berthName = visit.berthName, port = port)

        if (berthsViaPort.size > 1) {
            log.debug { "Found multiple berths when resolving unique berht id, taking first (search pattern = ${visit.berthName}, port = $port)" }
            return berthsViaPort.first()
        }

        val berthViaPort = berthsViaPort.firstOrNull()
        if (berthViaPort != null) {
            return berthViaPort
        }

        // Fallback, search only based on the search pattern
        val fallbackBerths = pomaService.getBerths(berthName = visit.berthName, port = null)

        if (fallbackBerths.size > 1) {
            log.debug { "Found multiple berths using fall back when resolving unique berht id, taking first (search pattern = ${visit.berthName})" }
            return fallbackBerths.first()
        }

        val fallbackBerth = fallbackBerths.firstOrNull()

        if (fallbackBerth == null) {
            log.debug { "Could not resolve berth using fallback (search pattern = ${visit.berthName})" }
        }

        return fallbackBerth
    }

    private fun getUniqueBerthIdForVopakTerminal(
        berthName: String?,
        terminalName: String?,
    ): String? {
        if (berthName == null && terminalName == null) {
            // To resolve the Vopak alias we need the berth and terminal name
            return null
        }

        // TODO rethink this approach, maybe moving it to Poma if we have to add more besides Vopak
        return when (terminalName) {
            // SGSIN terminals
            "VTS SAKRA" -> {
                when (berthName) {
                    "JETTY 1" -> "VOPAK SAKRA TERMINAL OSK 1"
                    "JETTY 1A" -> "VOPAK SAKRA TERMINAL OSK 1A"
                    "JETTY 2" -> "VOPAK SAKRA TERMINAL OSK 2"
                    else -> null
                }
            }
            "VTS BANYAN" -> {
                when (berthName) {
                    "JETTY 1" -> "VOPAK BANYAN OBV 1"
                    "JETTY 2" -> "VOPAK BANYAN OBV 2"
                    "JETTY 3" -> "VOPAK BANYAN OBV 3"
                    "JETTY 4" -> "VOPAK BANYAN OBV 4"
                    "JETTY 5" -> "VOPAK BANYAN OBV 5"
                    "JETTY 6" -> "VOPAK BANYAN OBV 6"
                    "JETTY 7" -> "VOPAK BANYAN OBV 7"
                    else -> null
                }
            }
            "VTS SEBAROK" -> {
                when (berthName) {
                    "JETTY 2" -> "SHELL BUKOM OSV 4"
                    "JETTY 3" -> "SHELL BUKOM OSV 3"
                    "JETTY 4" -> "SHELL BUKOM OSV 2"
                    "JETTY 5" -> "SHELL BUKOM OSV 13"
                    "JETTY 6" -> "SHELL BUKOM OSV 9"
                    "JETTY 7" -> "SHELL BUKOM OSV 8"
                    "JETTY 8" -> "SHELL BUKOM OSV 12"
                    "JETTY 9" -> "SHELL BUKOM OSV 11"
                    "JETTY 10" -> "SHELL BUKOM OSV 10"
                    else -> null
                }
            }
            "VTS PENJURU" -> {
                when (berthName) {
                    "JETTY 1" -> "VOPAK PENJURU OVPJ 1"
                    "JETTY 2" -> "VOPAK PENJURU OVPJ 2"
                    "JETTY 3" -> "VOPAK PENJURU OVPJ 3"
                    else -> null
                }
            }

            // USHOU terminal
            "DEER PARK" -> {
                when (berthName) {
                    "1SD" -> "VOPAK1-95.0964799331069_29.74454115076873"
                    "2SD" -> "VOPAK2-95.09727789971701_29.74599303113119"
                    "3SD" -> "VOPAK3-95.09790965122484_29.7477925526672"
                    "4SD" -> "VOPAK3-95.09790965122484_29.7477925526672"
                    "5SD" -> "VOPAK5-95.09621543551528_29.74903779207846"
                    else -> null
                }
            }

            else -> null
        }
    }

    private fun List<PortcallVisit>.fillUniqueBerthIds(port: String): List<PortcallVisit> {
        return this.map { visit ->
            if (visit.uniqueBerthId == null) {
                // try resolve location first via the Vopak mechanism before using Poma
                val visitBerth = visit.berthName?.uppercase()
                val visitTerminal = visit.terminal?.uppercase()

                val vopakUniqueBerthId = getUniqueBerthIdForVopakTerminal(visitBerth, visitTerminal)

                if (vopakUniqueBerthId == null) {
                    val berth = resolveBerthForVisit(visit, port)

                    visit.copy(uniqueBerthId = berth?._id)
                } else {
                    visit.copy(uniqueBerthId = vopakUniqueBerthId)
                }
            } else {
                visit
            }
        }
    }

    fun Portcall.getUpdatedPortcall(
        source: ScheduledTaskType = this.source,
        status: PortcallStatus = this.status,
        newVisits: List<PortcallVisit>,
        startTimeType: UpdateType = this.startTimeType,
        vesselAgent: String? = this.vesselAgent,
        purpose: Set<PortcallPurpose> = this.purpose,
        portcallAlias: Set<PortcallAlias> = this.portcallAlias,
        product: String? = this.product,
        pilot: String? = this.pilot,
        towingCompany: String? = this.towingCompany,
    ): Portcall {
        // Fill the unique berth ids for the current and new visits before we start matching them.
        val currentVisits = this.visits.fillUniqueBerthIds(this.port)
        val newVisitsWithUniqueBerthIds = newVisits.fillUniqueBerthIds(this.port)

        val updatedVisits = this.getUpdatedVisits(currentVisits, newVisitsWithUniqueBerthIds, startTimeType)
        return this.copy(
            source = source,
            status = status,
            startTime = getStartTime(startTime, updatedVisits),
            startTimeType = startTimeType,
            vesselAgent = vesselAgent,
            visits = updatedVisits,
            purpose = purpose,
            portcallAlias = portcallAlias,
            product = product,
            pilot = pilot,
            towingCompany = towingCompany,
        )
    }

    private fun Portcall.getUpdatedVisits(
        currentVisits: List<PortcallVisit>,
        newVisits: List<PortcallVisit>,
        newStartTimeType: UpdateType,
    ): List<PortcallVisit> {
        // keep the currentVisits as the starting point
        var updatedVisits = currentVisits

        // for every newVisit, check if there is a match. If so, update the visit details
        for (newVisit in newVisits) {
            if (isNotValidNewVisit(newVisit)) {
                continue
            }
            val berth = newVisit.berthName
            val berthId = newVisit.uniqueBerthId
            // We can't update the visits if we don't have a provided berth or found a poma berth id
            updatedVisits = if (berth != null || berthId != null) {
                // update the visit details if there is a matching visit
                val visitRefTime = newVisit.berthAta ?: newVisit.berthEta
                val terminalName = newVisit.terminal
                val matchingVisit = getMatchingVisit(updatedVisits, berthId, berth, terminalName, visitRefTime, newVisit.arrivalMovementId)

                if (matchingVisit != null) {
                    val updatedVisit = if (matchingVisit.berthName == newVisit.berthName) {
                        // it's a same berth-visit, then data update
                        PortcallVisit(
                            berthEta = newVisit.berthEta ?: matchingVisit.berthEta,
                            berthAta = newVisit.berthAta ?: matchingVisit.berthAta,
                            berthAtd = newVisit.berthAtd ?: matchingVisit.berthAtd,
                            berthEtd = newVisit.berthEtd ?: matchingVisit.berthEtd,
                            terminal = newVisit.terminal ?: matchingVisit.terminal,
                            uniqueBerthId = newVisit.uniqueBerthId ?: matchingVisit.uniqueBerthId,
                            berthName = newVisit.berthName ?: matchingVisit.berthName,
                            arrivalMovementId = newVisit.arrivalMovementId ?: matchingVisit.arrivalMovementId,
                            departureMovementId = newVisit.departureMovementId ?: matchingVisit.departureMovementId,
                            connected = newVisit.connected ?: matchingVisit.connected,
                            pilotIncoming = newVisit.pilotIncoming ?: matchingVisit.pilotIncoming,
                            pilotOutgoing = newVisit.pilotOutgoing ?: matchingVisit.pilotOutgoing,
                            draughtIncoming = newVisit.draughtIncoming ?: matchingVisit.draughtIncoming,
                            draughtOutgoing = newVisit.draughtOutgoing ?: matchingVisit.draughtOutgoing,
                            visitType = newVisit.visitType,
                        )
                    } else {
                        // else, it's berth correction (going to another Berth)
                        log.info {
                            "Portcall ${this.portcallId} got a berth correction for visit " +
                                "(arrivalMovement: ${matchingVisit.arrivalMovementId}) " +
                                "from '${matchingVisit.berthName}' to '${newVisit.berthName}' "
                        }
                        // then, old visit's timestamps are invalid, hence, returning directly the newVisit
                        newVisit
                    }

                    eventService.onVisitChanges(
                        portcall = this,
                        berthId = berthId,
                        berth = berth,
                        currentVisit = matchingVisit,
                        updatedVisit = updatedVisit,
                        newStartTimeType = newStartTimeType,
                    )

                    updatedVisits - matchingVisit + updatedVisit
                } else {
                    eventService.onNewVisit(
                        portcall = this,
                        berthId = berthId,
                        berth = berth,
                        newVisit = newVisit,
                        newStartTimeType = newStartTimeType,
                    )

                    updatedVisits + newVisit
                }
            } else {
                updatedVisits
            }
        }

        return sortPortcallVisits(updatedVisits)
    }

    /**
     * Auxiliary method to obtain the matching visit in the [visits] list by the given berth arguments.
     */
    open fun getMatchingVisit(
        visits: List<PortcallVisit>,
        berthId: String?,
        berth: String?,
        terminal: String?,
        refTime: Date?,
        arrivalMovementId: String?,
    ): PortcallVisit? =
        visits.firstOrNull { visit ->
            isSamePortcallVisit(visit, berthId, berth, terminal, refTime)
        }

    private fun isSamePortcallVisit(
        visit: PortcallVisit,
        berthId: String?,
        berth: String?,
        terminal: String?,
        refStartTime: Date?,
    ): Boolean {
        val isMatchingBerthId = berthId != null && visit.uniqueBerthId == berthId
        val isMatchingBerth = visit.berthName == berth
        // If terminal is missing, assume it's the same berth.
        // Currently only Vopak nominations given same berth for different terminals
        val isMatchingTerminal = terminal == null || visit.terminal == terminal
        return if (isMatchingBerthId || (isMatchingBerth && isMatchingTerminal)) {
            // it's a matchingBerth if the refStartTime is before the berthAtd
            val visitEnd = visit.berthAtd
            refStartTime == null || visitEnd == null || refStartTime < visitEnd
        } else {
            false
        }
    }

    /**
     * Method to sort Portvisits if available based on berthEta or berthAta
     * @param inputs the portcallVisits to be sorted
     * @return the sorted list of portVisits
     */
    private fun sortPortcallVisits(inputs: List<PortcallVisit>): List<PortcallVisit> =
        inputs.sortedBy {
            it.berthAta ?: it.berthEta ?: it.berthAtd ?: it.berthEtd ?: Date(Long.MAX_VALUE)
        }

    /**
     * Update the translated [portcallsFromService] received from this external service.
     * @param portcallsFromService Actual translated portcalls received from the external service
     * @return The list of all portcalls converted/updated
     */
    internal fun updatePortcall(portcallsFromService: List<Portcall>): List<PortcallUpdateResult> {
        // get portcalls locally known
        val currentPortcalls = portcallDataSource.get(portcallsFromService.map { it.portcallId })
            .associateBy { it.portcallId }
        val result = mutableListOf<PortcallUpdateResult>()
        // update local portcall instance if there is a change by processing each portcall from external service
        for (portcallFromService in portcallsFromService) {
            val currentPortcall = currentPortcalls[portcallFromService.portcallId]
            if (currentPortcall != null && currentPortcall.source != portcallFromService.source && !allowOtherSourcePortcallUpdate) {
                log.info {
                    "${portcallFromService.source} not allowed to update other source (${currentPortcall.source}) portcalls (${currentPortcall.portcallId})."
                }
                continue
            }
            val updatedPortcall = getUpdatedPortcall(portcallFromService, currentPortcall, getTimestampFrom())
            // create or update the portcallFromService
            if (updatedPortcall != null) {
                log(
                    portcall = updatedPortcall,
                    message = "PortCall updated by task: ${updatedPortcall.source} via ${this::class.java.simpleName}",
                )
                portcallDataSource.createOrUpdate(updatedPortcall)

                if (currentPortcall != null) {
                    if (currentPortcall.source != updatedPortcall.source) {
                        log(
                            portcall = currentPortcall,
                            message = "PortCall update will change source from: ${currentPortcall.source} to ${updatedPortcall.source} via ${this::class.java.simpleName}",
                        )
                    }
                    eventService.onUpdatedPortcall(currentPortcall = currentPortcall, updatedPortcall = updatedPortcall)
                } else {
                    val newPortcall = updatedPortcall.copy(
                        visits = updatedPortcall.visits.fillUniqueBerthIds(updatedPortcall.port),
                    )
                    eventService.onNewPortcall(newPortcall = newPortcall)
                }
            }
            result.add(PortcallUpdateResult(portcallFromService.imo, portcallFromService, currentPortcall, updatedPortcall))
        }
        return result
    }

    /**
     * Update the portcall if necessary based on any changes detected in the [portcallFromService]
     * @param existing    the existing portcall
     * @param portcallFromService the new portcall
     * @param timestampFrom the reference timestamp for fetching future portcall updates
     */
    private fun getUpdatedPortcall(
        portcallFromService: Portcall,
        existing: Portcall?,
        timestampFrom: Date,
    ): Portcall? {
        // for all new portcalls started after the given timestamp, create a new portcall instance
        return if (existing == null) {
            if (portcallFromService.startTime.after(timestampFrom)) {
                log(portcallFromService, "Creating portcall with startTime: ${portcallFromService.startTime.toInstant()}")
                portcallFromService
            } else {
                log(
                    portcallFromService,
                    "Not creating portcall as startTime: ${portcallFromService.startTime.toInstant()} is before: $timestampFrom",
                    Level.WARNING,
                )
                null
            }
        }
        // check for any portcall related updates.
        else if (portcallFromService != existing) {
            // We can just blindly return the portcallFromService (what if there were already solved values, such as vesselAgent?
            existing.copy(
                portcallAlias = existing.portcallAlias + portcallFromService.portcallAlias,
                source = portcallFromService.source,
                startTime = portcallFromService.startTime,
                startTimeType = portcallFromService.startTimeType,
                status = portcallFromService.status.takeIf { it != PortcallStatus.UNKNOWN } ?: existing.status,
                endTime = portcallFromService.endTime ?: existing.endTime,
                portAtaTime = portcallFromService.portAtaTime ?: existing.portAtaTime,
                portAtdTime = portcallFromService.portAtdTime ?: existing.portAtdTime,
                etd = portcallFromService.etd ?: existing.etd,
                enteredPBP = portcallFromService.enteredPBP,
                pilotBoardingPlaceType = portcallFromService.pilotBoardingPlaceType,
                vesselAgent = portcallFromService.vesselAgent ?: existing.vesselAgent,
                originUnlocode = portcallFromService.originUnlocode ?: existing.originUnlocode,
                destinationUnlocode = portcallFromService.destinationUnlocode ?: existing.destinationUnlocode,
                purpose = existing.purpose + portcallFromService.purpose,
                visits = portcallFromService.visits,
                eosAtaTime = portcallFromService.eosAtaTime ?: existing.eosAtaTime,
                eosAtdTime = portcallFromService.eosAtdTime ?: existing.eosAtdTime,
                product = portcallFromService.product ?: existing.product,
                pilot = portcallFromService.pilot ?: existing.pilot,
                // Remember that Movement.tugName actually refers to the Towing Company. See more details in the Movement model.
                towingCompany = portcallFromService.towingCompany ?: existing.towingCompany,
            )
        } else {
            null.also {
                log.debug { "Portcall ${existing.portcallId} to update is equal to existing." }
            }
        }
    }
}
