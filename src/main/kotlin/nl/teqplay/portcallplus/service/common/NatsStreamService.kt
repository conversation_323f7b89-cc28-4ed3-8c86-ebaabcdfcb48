package nl.teqplay.portcallplus.service.common

import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.aisengine.nats.stream.revents.model.ReventsEventStreamOptions
import nl.teqplay.portcallplus.service.internal.nats.AreaEventHandler
import nl.teqplay.skeleton.nats.NatsConsumerStream
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Service

@Service
class NatsStreamService(
    private val eventStreamService: EventStreamService,
    private val natsConsumerStream: NatsConsumerStream<Event>,
    private val eventHandles: List<AreaEventHandler>,
) {
    /**
     * Setup nats consumer with on message handler
     */
    @EventListener(ApplicationReadyEvent::class)
    private fun startupConsumer() {
        eventStreamService.consume(
            stream = natsConsumerStream,
            suffix = null,
            subjects = listOf("event.area.>"),
            enableStateEvents = false,
            revents = ReventsEventStreamOptions(false),
        ) { event, message ->
            eventHandles.forEach {
                it.onMessage(event)
            }
            message.ack()
        }
    }
}
