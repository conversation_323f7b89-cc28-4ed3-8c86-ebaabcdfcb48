package nl.teqplay.portcallplus.service.common

import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.ServiceModel
import nl.teqplay.portcallplus.controller.GenericControllerEndpoints
import nl.teqplay.portcallplus.datasource.ActivityDataSource
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.datasource.ScheduledTaskRunDataSource
import nl.teqplay.portcallplus.datasource.ServiceFetchCounterDataSource
import nl.teqplay.portcallplus.model.ScheduledTaskTypeFields
import nl.teqplay.portcallplus.model.ScheduledTaskTypeFieldsProxy
import nl.teqplay.portcallplus.model.data.ScheduledTaskRun
import nl.teqplay.portcallplus.model.data.monitor.SubSystemStatus
import nl.teqplay.portcallplus.service.internal.PoMaService
import nl.teqplay.portcallplus.service.internal.UserService
import nl.teqplay.portcallplus.utils.fetchReadableTimeFromMillis
import nl.teqplay.portcallplus.utils.fetchTimestamp
import nl.teqplay.portcallplus.utils.log
import nl.teqplay.skeleton.actuator.DEGRADED
import org.springframework.boot.actuate.health.Status
import java.time.Duration
import java.util.Date
import java.util.EnumMap
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledFuture
import java.util.concurrent.TimeUnit.MILLISECONDS
import java.util.concurrent.TimeUnit.MINUTES
import java.util.concurrent.atomic.AtomicInteger
import java.util.logging.Level

/**
 * Gives a template for all services that need the following features:
 * 1. Handle portcall details from an external service
 * 2. Schedueled task to call an external service at a regular interval
 * 3. Keep track of the amount of requests done to these external service
 */
private val scheduledTasks = hashMapOf<ScheduledTaskType, ScheduledFuture<*>>()
const val RUN_SUCCESSFUL = "Run successful"

/**
 * Generic abstract class, defining common functions to trigger tasks and update portcalls as a result
 */
abstract class TaskService(
    private val scheduledTaskRunDataSource: ScheduledTaskRunDataSource,
    val activityDataSource: ActivityDataSource,
    serviceFetchCounterDataSource: ServiceFetchCounterDataSource,
    private val portcallDataSource: PortcallDataSource,
    private val scheduledTaskTypeFieldsProxy: ScheduledTaskTypeFieldsProxy,
    private val userService: UserService,
    pomaService: PoMaService,
    eventService: EventService,
) : TaskCounterService(activityDataSource, serviceFetchCounterDataSource, portcallDataSource, pomaService, eventService) {
    // incremental triggering of external tasks
    private var failureIncrement: EnumMap<ScheduledTaskType, AtomicInteger> = EnumMap(ScheduledTaskType::class.java)

    protected val lastTaskRunByType: MutableMap<ScheduledTaskType, ScheduledTaskRun> = mutableMapOf()

    /**
     * Returns the list of all [ScheduledTaskType] being implemented in this class
     */
    abstract fun getTasks(): List<ScheduledTaskType>

    /**
     * Return true if this task is enabled in the config
     */
    abstract val isEnabled: Boolean

    /**
     * Method to be implemented by all [TaskService] implementations, to fetch/return all the [ServiceModel] paired with their IMO.
     */
    abstract override fun getServiceModels(taskType: ScheduledTaskType): Collection<Pair<String, ServiceModel>>

    /**
     * Task used to trigger all defined tasks that this service is implemented for.
     */
    fun startAllTasks() {
        lastTaskRunByType.putAll(
            scheduledTaskRunDataSource
                .get(this.getTasks().map { it.toString() })
                .associateBy { it.taskType }
                .toMutableMap(),
        )
        if (isEnabled) {
            getTasks().forEach { taskType ->
                triggerRunnableTask(taskType = taskType)
            }
        } else {
            getTasks().forEach { taskType ->
                val taskTypeFields = scheduledTaskTypeFieldsProxy.getFields(taskType)
                scheduledTaskRunDataSource.createOrUpdate(
                    ScheduledTaskRun(
                        taskType,
                        Date(),
                        taskName = taskTypeFields.taskFullName,
                        enabled = taskTypeFields.enabled,
                    ),
                )
            }
            log(message = "Task service: ${this::class.java.simpleName} is not enabled")
        }
    }

    /**
     * Task used to trigger all defined tasks that this service is implemented for.
     */
    fun stopAllTasks() {
        getTasks().forEach { taskType ->
            stopTask(taskType, true)
        }
    }

    /**
     * Method to stop a specific task.
     */
    open fun stopTask(
        taskType: ScheduledTaskType,
        forceStop: Boolean,
    ): Boolean {
        val isCancelled = cancelAndRemoveTask(taskType, forceStop)
        val message = "Request to stop task: $taskType with force: $forceStop by user: " +
            "${userService.getCurrentUsername()} was ${if (isCancelled) "successful" else "unsuccessful"}"
        log(message = message)
        val lastFetchIndex = scheduledTaskRunDataSource.get(taskType)?.lastFetchIndex
        // update the task run details to show in the /status that it is not running
        saveLastRun(scheduledTaskTypeFieldsProxy.getFields(taskType), "$message at ${fetchTimestamp(Date())}", 0, lastFetchIndex)
        return isCancelled
    }

    /**
     * Triggers the task related to [taskType]
     * @param taskType The type of task for which is to be triggered
     * @param force If true, ignores the last run of this task and runs it now
     * @param initialDelayInMillis Appends the total delay by this initialDelay. Defaulted to 0L (no delay)
     */
    fun triggerRunnableTask(
        taskType: ScheduledTaskType,
        force: Boolean = false,
        isRetryOnFailure: Boolean = false,
    ): Boolean {
        // cancel the existing task, if one is already triggered
        cancelAndRemoveTask(taskType, true)
        val lastRunTime = scheduledTaskRunDataSource.get(taskType)?.lastRun
        val runnableTask = ScheduledTask(scheduledTaskTypeFieldsProxy.getFields(taskType), this)

        val totalInitialDelayInMillis = if (isRetryOnFailure) {
            failureIncrement.putIfAbsent(taskType, AtomicInteger(0))
            failureIncrement[taskType]!!.getAndIncrement()
            // increment the initial delay until 1hour, after which reset the increments
            MINUTES.toMillis(2L)
        } else {
            failureIncrement.remove(taskType)
            0
        }
        scheduledTasks[taskType] = triggerScheduledTask(
            runnableTask, taskType, lastRunTime, force, totalInitialDelayInMillis,
        )
        return isRunning(taskType)
    }

    /**
     * Method to check if the given [taskType] is running
     */
    fun isRunning(taskType: ScheduledTaskType) = scheduledTasks[taskType]?.getDelay(MILLISECONDS) != null

    /**
     * Method to check the next run time of the given [taskType]
     */
    fun getTimeToNextRun(taskType: ScheduledTaskType): String? {
        val taskTypeFields = scheduledTaskTypeFieldsProxy.getFields(taskType)
        return scheduledTasks[taskType]?.getDelay(MILLISECONDS)?.let {
            // if this task is not running indefinitely, add the cycle period if value is lesser than 0
            val nextTaskRunIn = if (taskTypeFields.cycleDuration != Duration.ofHours(0) && it < 0) {
                it + taskTypeFields.cycleDuration.toMillis()
            } else {
                it
            }
            fetchReadableTimeFromMillis(nextTaskRunIn)
        }
    }

    /**
     * This method is expected to be called only in the actual implementation of the Runnable method
     * Updates the last run time of the given [taskType] if successful. If a [runMessage] is seen, will only update
     * the failure message to the last run for [taskType]
     */
    internal fun saveLastRun(
        taskTypeFields: ScheduledTaskTypeFields,
        runMessage: String = RUN_SUCCESSFUL,
        lastTimeTakenToRun: Long,
        lastFetchIndex: String? = null,
        isFailure: Boolean = false,
    ) {
        val currentTime = Date()
        val lastUpdatedPortcallTime = activityDataSource.getLastActivityWithUpdates(taskTypeFields.type)?.updateTimestamp
        // fetch the last saved taskRun details to preserve the last failed details
        val taskRun = scheduledTaskRunDataSource.get(taskTypeFields.type)
        val lastIndex = lastFetchIndex ?: taskRun?.lastFetchIndex

        val updatedTaskRun = if (isFailure) {
            log(message = "[${taskTypeFields.type}]: Failed to fetch data. Message: $runMessage", level = Level.WARNING)
            ScheduledTaskRun(
                taskTypeFields.type, currentTime, lastIndex, runMessage, lastTimeTakenToRun, taskRun?.lastSuccessRun, currentTime,
                runMessage, lastUpdatedPortcallTime, taskTypeFields.taskFullName, taskTypeFields.enabled,
            )
        } else {
            ScheduledTaskRun(
                taskTypeFields.type, currentTime, lastIndex, runMessage, lastTimeTakenToRun, currentTime, taskRun?.lastFailedRun,
                taskRun?.lastFailedRunMessage, lastUpdatedPortcallTime, taskTypeFields.taskFullName, taskTypeFields.enabled,
            )
        }
        lastTaskRunByType[taskTypeFields.type] = updatedTaskRun
        scheduledTaskRunDataSource.createOrUpdate(updatedTaskRun)
    }

    /**
     * Returns a map of all tasks that are scheduled
     */
    internal fun getScheduledTaskDetails(): Map<ScheduledTaskType, String> {
        val result = sortedMapOf<ScheduledTaskType, String>()
        scheduledTasks.forEach { scheduledTask ->
            result[scheduledTask.key] = getTimeToNextRun(scheduledTask.key)
        }
        return result
    }

    /**
     * Triggers the given task: [task] based on the properties in [taskType]. Also delays the task if there was a recent
     * call already triggered for this.
     * @param taskType Needed to calculate the delay to trigger this task.
     * @param force If true, ignores the last run of this task and runs it now
     * @param initialDelayInMillis The time in milliseconds that the task must be delayed
     */
    private fun triggerScheduledTask(
        task: Runnable,
        taskType: ScheduledTaskType,
        lastRunTime: Date?,
        force: Boolean = false,
        initialDelayInMillis: Long = 0L,
    ): ScheduledFuture<*> {
        val taskTypeFields = scheduledTaskTypeFieldsProxy.getFields(taskType)
        var totalDelay = 0L
        if (!force && lastRunTime != null) {
            val currentTime = Date()
            val expectedNextRun = fetchTimestamp(lastRunTime, taskTypeFields.cycleDuration)
            if (expectedNextRun.after(currentTime)) {
                totalDelay = expectedNextRun.time - currentTime.time
            }
        }
        // add the initial delay requested
        totalDelay += initialDelayInMillis
        return triggerRunnableInCycle(task, totalDelay, taskTypeFields.cycleDuration, taskTypeFields.taskFullName)
    }

    private val executor = Executors.newScheduledThreadPool(Runtime.getRuntime().availableProcessors())

    /**
     * For a given [task], this method will schedule a task every [cycleDuration]. With an initial given [initialDelayInMillis]
     * if a cycle period of 0 is given it triggers the task only once
     */
    private fun triggerRunnableInCycle(
        task: Runnable,
        initialDelayInMillis: Long,
        cycleDuration: Duration,
        logMessage: String? = null,
    ): ScheduledFuture<*> {
        if (logMessage != null) {
            log(
                message = "Triggering $logMessage in $cycleDuration (initialDelayInMillis: ${fetchReadableTimeFromMillis(
                    initialDelayInMillis,
                )})",
                level = Level.INFO,
            )
        } else {
            log(
                message = "Triggering a Runnable task in $cycleDuration (initialDelayInMillis: ${fetchReadableTimeFromMillis(
                    initialDelayInMillis,
                )})",
                level = Level.INFO,
            )
        }

        if (cycleDuration == Duration.ofHours(0)) {
            return executor.schedule(task, initialDelayInMillis, MILLISECONDS)
        }
        return executor.scheduleAtFixedRate(task, initialDelayInMillis, cycleDuration.toMillis(), MILLISECONDS)
    }

    /**
     * Cancels the given [taskType] and removes it from the [scheduledTasks]
     */
    private fun cancelAndRemoveTask(
        taskType: ScheduledTaskType,
        force: Boolean,
    ): Boolean {
        try {
            scheduledTasks[taskType]?.cancel(force)
        } catch (ex: Exception) {
            ex.printStackTrace()
            log(message = "Error seen while cancelling the task: $ex")
        }
        scheduledTasks.remove(taskType)
        return !isRunning(taskType)
    }

    override fun getSubSystemStati(): List<SubSystemStatus> {
        return lastTaskRunByType.map { (_, lastRun) ->
            /** Turn ScheduledTaskRun information like: enabled, last run and last failed run into Status */
                val (status, statusMessage) = getServiceStatus(lastRun)

            /** If the last run failed set the errMessage to the error message of the task*/
            val errorMessage = if (status == Status.DOWN) {
                "[${lastRun.lastFailedRun}] ${lastRun.lastFailedRunMessage}"
            } else {
                null
            }
            /**
             * return SubSystem status with the information from the ScheduledTaskRun. Add the generated status and
             * and error message. For the start and stop url take the controller endpoints and replace {variable}
             * from the string with the task type.
             */
            SubSystemStatus(
                name = lastRun.taskType.toString(),
                enabled = lastRun.enabled,
                status = status,
                detailedStatus = statusMessage,
                errorMessage = errorMessage,
                lastUpdated = lastRun.lastRun.toInstant().toString(),
                start = "${GenericControllerEndpoints.PREFIX}${GenericControllerEndpoints.FORCE_RUN}".replace(
                    "\\{[A-z]\\w+}"
                        .toRegex(),
                    lastRun.taskType.toString(),
                ),
                stop = "${GenericControllerEndpoints.PREFIX}${GenericControllerEndpoints.FORCE_STOP}".replace(
                    "\\{[A-z]\\w+}"
                        .toRegex(),
                    lastRun.taskType.toString(),
                ),
            )
        }
    }

    /**
     * Get the status of a task based on the last run
     */
    private fun getServiceStatus(lastRun: ScheduledTaskRun): Pair<Status, String> {
        val taskTypeFields = scheduledTaskTypeFieldsProxy.getFields(lastRun.taskType)
        val lastUpdatedPortcallTime = lastRun.lastUpdatedPortcallTime
        val cycleDuration = taskTypeFields.cycleDuration
        return when {
            // Is the Service enabled
            !isEnabled -> Status.UP to "Disabled"

            // Is the last success is longer ago than the last fail + the allowed time for a service to be down
            lastRun.lastFailedRun != null && lastRun.lastSuccessRun != null &&
                lastRun.lastFailedRun.after(
                    fetchTimestamp(
                        lastRun.lastSuccessRun,
                        taskTypeFields.maxDownTime + isHealthyFailDuration,
                    ),
                ) -> Status(DEGRADED) to "The service is down for longer than the allowed downtime"

            // Has the lastRun started more than the cycle duration ago + leniency in the form of a configurable isHealthLastRunDuration
            Date().after(
                fetchTimestamp(
                    lastRun.lastRun,
                    cycleDuration + Duration.ofMillis(lastRun.lastRunDuration) + isHealthyLastRunDuration,
                ),
            ) -> Status(DEGRADED) to "Failed because the last run started more than the cycle duration + last run time ago"

            // If the last updated portcall by the service is longer ago than the expected time for the service to be down
            lastUpdatedPortcallTime != null && Date().after(
                fetchTimestamp(
                    lastUpdatedPortcallTime,
                    cycleDuration + taskTypeFields.lastPortcallUpdateDuration,
                ),
            ) ->
                Status(DEGRADED) to "Last updated portcall is longer ago than ${ cycleDuration + taskTypeFields
                    .lastPortcallUpdateDuration }, last was at $lastUpdatedPortcallTime"

            !isRunning(lastRun.taskType) -> Status(DEGRADED) to "The task is detected to not be running"

            else -> Status.UP to "Ran succesfully"
        }
    }
}
