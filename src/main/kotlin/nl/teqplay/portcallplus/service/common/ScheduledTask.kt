package nl.teqplay.portcallplus.service.common

import nl.teqplay.portcallplus.model.ScheduledTaskTypeFields
import nl.teqplay.portcallplus.model.data.ServiceFetchCounter
import nl.teqplay.portcallplus.utils.DURATION_FORMAT_MS
import nl.teqplay.portcallplus.utils.fetchReadableTimeFromMillis
import nl.teqplay.portcallplus.utils.log
import java.time.Duration
import java.util.Date
import java.util.logging.Level
import kotlin.concurrent.thread
import kotlin.system.measureTimeMillis

class ScheduledTask(
    private val taskTypeFields: ScheduledTaskTypeFields,
    private val taskService: TaskService,
) : Runnable {
    override fun run() {
        val taskFullName = taskTypeFields.taskFullName
        var measureTimeMillis: Long = System.currentTimeMillis()
        try {
            log(message = "Starting $taskFullName")
            measureTimeMillis = measureTimeMillis {
                val serviceModels = taskService.getServiceModels(taskTypeFields.type)
                taskService.updatePortcall(serviceModels, taskTypeFields.type)
            }
            afterRun(measureTimeMillis)
        } catch (ex: Exception) {
            log(
                message = "Exception seen while executing task: $taskFullName. Message: $ex. " +
                    "Triggering the task again with delay",
                level = Level.SEVERE,
            )

            // save the failure in the lastRun for diagnostics
            taskService.saveLastRun(
                taskTypeFields = taskTypeFields,
                runMessage = "Exception: $ex",
                lastTimeTakenToRun = System.currentTimeMillis() - measureTimeMillis,
                isFailure = true,
            )

            // create a new failure entry for this task
            val fetchCounter = ServiceFetchCounter(Date(), taskTypeFields.type)
            fetchCounter.failedImoNumbers.add(ex.toString())
            taskService.updateServiceFetchCounter(fetchCounter)

            // trigger the task in 1 minute again
            thread {
                taskService.triggerRunnableTask(taskType = taskTypeFields.type, force = true, isRetryOnFailure = true)
            }
        }
    }

    /**
     * Perform after run tasks
     */
    private fun afterRun(timeTaken: Long) {
        // only save if the task is periodic
        if (taskTypeFields.cycleDuration != Duration.ofHours(0) && taskService.isRunning(taskTypeFields.type)) {
            taskService.saveLastRun(taskTypeFields, lastTimeTakenToRun = timeTaken)
        }
        log(message = "Finished running ${taskTypeFields.taskFullName} in ${fetchReadableTimeFromMillis(timeTaken, DURATION_FORMAT_MS)}")
    }
}
