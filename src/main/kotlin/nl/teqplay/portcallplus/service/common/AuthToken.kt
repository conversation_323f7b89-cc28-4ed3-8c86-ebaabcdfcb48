package nl.teqplay.portcallplus.service.common

import com.fasterxml.jackson.annotation.JsonAlias
import nl.teqplay.portcallplus.utils.fetchTimestamp
import java.util.Date
import java.util.concurrent.TimeUnit.SECONDS

/**
 * The AuthToken class contains tokens for a user:
 *
 * - token is a short lived token (like for 24 hours)
 * - refreshToken is a long lived token, which can be used to log in (instead of passing username+password)
 *
 * IMPORTANT: this class is copied from the Authenticator project. Don't make changes here
 */
data class AuthToken(
    val userName: String = "",
    @JsonAlias("refresh_token")
    val refreshToken: String = "",
    @JsonAlias("access_token")
    val token: String = "",
    @JsonAlias("token_type")
    val tokenType: String = "",
    @JsonAlias("expiresInSeconds", "expires_in")
    val expiresInSeconds: Int = 0,
    val createdAt: Date? = null,
    /** The users uuid */
    val userUuid: String? = null,
    val scope: String? = null,
    /** Actual expiration time of this token. Updated based on a login response */
    val expiration: Date? = createdAt?.let { fetchTimestamp(it, expiresInSeconds, SECONDS) },
)
