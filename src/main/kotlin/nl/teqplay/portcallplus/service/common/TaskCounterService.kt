package nl.teqplay.portcallplus.service.common

import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.ServiceModel
import nl.teqplay.portcallplus.datasource.ActivityDataSource
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.datasource.ServiceFetchCounterDataSource
import nl.teqplay.portcallplus.model.data.Activity
import nl.teqplay.portcallplus.model.data.ServiceFetchCounter
import nl.teqplay.portcallplus.model.data.ServiceFetchDetail
import nl.teqplay.portcallplus.model.data.monitor.SubSystemStatus
import nl.teqplay.portcallplus.model.service.PortcallUpdateResult
import nl.teqplay.portcallplus.service.internal.PoMaService
import nl.teqplay.portcallplus.utils.log
import nl.teqplay.portcallplus.utils.unsortedEquals
import java.time.Duration
import java.util.Date
import java.util.logging.Level

/**
 * Gives a template for all services that need the following features:
 * 1. Handle portcall details from an external service
 * 2. Keep track of the amount of requests done to these external service
 */
abstract class TaskCounterService(
    private val activityDataSource: ActivityDataSource,
    protected val serviceFetchCounterDataSource: ServiceFetchCounterDataSource,
    portcallDataSource: PortcallDataSource,
    pomaService: PoMaService,
    eventService: EventService,
) : SourceService(portcallDataSource, pomaService, eventService) {
    /**
     * Implementations of TaskCounterServices do not poll for new [ServiceModel]s. Instead they receive it via queues or
     * similar implementaitons. So this method always return emptyList
     */
    override fun getServiceModels(taskType: ScheduledTaskType): Collection<Pair<String, ServiceModel>> = emptyList()

    /**
     * Convert the given [serviceModel] received from this external service to an internal representation in [Portcall]
     * @param serviceModel The model received from the this external service
     * @param imoNumber The vessel imoNumber for which this data refers to
     * @param taskType The taskType is requesting this action.
     */
    abstract fun convertToPortcall(
        serviceModel: ServiceModel,
        imoNumber: String,
        taskType: ScheduledTaskType,
    ): Portcall

    /**
     * Method to indicate if we are charged by this external service. If yes, counters will be updated also if there is
     * no updates to an existing portcall. Probably to indicate how to reduce cost, e.g. reducing the cycle or so.
     */
    open fun isCreditBased() = false

    /**
     * Returns true if the given [portcallFromService] should be process for the [taskType] properties:
     * - [ScheduledTaskType.processIfPortcallFound]
     * - [ScheduledTaskType.processIfInBerth]
     * @param imoNumber Given to make sure some logging is done in case no portcall is given
     */
    open fun canShipImoBeProcessed(
        imoNumber: String,
        portcallFromService: Portcall?,
        taskType: ScheduledTaskType,
    ): Boolean {
        // imo should be defined and it should not be a ferry
        return taskType != ScheduledTaskType.UNKNOWN && imoNumber != "0" && imoNumber.isNotBlank()
    }

    /**
     * Convert the given [serviceModel] received from this external service to an internal representation in [Portcall]
     * @param serviceModel The model received from the this external service
     * @param imoNumber The vessel imoNumber for which this data refers to
     */
    override fun convertToPortcall(
        serviceModel: ServiceModel,
        imoNumber: String,
    ): Portcall {
        return convertToPortcall(serviceModel, imoNumber, ScheduledTaskType.UNKNOWN)
    }

    /**
     * Update the existing and portcall received from this external service
     * @param serviceModels Actual <imoNumber, [ServiceModel]> received from the external service
     */
    open fun updatePortcall(
        serviceModels: Collection<Pair<String, ServiceModel>>,
        taskType: ScheduledTaskType,
    ): List<PortcallUpdateResult> {
        // save all the activities received from any service
        val imoNumbers = hashSetOf<String>()
        serviceModels.forEach { imoNumbers.add(it.first) }
        val serviceFetchCounter = getServiceFetchCounter(taskType)

        activityDataSource.insert(Activity(imoNumbers, serviceFetchCounter.taskType))

        // create/update portcalls for the service models
        val portcallsFromService = mutableListOf<Portcall>()
        serviceModels.forEach { (imoNumber, serviceModel) ->
            try {
                if (imoNumber != "0" && imoNumber.isNotBlank()) {
                    // collect a mapping of all <imoNumber, Portcall> from this external service
                    val portcall = convertToPortcall(serviceModel, imoNumber, serviceFetchCounter.taskType)
                    if (canShipImoBeProcessed(imoNumber, portcall, serviceFetchCounter.taskType)) {
                        portcallsFromService.add(portcall)
                    }
                }
            } catch (ex: Exception) {
                log(
                    imoNumber = imoNumber,
                    message = "Not processing ${serviceModel::class.java.simpleName} update: $serviceModel. Message: ${ex.localizedMessage}",
                    level = Level.WARNING,
                )
            }
        }
        val updatedPortcallResult = super.updatePortcall(portcallsFromService)

        // update the counters
        updateCounters(updatedPortcallResult, serviceFetchCounter)
        return updatedPortcallResult
    }

    fun updateServiceFetchCounter(counter: ServiceFetchCounter) {
        if (counter.getSize() > 0) {
            serviceFetchCounterDataSource.createOrUpdate(counter)
        }
    }

    private fun updateCounters(
        portcallResult: List<PortcallUpdateResult>,
        counter: ServiceFetchCounter,
    ) {
        portcallResult.forEach {
            // update the counters by comparing the local portcallFromService to the one received from the external service
            updateCounter(it.currentPortcall, it.portcallFromService, getTimestampFrom(), counter)
        }
        updateServiceFetchCounter(counter)
    }

    /**
     * Adds either the portcallId or the imo to relevant categories of the given [fetchCounter] based on the
     * given portcall information
     */
    internal fun updateCounter(
        existing: Portcall?,
        updatedPortcall: Portcall,
        timestampFrom: Date,
        fetchCounter: ServiceFetchCounter,
    ) {
        // assign the id for the counter based on if the linked ship can be processed for the task
        when {
            existing == null -> {
                if (updatedPortcall.startTime.after(timestampFrom)) {
                    fetchCounter.create.add(ServiceFetchDetail(updatedPortcall.portcallId, updatedPortcall.startTime))
                } else {
                    log(
                        updatedPortcall,
                        "Not found, but start date: ${updatedPortcall.startTime} is after configured date of: $timestampFrom",
                    )
                }
            }
            // update the counter if there is a change in the eta
            existing.startTime != updatedPortcall.startTime ->
                fetchCounter.etaUpdate.add(ServiceFetchDetail(updatedPortcall.portcallId, updatedPortcall.startTime))
            // update the counter if there is a change in the etd
            existing.etd != updatedPortcall.etd ->
                fetchCounter.etdUpdate.add(ServiceFetchDetail(updatedPortcall.portcallId, updatedPortcall.etd))
            // update the counter if there is a change in the atd
            existing.portAtdTime != updatedPortcall.portAtdTime ->
                fetchCounter.finish.add(ServiceFetchDetail(updatedPortcall.portcallId, updatedPortcall.portAtdTime))
            // update the counter if there is a change in the vessel agent
            existing.vesselAgent != updatedPortcall.vesselAgent -> fetchCounter.agentUpdate.add(updatedPortcall.portcallId)
            // check if there are any berth updates. We do not add a eventTime for this.
            !existing.visits.unsortedEquals(updatedPortcall.visits) ->
                fetchCounter.berthUpdate.add(ServiceFetchDetail(updatedPortcall.portcallId, null))
            // Only if the service is credit based, add it to the noUpdates list.
            // if there has been no updates. Update the no-updates counter with the imo
            isCreditBased() -> fetchCounter.noUpdates.add(updatedPortcall.imo)
        }
    }

    private fun getServiceFetchCounter(taskType: ScheduledTaskType): ServiceFetchCounter = ServiceFetchCounter(Date(), taskType)

    /**
     * It returns a list of all SubsystemStatus used by the specific service.
     * Mainly used to construct the PortcallPlus global health indicator
     * [HealthConfiguration].[portcallPlusGlobalHealthIndicator].
     */
    abstract fun getSubSystemStati(): List<SubSystemStatus>

    /**
     * Service specific time how long we allow a service to be in the failed state
     */
    protected open val isHealthyFailDuration: Duration = Duration.ofMinutes(0)

    /**
     * Service specific time how much more a task can take compared to its cycleDuration
     */
    protected open val isHealthyLastRunDuration: Duration = Duration.ofMinutes(5)
}
