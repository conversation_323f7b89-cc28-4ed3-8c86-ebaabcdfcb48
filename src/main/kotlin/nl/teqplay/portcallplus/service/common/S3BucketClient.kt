package nl.teqplay.portcallplus.service.common

import nl.teqplay.portcallplus.model.config.S3Properties
import nl.teqplay.portcallplus.utils.log
import nl.teqplay.skeleton.common.exception.NotFoundException
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest
import software.amazon.awssdk.services.s3.model.GetObjectRequest
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request
import software.amazon.awssdk.services.s3.model.S3Object

/**
 * Unlike an S3Client, that it's able to access to different buckets with the set credentials, this class is intended
 * to only access to a specific client for the given credentials.
 * This is so, because we normally use 1:1 to credentials:bucket.
 * So setting a different bucket in an S3Client would normally result in error.
 * Note that if any needed object (i.e. GetObjectRequest) provides the bucket, this will be overriden by this class one.
 */
class S3BucketClient(
    private val s3Properties: S3Properties,
) {
    private val s3Client = with(s3Properties) {
        S3Client.builder()
            .region(Region.of(region))
            .credentialsProvider(
                StaticCredentialsProvider.create(
                    AwsBasicCredentials.create(accesskey, secret),
                ),
            ).build()
    }

    fun getSimpleObject(requestedKey: String): ByteArray {
        val getRequest = with(this.s3Properties) {
            GetObjectRequest.builder()
                .bucket(bucketName)
                .key(requestedKey)
                .build()
        }
        return s3Client.getObject(getRequest)?.readAllBytes()
            ?: throw NotFoundException("Requesting S3 object with key $requestedKey resulted in NULL!")
    }

    fun listObjects(): List<String> {
        var responseWasTruncated = false
        var continuationToken: String? = null
        val fileKeysInBucket = mutableListOf<S3Object>()
        val requestBuilder = ListObjectsV2Request
            .builder()
            .bucket(this.s3Properties.bucketName)
        do {
            // Let's set the continuationToken if there was any.
            continuationToken?.let { requestBuilder.continuationToken(continuationToken) }
            val response = s3Client.listObjectsV2(requestBuilder.build())
            responseWasTruncated = responseWasTruncated || response.isTruncated
            val keysBatch = response.contents()
            fileKeysInBucket.addAll(keysBatch)
            // Let's update the continuationToken if the response is truncated.
            continuationToken = response.nextContinuationToken().takeIf { response.isTruncated }
        } while (response.isTruncated)
        if (responseWasTruncated) {
            log.warn {
                "Listing objects from S3 bucket ${this.s3Properties.bucketName} reached the default max limit (1000)! I've tackled it, but consider taking some actions to make my life easier. Best regards, Portcall+ ;)"
            }
        }
        return fileKeysInBucket.sortedBy { it.lastModified() }.map { it.key() }
    }

    fun delete(fileKey: String): Boolean {
        val deleteObjectRequest = with(this.s3Properties) {
            DeleteObjectRequest.builder()
                .bucket(bucketName)
                .key(fileKey)
                .build()
        }
        return runCatching {
            s3Client.deleteObject(deleteObjectRequest)
        }.onFailure {
            log.warn { "Failed to delete file $fileKey from the bucket ${s3Properties.bucketName}!" }
        }.getOrNull()?.deleteMarker() ?: false
    }
}
