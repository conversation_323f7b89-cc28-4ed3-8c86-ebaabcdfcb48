package nl.teqplay.portcallplus.service.common

data class PortOfAntwerpBrugesResponse(
    val vesselName: String? = null,
    val imoNumber: String? = null,
    val flag: String? = null,
    val countryNl: String? = null,
    val countryEn: String? = null,
    val vesselTypeFull: String? = null,
    val vesselType: String? = null,
    val loa: String? = null,
    val gbr: String? = null,
    val stayDetail: List<PortOfAntwerpBrugesResponseStay>? = emptyList(),
)

data class PortOfAntwerpBrugesResponseStay(
    val stayId: String? = null,
    val agentName: String? = null,
    val agentCode: String? = null,
    val berthArrival: String? = null,
    val eta: Long? = null,
    val ata: Long? = null,
    val berthDeparture: String? = null,
    val etd: Long? = null,
    val atd: Long? = null,
    val origin: String? = null,
    val destination: String? = null,
    val originFull: String? = null,
    val destinationFull: String? = null,
)
