package nl.teqplay.portcallplus.service.common

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.AreaBasedEvent
import nl.teqplay.aisengine.event.interfaces.PortcallArrivalEvent
import nl.teqplay.aisengine.event.interfaces.PortcallDepartureEvent
import nl.teqplay.aisengine.event.interfaces.PortcallPlusEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.GeneralShipIdentifier
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusAgentChangedEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusAtaEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusAtdEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusEtaEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusEtdEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusPortcallFinishEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusPortcallVisit
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusShipChangedEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusVisitsUpdateEvent
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallVisit
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.UpdateType
import nl.teqplay.portcallplus.service.external.getVisitType
import nl.teqplay.portcallplus.utils.unsortedEquals
import org.springframework.stereotype.Service
import java.time.Duration
import java.time.Instant
import java.util.Date
import java.util.UUID
import kotlin.reflect.KProperty1

/**
 * Service to create AisEngine [PortcallPlusEvent] which can be generated when certain changes happen with the portcall or the visit inside the portcall.
 */
@Service
class EventService(
    private val eventStreamService: EventStreamService,
) {
    private val log = KotlinLogging.logger {}

    /**
     * Create events needed for a new portcall and publish them if we generated any
     */
    fun onNewPortcall(newPortcall: Portcall) {
        log.debug { "Portcall is new, creating needed events" }
        val newPortcallEvents = createEventsForNewPortcall(newPortcall)
        log.debug {
            "Generating events ${newPortcallEvents.joinToString(", ") { it.getSubject() }} for changes" +
                " on create portcall ${newPortcall.portcallId}."
        }
        publishEventsWithCheck(newPortcall, newPortcallEvents)
    }

    private fun publishEventsWithCheck(
        portcall: Portcall,
        portCallEvents: List<PortcallPlusEvent>,
    ) {
        if (portCallEvents.isNotEmpty()) {
            log.info { "Checking ${portCallEvents.size} events for new portcall (id = ${portcall.portcallId})" }
            portCallEvents.forEach {
                if (portcall.isValidUpdateEvent(it)) {
                    log.info { "Publishing portcall ${it.getSubject()} event for ${portcall.portcallId}" }
                    eventStreamService.publish(it)
                } else {
                    log.info { "Dropping portcall ${it.getSubject()} event for ${portcall.portcallId} $portcall" }
                }
            }
        }
    }

    /**
     * Create events needed for a portcall update and publish them if we generated any
     */
    fun onUpdatedPortcall(
        currentPortcall: Portcall,
        updatedPortcall: Portcall,
    ) {
        log.debug { "Generating events for changes on portcall" }
        val portcallEvents = createEventsForUpdatePortcall(currentPortcall, updatedPortcall)
        log.debug {
            "Generating events ${portcallEvents.joinToString(",") { it.getSubject() }} for changes" +
                " on update portcall ${currentPortcall.portcallId}."
        }
        publishEventsWithCheck(updatedPortcall, portcallEvents)
    }

    /**
     * Create event for closed portcall and publish them if we generated any
     */
    fun onPortcallFinish(portcall: Portcall) {
        log.debug { "Portcall ${portcall.portcallId} is finished, creating portcall end event" }
        val portcallFinishEvent = portcall.generatePortcallFinishEvent()
        portcallFinishEvent?.let { eventStreamService.publish(it) }
    }

    private fun Portcall.generatePortcallFinishEvent(): PortcallPlusPortcallFinishEvent? {
        // A finished portcall should always have an endtime.
        val endTime = this.endTime ?: return null

        val portAreaIdentifier = AreaIdentifier(
            id = null,
            type = AreaIdentifier.AreaType.PORT,
            name = this.port,
            unlocode = this.port,
        )

        return PortcallPlusPortcallFinishEvent(
            _id = UUID.randomUUID().toString(),
            portcallId = this.portcallId,
            ship = GeneralShipIdentifier(mmsi = null, imo = this.imo.toInt()),
            port = portAreaIdentifier,
            actualTime = endTime.toInstant(),
            createdTime = Instant.now(),
            deleted = false,
            regenerated = false,
        )
    }

    /**
     * Create events needed for all changes happened inside the updated visit and publish them if we generated any
     */
    fun onVisitChanges(
        portcall: Portcall,
        berthId: String?,
        berth: String?,
        currentVisit: PortcallVisit,
        updatedVisit: PortcallVisit,
        newStartTimeType: UpdateType,
    ) {
        val eventsForUpdatedVisit = portcall.createEventsForVisitChanges(
            berthId = berthId,
            berth = berth,
            updatedVisit = updatedVisit,
            matchingVisit = currentVisit,
            newStartTimeType = newStartTimeType,
        )
        publishEventsWithCheck(portcall, eventsForUpdatedVisit)
    }

    /**
     * Create events needed for a new visit and publish them if we generated any
     */
    fun onNewVisit(
        portcall: Portcall,
        berthId: String?,
        berth: String?,
        newVisit: PortcallVisit,
        newStartTimeType: UpdateType,
    ) {
        val eventsForNewVisit = portcall.createEventsForNewVisit(
            berthId = berthId,
            berth = berth,
            newVisit = newVisit,
            newStartTimeType = newStartTimeType,
        )

        publishEventsWithCheck(portcall, eventsForNewVisit)
    }

    /**
     * Create an ETA port event for a new portcall.
     *
     * @return An [PortcallPlusEtaEvent] that is for a port and not a nomination
     */
    private fun Portcall.createEtaEventForNewPortcall(): PortcallPlusEtaEvent {
        val portAreaIdentifier = AreaIdentifier(
            id = null,
            type = AreaIdentifier.AreaType.PORT,
            name = this.port,
            unlocode = this.port,
        )

        val source = getSourceMapping(this.source)

        return PortcallPlusEtaEvent(
            _id = UUID.randomUUID().toString(),
            portcallId = portcallId,
            ship = GeneralShipIdentifier(mmsi = null, imo = this.imo.toInt()),
            area = portAreaIdentifier,
            port = portAreaIdentifier,
            source = source,
            berth = null,
            berthId = null,
            vesselAgent = this.vesselAgent,
            predictedTime = this.startTime.toInstant(),
            nomination = false,
        )
    }

    /**
     * Create an [PortcallPlusEtaEvent] or [PortcallPlusEtdEvent] based on the [UpdateType] provided in the [Portcall].
     *
     * The following types of ETA/ETD events are generated:
     * - Berth, on [UpdateType.BERTH] or [UpdateType.NOMINATION_TERMINAL]
     * - PilotBoardingPlace, on [UpdateType.ANCHORAGE] or [UpdateType.PILOTBOARDINGPLACE]
     * - Port, on [UpdateType.PORT]
     *
     * The nomination flag should be set to true when the update was either a nomination or an agent nomination, and the port area should be used.
     * This is because for nominations we don't get any information about anything else other than the port.
     *
     * @param predictedTime The time we received this prediction to be take place
     * @param berthId The unique berth id used when creating a berth ETA/ETD
     * @param berth The berth name used when creating a berth ETA/ETD
     * @param source The name of the source this data is originating from
     * @param createEtaEvent Indicate if we should create an ETA event. Otherwise, an ETD event will be created
     * @param selectedVisit The visit that is being created or updated which triggered the creation of this event
     * @param newStartTimeType The new type of the created or updated visit directly provided by the source
     * @return The created [PortcallPlusEtaEvent] or [PortcallPlusEtdEvent]
     */
    private fun Portcall.createPredictionEventForUpdateType(
        predictedTime: Instant,
        berthId: String?,
        berth: String?,
        source: String?,
        createEtaEvent: Boolean,
        selectedVisit: PortcallVisit,
        newStartTimeType: UpdateType?,
    ): PortcallPlusEvent {
        val portAreaIdentifier = AreaIdentifier(
            id = null,
            type = AreaIdentifier.AreaType.PORT,
            name = this.port,
            unlocode = this.port,
        )

        val visitType = selectedVisit.visitType ?: newStartTimeType

        // Determine what kind of area we need to create an event for and if said event will be a nomination
        val (areaIdentifier, isNomination) = when (visitType) {
            UpdateType.NOMINATION_TERMINAL,
            UpdateType.BERTH,
            -> {
                val berthAreaIdentifier = AreaIdentifier(
                    id = berthId,
                    type = AreaIdentifier.AreaType.BERTH,
                    name = berth,
                    unlocode = this.port,
                )

                berthAreaIdentifier to false
            }
            UpdateType.PILOTBOARDINGPLACE,
            UpdateType.ANCHORAGE,
            -> {
                val locationName = if (selectedVisit.visitType == UpdateType.PILOTBOARDINGPLACE || selectedVisit.visitType == UpdateType.ANCHORAGE) {
                    selectedVisit.berthName
                } else {
                    null
                }

                val pilotBoardingPlaceAreaIdentifier = AreaIdentifier(
                    id = null,
                    type = AreaIdentifier.AreaType.PILOT_BOARDING_PLACE,
                    name = locationName,
                    unlocode = this.port,
                )

                pilotBoardingPlaceAreaIdentifier to false
            }
            UpdateType.PORT -> {
                portAreaIdentifier to false
            }
            UpdateType.NOMINATION,
            UpdateType.NOMINATION_AGENT,
            -> {
                val nominationAreaIdentifier = if (berth != null || berthId != null) {
                    // Create a berth area identifier when we know any information about the berth
                    AreaIdentifier(
                        id = berthId,
                        type = AreaIdentifier.AreaType.BERTH,
                        name = berth,
                        unlocode = this.port,
                    )
                } else {
                    // We don't know the berth, so we can assume this nomination is about a port
                    portAreaIdentifier
                }

                nominationAreaIdentifier to true
            }
            // This should never happen but this can be triggered when we didn't specify what type of visit was provided
            UpdateType.END_OF_SEA, null -> {
                portAreaIdentifier to false
            }
        }

        if (createEtaEvent) {
            return PortcallPlusEtaEvent(
                _id = UUID.randomUUID().toString(),
                portcallId = portcallId,
                ship = GeneralShipIdentifier(mmsi = null, imo = this.imo.toInt()),
                area = areaIdentifier,
                port = portAreaIdentifier,
                source = source,
                berth = berth,
                berthId = berthId,
                vesselAgent = this.vesselAgent,
                predictedTime = predictedTime,
                nomination = isNomination,
            )
        } else {
            return PortcallPlusEtdEvent(
                _id = UUID.randomUUID().toString(),
                portcallId = portcallId,
                ship = GeneralShipIdentifier(mmsi = null, imo = this.imo.toInt()),
                area = areaIdentifier,
                port = portAreaIdentifier,
                source = source,
                berth = berth,
                berthId = berthId,
                vesselAgent = this.vesselAgent,
                predictedTime = predictedTime,
                nomination = isNomination,
            )
        }
    }

    private fun Portcall.createEventsForVisitChanges(
        berthId: String?,
        berth: String?,
        updatedVisit: PortcallVisit,
        matchingVisit: PortcallVisit,
        newStartTimeType: UpdateType,
    ): List<PortcallPlusEvent> {
        log.debug { "Creating events for visit changes" }
        val berthAreaIdentifier = AreaIdentifier(
            id = berthId,
            type = AreaIdentifier.AreaType.BERTH,
            name = berth,
            unlocode = this.port,
        )

        val portAreaIdentifier = AreaIdentifier(
            id = null,
            type = AreaIdentifier.AreaType.PORT,
            name = this.port,
            unlocode = this.port,
        )

        val source = getSourceMapping(this.source)

        val etaEvent = createEventIfTimeChanged(updatedVisit, matchingVisit, PortcallVisit::berthEta, true) { newTime ->
            createPredictionEventForUpdateType(
                newTime.toInstant(),
                berthId,
                berth,
                source,
                createEtaEvent = true,
                updatedVisit,
                newStartTimeType,
            )
        }

        val etdEvent = createEventIfTimeChanged(updatedVisit, matchingVisit, PortcallVisit::berthEtd, true) { newTime ->
            createPredictionEventForUpdateType(
                newTime.toInstant(),
                berthId,
                berth,
                source,
                createEtaEvent = false,
                updatedVisit,
                newStartTimeType,
            )
        }

        val ataEvent = createEventIfTimeChanged(updatedVisit, matchingVisit, PortcallVisit::berthAta, false) { newTime ->
            PortcallPlusAtaEvent(
                _id = UUID.randomUUID().toString(),
                portcallId = portcallId,
                ship = GeneralShipIdentifier(mmsi = null, imo = this.imo.toInt()),
                area = berthAreaIdentifier,
                port = portAreaIdentifier,
                actualTime = newTime.toInstant(),
            )
        }

        val atdEvent = createEventIfTimeChanged(updatedVisit, matchingVisit, PortcallVisit::berthAtd, false) { newTime ->
            PortcallPlusAtdEvent(
                _id = UUID.randomUUID().toString(),
                portcallId = portcallId,
                ship = GeneralShipIdentifier(mmsi = null, imo = this.imo.toInt()),
                area = berthAreaIdentifier,
                port = portAreaIdentifier,
                actualTime = newTime.toInstant(),
            )
        }

        return listOfNotNull(etaEvent, etdEvent, ataEvent, atdEvent)
    }

    private fun Portcall.createEventsForNewVisit(
        berthId: String?,
        berth: String?,
        newVisit: PortcallVisit,
        newStartTimeType: UpdateType?,
    ): List<PortcallPlusEvent> {
        log.debug { "Creating events for new visit" }
        val berthAreaIdentifier = AreaIdentifier(
            id = berthId,
            type = AreaIdentifier.AreaType.BERTH,
            name = berth,
            unlocode = this.port,
        )

        val portAreaIdentifier = AreaIdentifier(
            id = null,
            type = AreaIdentifier.AreaType.PORT,
            name = this.port,
            unlocode = this.port,
        )

        val source = getSourceMapping(this.source)

        // Create events for all eta/etd/ata/atd fields
        val etaEvent = createEventIfFieldNotNull(newVisit, PortcallVisit::berthEta, true) { newTime ->
            createPredictionEventForUpdateType(
                newTime.toInstant(),
                berthId,
                berth,
                source,
                createEtaEvent = true,
                newVisit,
                newStartTimeType,
            )
        }

        val etdEvent = createEventIfFieldNotNull(newVisit, PortcallVisit::berthEtd, true) { newTime ->
            createPredictionEventForUpdateType(
                newTime.toInstant(),
                berthId,
                berth,
                source,
                createEtaEvent = false,
                newVisit,
                newStartTimeType,
            )
        }

        val ataEvent = createEventIfFieldNotNull(newVisit, PortcallVisit::berthAta, false) { newTime ->
            PortcallPlusAtaEvent(
                _id = UUID.randomUUID().toString(),
                portcallId = portcallId,
                ship = GeneralShipIdentifier(mmsi = null, imo = this.imo.toInt()),
                area = berthAreaIdentifier,
                port = portAreaIdentifier,
                actualTime = newTime.toInstant(),
            )
        }

        val atdEvent = createEventIfFieldNotNull(newVisit, PortcallVisit::berthAtd, false) { newTime ->
            PortcallPlusAtdEvent(
                _id = UUID.randomUUID().toString(),
                portcallId = portcallId,
                ship = GeneralShipIdentifier(mmsi = null, imo = this.imo.toInt()),
                area = berthAreaIdentifier,
                port = portAreaIdentifier,
                actualTime = newTime.toInstant(),
            )
        }

        return listOfNotNull(etaEvent, etdEvent, ataEvent, atdEvent)
    }

    /**
     * Wrapper method that generates all events on existing portcall updating.
     * The analogous method for the events on update is [createEventsForNewPortcall].
     */
    fun createEventsForUpdatePortcall(
        currentPortcall: Portcall,
        updatedPortcall: Portcall,
    ): List<PortcallPlusEvent> {
        val imo = updatedPortcall.imo.toIntOrNull()
            // We can't create any events if the imo is not set correctly
            ?: return emptyList()

        // Todo: Check if we should compare the portcall start time to getOldestRefTime(), as in [createEventsForNewPortcall].
        val portcallId = updatedPortcall.portcallId
        val port = AreaIdentifier(
            id = null,
            type = AreaIdentifier.AreaType.PORT,
            name = updatedPortcall.port,
            unlocode = updatedPortcall.port,
        )

        val agentChangedEvent = createEventIfAgentChanged(
            currentAgent = currentPortcall.vesselAgent,
            updatedAgent = updatedPortcall.vesselAgent,
            portcallId = portcallId,
            port = port,
            imo = imo,
            updateTimestamp = updatedPortcall.updateTimestamp,
        )

        val imoChangedEvent = createEventIfImoChanged(
            currentImo = currentPortcall.imo.toIntOrNull(),
            updatedImo = imo,
            portcallId = portcallId,
            port = port,
        )

        val visitsUpdateEvent = createEventIfVisitsChanged(
            vesselAgent = updatedPortcall.vesselAgent,
            currentVisits = currentPortcall.visits,
            updatedVisits = updatedPortcall.visits,
            portcallId = portcallId,
            port = port,
            imo = imo,
        )

        val etaEvent = createEventIfStartTimeChanged(currentPortcall, updatedPortcall)

        return listOfNotNull<PortcallPlusEvent>(
            agentChangedEvent,
            imoChangedEvent,
            visitsUpdateEvent,
            etaEvent,
        )
    }

    fun createEventIfStartTimeChanged(
        currentPortcall: Portcall?,
        updatedPortcall: Portcall,
    ): PortcallPlusEtaEvent? {
        return if (currentPortcall?.startTime != updatedPortcall.startTime) {
            updatedPortcall.createEtaEventForNewPortcall()
        } else {
            null
        }
    }

    fun createEventIfAgentChanged(
        currentAgent: String?,
        updatedAgent: String?,
        portcallId: String,
        port: AreaIdentifier,
        imo: Int,
        updateTimestamp: Date,
    ): PortcallPlusAgentChangedEvent? {
        // We can only fire an agent changed event if an updated one is provided
        if (updatedAgent == null) {
            return null
        }

        if (currentAgent != updatedAgent) {
            return PortcallPlusAgentChangedEvent(
                _id = UUID.randomUUID().toString(),
                ship = GeneralShipIdentifier(mmsi = null, imo = imo),
                portcallId = portcallId,
                port = port,
                vesselAgent = updatedAgent,
                source = null,
                actualTime = updateTimestamp.toInstant(),
            )
        }
        return null
    }

    private fun createEventIfImoChanged(
        currentImo: Int?,
        updatedImo: Int,
        portcallId: String,
        port: AreaIdentifier,
    ): PortcallPlusShipChangedEvent? {
        // Agent only counts as a change when we have a value in the current and updated portcall
        if (currentImo == null) {
            return null
        }

        if (currentImo != updatedImo) {
            return PortcallPlusShipChangedEvent(
                _id = UUID.randomUUID().toString(),
                ship = GeneralShipIdentifier(mmsi = null, imo = currentImo),
                portcallId = portcallId,
                port = port,
                newShip = GeneralShipIdentifier(mmsi = null, imo = updatedImo),
            )
        }

        return null
    }

    internal fun createEventIfVisitsChanged(
        vesselAgent: String?,
        currentVisits: List<PortcallVisit>,
        updatedVisits: List<PortcallVisit>,
        portcallId: String,
        port: AreaIdentifier,
        imo: Int,
    ): PortcallPlusVisitsUpdateEvent? {
        // We have to check compare each visit individually between the 2 lists else we might miss a visit change and/or create a false positive event
        if (!currentVisits.unsortedEquals(updatedVisits)) {
            return PortcallPlusVisitsUpdateEvent(
                _id = UUID.randomUUID().toString(),
                ship = GeneralShipIdentifier(mmsi = null, imo = imo),
                portcallId = portcallId,
                port = port,
                visits = updatedVisits.map { it.toAisEnginePortcallVisit() },
                vesselAgent = vesselAgent,
            )
        }

        return null
    }

    private fun PortcallVisit.toAisEnginePortcallVisit(): PortcallPlusPortcallVisit {
        return PortcallPlusPortcallVisit(
            berthEta = this.berthEta?.toInstant(),
            berthEtd = this.berthEtd?.toInstant(),
            berthAta = this.berthAta?.toInstant(),
            berthAtd = this.berthAtd?.toInstant(),
            terminal = this.terminal,
            berthName = this.berthName,
            arrivalMovementId = this.arrivalMovementId,
            departureMovementId = this.departureMovementId,
        )
    }

    /**
     * The oldest reference time before which received eta/etds are not processed.
     */
    private fun getOldestRefTime(): Long = (Instant.now() - Duration.ofDays(2)).toEpochMilli()

    /**
     * Wrapper method that generates all events on new portcall creation.
     * The analogous method for the events on update is [createEventsForUpdatePortcall].
     */
    fun createEventsForNewPortcall(portcall: Portcall): List<PortcallPlusEvent> {
        // We can't create any events if the imo is not set correctly
        val imo = portcall.imo.toIntOrNull()
            ?: return emptyList()
        val oldestRefTime = getOldestRefTime()
        if (portcall.startTime.time < getOldestRefTime()) {
            log.info {
                "Ignoring portcall ${portcall.portcallId} for ${portcall.imo} because startTime: ${portcall.startTime} is older than refTime: $oldestRefTime"
            }
            return emptyList()
        }
        val port = AreaIdentifier(
            id = null,
            type = AreaIdentifier.AreaType.PORT,
            name = portcall.port,
            unlocode = portcall.port,
        )
        /* Using the method [createEventIfAgentChanged] only for centralizing the check logic and maintaining similar logic to [createEventsForUpdatePortcall].
        But it'd be only needed to use the constructor [PortcallPlusAgentChangedEvent]. */
        val agentSetEvent = createEventIfAgentChanged(
            currentAgent = null,
            updatedAgent = portcall.vesselAgent,
            portcallId = portcall.portcallId,
            port = port,
            imo = imo,
            updateTimestamp = portcall.updateTimestamp,
        )

        val etaEvent = createEventIfStartTimeChanged(null, portcall)

        val individualVisitEvents = portcall.visits.map { visit ->
            portcall.createEventsForNewVisit(visit.uniqueBerthId, visit.berthName, visit, visit.visitType)
        }.flatten()
        /* Using the method [createEventIfVisitsChanged] only for centralizing the check logic and maintaining similar logic to [createEventsForUpdatePortcall].
        But it'd be only needed to use the constructor [PortcallPlusVisitsUpdateEvent]. */
        val visitsUpdateEvent = createEventIfVisitsChanged(
            vesselAgent = portcall.vesselAgent,
            currentVisits = emptyList(),
            updatedVisits = portcall.visits,
            portcallId = portcall.portcallId,
            port = port,
            imo = imo,
        )

        return listOfNotNull(agentSetEvent, visitsUpdateEvent, etaEvent) + individualVisitEvents
    }

    private fun getSourceMapping(portcallPlusSource: ScheduledTaskType): String? =
        when (portcallPlusSource) {
            ScheduledTaskType.SIMPLY5_NOMINATION -> "Simply5 Nominations"
            ScheduledTaskType.VOPAK_NOMINATION -> "Vopak Nominations"
            ScheduledTaskType.PORTCALL_GENERATOR -> "Teqplay Nominations"
            ScheduledTaskType.HARBORLIGHTS -> "Houston PCS"
            ScheduledTaskType.TMA_NOMINATION -> "TMA Nominations"
            else -> null
        }

    private fun createEventIfTimeChanged(
        updatedVisit: PortcallVisit,
        matchingVisit: PortcallVisit,
        timeField: KProperty1<PortcallVisit, Date?>,
        checkRefTime: Boolean,
        onCreateEvent: (newTime: Date) -> PortcallPlusEvent,
    ): PortcallPlusEvent? {
        // Only create an event if we have a new time, the old time can be empty
        val newTime = timeField.get(updatedVisit)
            // TODO we should eventually add support for an ETA/ETD cancel event when updated to null?
            ?: return null
        val oldTime = timeField.get(matchingVisit)

        if (checkRefTime && newTime.time < getOldestRefTime()) {
            // Time is too old, skip
            return null
        }

        // Only create an event if the new time isn't the same as the old time
        return if (newTime != oldTime) {
            onCreateEvent(newTime)
        } else {
            null
        }
    }

    private fun createEventIfFieldNotNull(
        visit: PortcallVisit,
        timeField: KProperty1<PortcallVisit, Date?>,
        checkRefTime: Boolean,
        onCreateEvent: (newTime: Date) -> PortcallPlusEvent,
    ): PortcallPlusEvent? {
        // Only create an event if we have a non-nullable value in the provided timeField
        val timeValue = timeField.get(visit) ?: return null

        return if (checkRefTime) {
            // Check the ref time, only create an event if it is not too old
            if (timeValue.time >= getOldestRefTime()) {
                onCreateEvent(timeValue)
            } else {
                // Time is too old, skip
                null
            }
        } else {
            // We don't care how old the timestamp is, always create the event
            onCreateEvent(timeValue)
        }
    }
}

fun Portcall.isValidUpdateEvent(portcallPlusEvent: PortcallPlusEvent): Boolean {
    return when (this.source) {
        ScheduledTaskType.SG_MDH_VISIT_ARRIVAL_DECLARATION,
        ScheduledTaskType.SG_MDH_DUE_TO_ARRIVE,
        ScheduledTaskType.SG_MDH_DUE_TO_DEPART,
        ->
            (portcallPlusEvent !is PortcallArrivalEvent && portcallPlusEvent !is PortcallDepartureEvent) ||
                (portcallPlusEvent is AreaBasedEvent && getVisitType(portcallPlusEvent.area.name ?: "") == UpdateType.BERTH)
        else -> true
    }
}
