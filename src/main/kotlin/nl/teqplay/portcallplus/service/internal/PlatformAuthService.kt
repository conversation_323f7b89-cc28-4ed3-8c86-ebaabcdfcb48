package nl.teqplay.portcallplus.service.internal

import com.fasterxml.jackson.core.JsonParseException
import nl.teqplay.platform.model.auth.AuthToken
import nl.teqplay.portcallplus.config.CacheName
import nl.teqplay.portcallplus.model.data.User
import nl.teqplay.portcallplus.properties.AuthProperties
import nl.teqplay.portcallplus.properties.PlatformProperties
import nl.teqplay.portcallplus.utils.mapper
import nl.teqplay.skeleton.auth.credentials.AuthorizationConfigurer
import nl.teqplay.skeleton.auth.credentials.SWAGGER_PATHS
import nl.teqplay.skeleton.auth.credentials.authentication.Authenticator
import nl.teqplay.skeleton.auth.credentials.passwordAuth.PasswordCredentials
import nl.teqplay.skeleton.common.exception.InternalErrorException
import nl.teqplay.skeleton.platform.auth.PlatformRestTemplate
import org.springframework.cache.annotation.Cacheable
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpStatus
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.stereotype.Service
import org.springframework.web.client.HttpStatusCodeException
import org.springframework.web.client.RestTemplate

/**
 * Default authentication service overriding the plugin implementation to make sure it works without any local
 * management of users. The login user must exist in the platform as configured in [platform.url]
 */
@Service
class PlatformAuthService(
    @PlatformRestTemplate
    private val restTemplate: RestTemplate,
    private val platformProperties: PlatformProperties,
    private val userService: UserService,
) : Authenticator<PasswordCredentials> {
    /**
     * Throws an exception if the User is not authenticated. Else returns authenticated [User], although it is not
     * persisted in the db in this project
     */
    @Cacheable(CacheName.BASIC_AUTH, unless = "#result==null", key = "#credentials.hashCode()")
    override fun authenticate(credentials: PasswordCredentials): User? {
        val r = try {
            restTemplate.postForEntity(
                "${platformProperties.url}/auth/login",
                mapOf("username" to credentials.username, "password" to credentials.password),
                AuthToken::class.java,
            )
        } catch (e: HttpStatusCodeException) {
            if (e.statusCode == HttpStatus.UNAUTHORIZED || e.statusCode == HttpStatus.FORBIDDEN) {
                return null
            } else {
                try {
                    val response = mapper.readTree(e.responseBodyAsString)
                    val error = response["error"]?.textValue() ?: "unknown error"
                    throw InternalErrorException(
                        "Something went wrong authenticating user '${credentials.username}' " +
                            "(HTTP ${e.statusCode}, $error)",
                    )
                } catch (e: JsonParseException) {
                    // TODO: parsing with `readTree` went wrong probably because HTML/XML was sent
                    return null
                }
            }
        }
        // All non-200 status codes should result in an exception. But, just to be sure, check the status code anyway.
        if (r.statusCode != HttpStatus.OK) {
            val error = r.body?.toString() ?: "unknown error"
            // throw a more generic exception not explaining any of the error details to the outside world
            throw InternalErrorException(
                "Something went wrong authenticating user '${credentials.username}' " +
                    "(HTTP ${r.statusCode}, $error)",
            )
        }
        // Loading the user from the DB (instead of building an on-the-fly User) so the roles from the DB are loaded.
        return userService.get(credentials.username)
            ?: throw InternalErrorException("Something went wrong authenticating user '${credentials.username}'.")
    }
}

@Suppress("unused")
@Configuration
class CustomAuthConfigurator(
    paths: List<String>,
    private val authProperties: AuthProperties,
) : AuthorizationConfigurer(paths) {
    override fun configure(http: HttpSecurity): HttpSecurity {
        // make authentication configurable
        return if (authProperties.useAuthentication) {
            super.configure(http)
        } else {
            http.authorizeHttpRequests { it.anyRequest().permitAll() }
        }
    }

    /**
     * We override the default AuthorizationConfigurer implemented in the skeleton plugins, to allow authentication to
     * be configurable. So locally it could be run without any user setup
     */
    @Bean
    fun authorizationConfigurer(): CustomAuthConfigurator =
        CustomAuthConfigurator(
            SWAGGER_PATHS
                .plus("/v1/status")
                .plus("/v1/mturk/vesselAgent")
                .plus("/system/overview")
                .plus("/actuator/health"),
            authProperties,
        )
}
