package nl.teqplay.portcallplus.service.internal.nats

import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.properties.PortEventsProperties
import nl.teqplay.portcallplus.service.internal.CsiService
import nl.teqplay.portcallplus.utils.fetchTimestamp
import nl.teqplay.portcallplus.utils.log
import java.util.Date
import java.util.concurrent.TimeUnit
import java.util.logging.Level

abstract class AreaEventHandler(
    protected val csiService: CsiService,
    protected val portEventsProperties: PortEventsProperties,
    protected val allowedAreaType: AreaIdentifier.AreaType,
) {
    val portEventsPorts: List<String> = portEventsProperties.ports

    fun <T : Event> onMessage(event: T) {
        if (event is AreaEvent && event.area.type === allowedAreaType) {
            // when the unlocode is set, process the event
            event.area.unlocode?.let {
                // check if processing is enabled for this port
                if (portEventsPorts.contains(it)) {
                    processEvent(event, it)
                }
            }
        }
    }

    abstract fun processAta(
        imo: String,
        port: String,
        datetime: Long,
    ): Portcall?

    abstract fun processAtd(
        imo: String,
        port: String,
        datetime: Long,
        findFromDate: Date = fetchTimestamp(
            Date(),
            -30,
            TimeUnit.DAYS,
        ),
    ): Portcall?

    private fun processEvent(
        areaEvent: AreaEvent,
        port: String,
    ) {
        val ship = csiService.getShipByMmsi(areaEvent.ship.mmsi.toString())
        val imo = ship?.identifiers?.imo

        if (imo != null) {
            when (areaEvent) {
                is AreaStartEvent -> processAta(imo, port, areaEvent.actualTime.toEpochMilli())
                is AreaEndEvent -> processAtd(imo, port, areaEvent.actualTime.toEpochMilli())
                else -> log.info { "Unknown event type" }
            }
        } else {
            log(message = "Could not process portcall for mmsi ${areaEvent.ship.mmsi} due to not having an imo or the ship not existing", level = Level.FINE)
        }
    }
}
