package nl.teqplay.portcallplus.service.internal

import nl.teqplay.aisengine.shiphistory.client.ShipCurrentClient
import nl.teqplay.aisengine.shiphistory.model.AisCurrentMessage
import nl.teqplay.platform.model.Location
import nl.teqplay.platform.model.Portcall
import nl.teqplay.portcallplus.utils.log
import nl.teqplay.skeleton.platform.client.PortcallClient
import nl.teqplay.skeleton.util.haversineDistance
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Service
import org.springframework.web.client.RestClientResponseException
import java.util.logging.Level
import nl.teqplay.skeleton.model.Location as SkeletonLocation

private const val NM_TO_METERS = 1852.0

// ports for which portcall plus does not have all portcalls
val PLATFORM_PORTS =
    setOf(nl.teqplay.portcallplus.api.model.Portcall.IDPREFIX_NLAMS, nl.teqplay.portcallplus.api.model.Portcall.IDPREFIX_NLRTM)

/**
 * This component houses all calls done to the relevant configured backend platform
 */
@Service
class PlatformService(
    private val shipCurrentClient: ShipCurrentClient,
    @Lazy private val portcallClient: PortcallClient,
) {
    /**
     * Fetches all ships in the given range in region on Antwerp port (BEANR). E.g. (10,100) means a ship search
     * would be performed in the area of 10nm to 100nm from the [center]
     * @param outerRange Outer Search range in Nautical miles from the given [center]
     * @param innerRange Inner search range in Nautical miles from the given [center]
     */
    fun getAllShipsAroundLocation(
        center: Location,
        innerRange: Double,
        outerRange: Double,
    ): List<AisCurrentMessage> {
        val c = SkeletonLocation(lat = center.latitude, lon = center.longitude)
        // fetch all the ships in the outer boundary of the given search range
        val shipsAroundLocation = getAllShipsAroundLocation(c, outerRange).toMutableList()
        // fetch all the ships in the inner boundary of the given search range
        if (innerRange != 0.0) {
            val minDistance = nmToMeters(innerRange)
            shipsAroundLocation.removeIf {
                haversineDistance(c, it.location) < minDistance
            }
        }
        return shipsAroundLocation
    }

    /**
     * Fetches the current [Portcall] for the given [imo] and [port] by making a call to the Platform
     */
    fun getCurrentPortcallByImo(
        imo: String,
        port: String = "",
    ): Portcall? {
        return try {
            portcallClient.getPortcallByImo(imo, port)
        } catch (e: RestClientResponseException) {
            if (e.statusCode.value() == 404) {
                return null
            } else {
                throw e
            }
        }
    }

    /**
     * Fetches a [Portcall] for the given [imo] and [port] by making a call to the Platform
     */
    fun getPortcallsByImo(
        imo: String,
        port: String = "",
    ) = portcallClient.getAllPortcallsByImo(imo, port).sortedByDescending {
        it.startTime
    }

    fun getPortcallById(portcallId: String): Portcall? {
        return try {
            portcallClient.getPortcallById(portcallId)
        } catch (ex: Exception) {
            log(portcallId = portcallId, message = "Portcall not found. Message: ${ex.localizedMessage}", level = Level.WARNING)
            null
        }
    }

    fun getCurrentByMmsis(mmsis: Set<String>) = shipCurrentClient.findCurrentByMmsiList(mmsis.map(String::toInt))

    fun getCurrentByMmsi(mmsi: String): AisCurrentMessage? {
        try {
            return shipCurrentClient.findCurrentByMmsi(mmsi.toInt())
        } catch (ex: Exception) {
            log(message = "Error fetching current for mmsi $mmsi. Message: ${ex.localizedMessage}", level = Level.WARNING)
            return null
        }
    }

    private fun getAllShipsAroundLocation(
        center: SkeletonLocation,
        searchRadius: Double,
    ): List<AisCurrentMessage> {
        return shipCurrentClient.findCurrentInCircle(
            center,
            nmToMeters(searchRadius) / 1000.0,
        )
    }

    private fun nmToMeters(nauticalMiles: Double) = NM_TO_METERS * nauticalMiles
}
