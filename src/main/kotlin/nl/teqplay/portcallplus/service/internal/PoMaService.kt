package nl.teqplay.portcallplus.service.internal

import nl.teqplay.poma.api.v1.Anchorage
import nl.teqplay.poma.api.v1.ApiModel
import nl.teqplay.poma.api.v1.Berth
import nl.teqplay.poma.api.v1.CustomArea
import nl.teqplay.poma.api.v1.InfrastructureModelType
import nl.teqplay.poma.api.v1.Lock
import nl.teqplay.poma.api.v1.PilotBoardingPlace
import nl.teqplay.poma.api.v1.Port
import nl.teqplay.poma.api.v1.Terminal
import nl.teqplay.portcallplus.config.CacheName
import nl.teqplay.skeleton.poma.client.PomaInfrastructureClient
import org.springframework.cache.annotation.Cacheable
import org.springframework.stereotype.Service

@Service
class PoMaService(
    private val pomaClient: PomaInfrastructureClient,
) {
    @Cacheable(CacheName.BERTHS_WITH_PORT)
    fun getBerths(
        berthName: String?,
        port: String?,
    ): Array<Berth> {
        return pomaClient.getBerths(searchPattern = berthName, port = port)
    }

    fun getBerthsByIds(ids: Set<String>): Array<Berth> {
        return pomaClient.getBerthsByIds(ids)
    }

    fun getPorts(
        searchPattern: String? = null,
        topLeftLat: Double? = null,
        topLeftLon: Double? = null,
        bottomRightLat: Double? = null,
        bottomRightLon: Double? = null,
        withArea: Boolean? = null,
        countryCode: String? = null,
        limit: Int = 0,
        skip: Int = 0,
        validated: Boolean? = true,
    ): Array<Port> {
        return pomaClient.getPorts(
            searchPattern = searchPattern,
            topLeftLat = topLeftLat,
            topLeftLon = topLeftLon,
            bottomRightLat = bottomRightLat,
            bottomRightLon = bottomRightLon,
            withArea = withArea,
            countryCode = countryCode,
            limit = limit,
            skip = skip,
            validated = validated,
        )
    }

    fun getPortByUnlocode(unlocode: String): Port? {
        return pomaClient.getPortByUnlocode(unlocode)
    }

    fun getPortsByMainPort(unlocode: String): Array<Port> {
        return pomaClient.getPortsByMainPort(unlocode)
    }

    fun getPortsByUnlocode(unlocodes: List<String>): Array<Port> {
        return pomaClient.getPortsByUnlocode(unlocodes)
    }

    fun getPortUnlocodeList(countryCode: String?): Array<Port> {
        return pomaClient.getPortUnlocodeList(countryCode)
    }

    /**
     * Get the Port by the poma _id, do not confuse it with the unique id or unlocode
     */
    fun getPortById(id: String): Port? {
        return pomaClient.getPortById(id)
    }

    fun getPortsByIds(ids: Set<String>): Array<Port> {
        return pomaClient.getPortsByIds(ids)
    }

    fun getAnchorages(
        searchPattern: String? = null,
        topLeftLat: Double? = null,
        topLeftLon: Double? = null,
        bottomRightLat: Double? = null,
        bottomRightLon: Double? = null,
        withArea: Boolean? = null,
        port: String? = null,
        limit: Int = 0,
        skip: Int = 0,
    ): Array<Anchorage> {
        return pomaClient.getAnchorages(
            searchPattern = searchPattern,
            topLeftLat = topLeftLat,
            topLeftLon = topLeftLon,
            bottomRightLat = bottomRightLat,
            bottomRightLon = bottomRightLon,
            withArea = withArea,
            port = port,
            limit = limit,
            skip = skip,
        )
    }

    fun getAnchoragesByIds(ids: Set<String>): Array<Anchorage> {
        return pomaClient.getAnchoragesByIds(ids)
    }

    fun getLocks(
        searchPattern: String? = null,
        topLeftLat: Double? = null,
        topLeftLon: Double? = null,
        bottomRightLat: Double? = null,
        bottomRightLon: Double? = null,
        withArea: Boolean? = null,
        port: String? = null,
        limit: Int = 0,
        skip: Int = 0,
    ): Array<Lock> {
        return pomaClient.getLocks(
            searchPattern = searchPattern,
            topLeftLat = topLeftLat,
            topLeftLon = topLeftLon,
            bottomRightLat = bottomRightLat,
            bottomRightLon = bottomRightLon,
            withArea = withArea,
            port = port,
            limit = limit,
            skip = skip,
        )
    }

    fun getPilotBoardingPlaces(
        searchPattern: String? = null,
        topLeftLat: Double? = null,
        topLeftLon: Double? = null,
        bottomRightLat: Double? = null,
        bottomRightLon: Double? = null,
        withArea: Boolean? = null,
        port: String? = null,
        limit: Int = 0,
        skip: Int = 0,
    ): Array<PilotBoardingPlace> {
        return pomaClient.getPilotBoardingPlaces(
            searchPattern = searchPattern,
            topLeftLat = topLeftLat,
            topLeftLon = topLeftLon,
            bottomRightLat = bottomRightLat,
            bottomRightLon = bottomRightLon,
            withArea = withArea,
            port = port,
            limit = limit,
            skip = skip,
        )
    }

    fun getTerminals(
        searchPattern: String? = null,
        topLeftLat: Double? = null,
        topLeftLon: Double? = null,
        bottomRightLat: Double? = null,
        bottomRightLon: Double? = null,
        withArea: Boolean? = null,
        port: String? = null,
        limit: Int = 0,
        skip: Int = 0,
    ): Array<Terminal> {
        return pomaClient.getTerminals(
            searchPattern = searchPattern,
            topLeftLat = topLeftLat,
            topLeftLon = topLeftLon,
            bottomRightLat = bottomRightLat,
            bottomRightLon = bottomRightLon,
            withArea = withArea,
            port = port,
            limit = limit,
            skip = skip,
        )
    }

    fun getTerminalsByIds(ids: Set<String>): Array<Terminal> {
        return pomaClient.getTerminalsByIds(ids)
    }

    /**
     * Get all terminals related to the given port
     */
    fun getTerminalsByPortName(portUnlocode: String): Array<Terminal> {
        return pomaClient.getTerminalsByPortName(portUnlocode)
    }

    /**
     * It returns the list of Ports which have at least one berth mapped in the system.
     * In comparison to [getAll], [getBerthMappedPorts] will not include ports with no berths.
     */
    fun getBerthMappedPorts(validated: Boolean? = true): Array<Port> {
        return pomaClient.getBerthMappedPorts(validated)
    }

    fun getCustomArea(searchPattern: String? = null): Array<CustomArea> {
        return pomaClient.getCustomArea(searchPattern)
    }

    fun globalSearch(
        searchPattern: String?,
        scope: List<InfrastructureModelType>? = null,
        topLeftLat: Double? = null,
        topLeftLon: Double? = null,
        bottomRightLat: Double? = null,
        bottomRightLon: Double? = null,
        validated: Boolean? = null,
        limitByModelType: Int? = 0,
    ): Array<ApiModel> {
        return pomaClient.globalSearch(
            searchPattern = searchPattern,
            scope = scope,
            topLeftLat = topLeftLat,
            topLeftLon = topLeftLon,
            bottomRightLat = bottomRightLat,
            bottomRightLon = bottomRightLon,
            validated = validated,
            limitByModelType = limitByModelType,
        )
    }
}
