package nl.teqplay.portcallplus.service.internal

import nl.teqplay.portcallplus.config.health.HealthConfiguration
import nl.teqplay.portcallplus.service.external.CorpusChristiService
import nl.teqplay.portcallplus.service.external.MTurkService
import nl.teqplay.skeleton.uncaughtexceptionslackreport.UncaughtExceptionSlackReport
import nl.teqplay.skeleton.uncaughtexceptionslackreport.slack.model.SlackLevel
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

/**
 * The purpose of this service is to group all Spring @Scheduled methods.
 * The motivation is because the use of annotation @UncaughtExceptionSlackReport uses AOP, which have a small functional
 * incompatibility with services that extends from other abstract services.
 * If using the annotation in Foo: AbstractBar, and there's some logic in AbstractBar, then the Foo's Beans are null.
 * The issue was much related with the CorpusChristiService, that extends from abstract TaskService, that contains the
 * method startAllTasks. Due to this and AOP, the beans aren't initialized, making this service to throw a NPE when using
 * scheduledTaskRunDataSource.
 */
@Component
public class SpringScheduleService(
    private val corpusChristiService: CorpusChristiService,
    private val healthConfiguration: HealthConfiguration,
    private val mTurkService: MTurkService,
    private val portcallEndFallBack: PortcallEndFallback,
    private val statusSlackReportingService: StatusSlackReportingService,
) {

    @UncaughtExceptionSlackReport(tagMessage = "CorpusChristi movements files clean up", level = SlackLevel.WARNING)
    @Scheduled(
        cron = "\${spring-schedule.clean-corpus-christi-processed-files-cron}",
        zone = "\${spring-schedule.timezone}",
    )
    fun corpusChristiCleanUpS3Files() {
        corpusChristiService.cleanUpS3Files()
    }

    @UncaughtExceptionSlackReport(tagMessage = "Health status Slack reporting", level = SlackLevel.WARNING)
    @Scheduled(
        cron = "\${spring-schedule.health-status-check-cron}",
        zone = "\${spring-schedule.timezone}",
    )
    fun healthConfigurationHealthCheck() {
        healthConfiguration.runHealthCheck()
    }

    @UncaughtExceptionSlackReport(tagMessage = "MTurk HITs auto-approval", level = SlackLevel.WARNING)
    @Scheduled(
        cron = "\${spring-schedule.m-turk.approve-correct-answers-cron}",
        zone = "\${spring-schedule.timezone}",
    )
    fun scheduleFetchSubmittedAnswersOnApprovedHITS() {
        mTurkService.scheduleFetchSubmittedAnswersOnApprovedHITS()
    }

    @UncaughtExceptionSlackReport(tagMessage = "MTurk HITs processing", level = SlackLevel.WARNING)
    @Scheduled(
        cron = "\${spring-schedule.m-turk.hit-assignments-process-cron}",
        zone = "\${spring-schedule.timezone}",
    )
    fun scheduledProcessHits() {
        mTurkService.processHits()
    }

    @UncaughtExceptionSlackReport(tagMessage = "Automatic Portcall ender", level = SlackLevel.WARNING)
    @Scheduled(
        initialDelayString = "\${spring-schedule.portcall-ender.initial-delay-duration}",
        fixedDelayString = "\${spring-schedule.portcall-ender.fixed-delay-duration}",
    )
    fun portcallEndFallBack() {
        portcallEndFallBack.closeAutomaticallyPortcalls()
    }

    @UncaughtExceptionSlackReport(tagMessage = "Service Counter publisher", level = SlackLevel.WARNING)
    @Scheduled(
        cron = "\${spring-schedule.publish-counters-on-slack-cron}",
        zone = "\${spring-schedule.timezone}",
    )
    fun publishCountersOnSlack() {
        statusSlackReportingService.publishCountersOnSlack()
    }
}
