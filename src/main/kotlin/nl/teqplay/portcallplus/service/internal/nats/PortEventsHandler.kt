package nl.teqplay.portcallplus.service.internal.nats

import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallStatus
import nl.teqplay.portcallplus.api.model.UpdateType
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.properties.PortEventsProperties
import nl.teqplay.portcallplus.service.internal.CsiService
import nl.teqplay.portcallplus.utils.log
import org.springframework.stereotype.Component
import java.util.Date
import java.util.logging.Level

@Component
class PortEventsHandler(
    private val portcallDataSource: PortcallDataSource,
    csiService: CsiService,
    eventsProperties: PortEventsProperties,
) : AreaEventHandler(csiService, eventsProperties, AreaIdentifier.AreaType.PORT) {
    /**
     * Process the port Ata on a existing portcall
     */
    override fun processAta(
        imo: String,
        port: String,
        datetime: Long,
    ): Portcall? {
        val portAta = Date(datetime)
        val portcall = portcallDataSource.getNearestByImoAndDate(
            shipImo = imo,
            port = port,
            estimatedTime = portAta,
            finished = false,
        )
        return if (portcall != null) {
            log(portcall.portcallId, imo, port, "Found from portAta $portAta", Level.INFO)
            val updatedPortcall = if (portcall.portAtaTime == null) {
                portcall.copy(
                    status = PortcallStatus.INBOUND,
                    portAtaTime = portAta,
                    startTime = portAta,
                    startTimeType = UpdateType.PORT,
                )
            } else {
                portcall
            }
            portcallDataSource.createOrUpdate(updatedPortcall)
            updatedPortcall
        } else {
            log("No open portcall", imo, port, "Found from portAta $portAta", Level.INFO)
            null
        }
    }

    /**
     * Process the port Atd on a existing portcall. And set the status of that portcall
     */
    override fun processAtd(
        imo: String,
        port: String,
        datetime: Long,
        findFromDate: Date,
    ): Portcall? {
        val portAtd = Date(datetime)
        // fetch the portcall with the closest startTime to the given portAtd time
        val portcall = portcallDataSource.getNearestByImoAndDate(imo, port, portAtd, findFromDate, portAtd, false, false)
        return if (portcall != null) {
            log(portcall.portcallId, imo, port, "Found from portAtd $portAtd", Level.INFO)
            val updatedPortcall = portcall.copy(
                // only update the portAtd once.
                portAtdTime = portcall.portAtdTime ?: portAtd,
                status = PortcallStatus.OUTBOUND,
            )
            portcallDataSource.createOrUpdate(updatedPortcall)
            updatedPortcall
        } else {
            log("No open portcall", imo, port, "Found from portAtd $portAtd", Level.INFO)
            return null
        }
    }
}
