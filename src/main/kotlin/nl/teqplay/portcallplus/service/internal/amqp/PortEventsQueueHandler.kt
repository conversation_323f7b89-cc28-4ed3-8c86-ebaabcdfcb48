package nl.teqplay.portcallplus.service.internal.amqp

import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallStatus
import nl.teqplay.portcallplus.api.model.UpdateType
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.model.data.amqp.PortEvent
import nl.teqplay.portcallplus.properties.PortEventsProperties
import nl.teqplay.portcallplus.service.internal.CsiService
import nl.teqplay.portcallplus.utils.fetchTimestamp
import nl.teqplay.portcallplus.utils.log
import nl.teqplay.portcallplus.utils.mapper
import org.apache.commons.lang.exception.ExceptionUtils
import org.springframework.amqp.core.Message
import org.springframework.amqp.core.MessageListener
import org.springframework.stereotype.Service
import java.util.Date
import java.util.concurrent.TimeUnit
import java.util.logging.Level

val PORTATA_INTERVAL: Long = TimeUnit.DAYS.toMillis(2) // 2 days

/**
 * This class handles the PortEvents gotten from Amqp and applies the information to existing portcalls
 */
@Service
@Deprecated("Deprecated in favor of PortEventsHandler which reads the events from NATs")
class PortEventsQueueHandler(
    private val portcallDataSource: PortcallDataSource,
    private val csiService: CsiService,
    private val portEventsProperties: PortEventsProperties,
) : MessageListener {
    val portEventsPorts: List<String> = portEventsProperties.ports

    override fun onMessage(message: Message) {
        try {
            val portEvent = mapper.readValue<PortEvent>(message.body)
            var port: String? = null
            for (enabledPort in portEventsPorts) {
                if (portEvent.type.contains(enabledPort)) {
                    port = enabledPort
                    break
                }
            }
            if (port != null) {
                log(message = " [x] Received $portEvent")
                processPortEvent(portEvent, port)
            }
        } catch (ex: Exception) {
            log(message = "Rabbitmq exception. Reason: ${ExceptionUtils.getFullStackTrace(ex)}", level = Level.WARNING)
        }
    }

    /**
     * Process the port event
     */
    private fun processPortEvent(
        portEvent: PortEvent,
        port: String,
    ) {
        val imo = csiService.getShipByMmsi(portEvent.shipMmsi)?.identifiers?.imo

        if (imo != null) {
            val startEvent = portEvent.type.contains("start", true)
            if (startEvent) {
                processPortAta(imo, port, portEvent.datetime)
            } else {
                processPortAtd(imo, port, portEvent.datetime)
            }
        } else {
            log(message = "Could not process portcall for mmsi ${portEvent.shipMmsi} due to not having a imo or the ship not existing")
        }
    }

    /**
     * Process the port Ata on a existing portcall
     */
    fun processPortAta(
        imo: String,
        port: String,
        datetime: Long,
    ): Portcall? {
        val portAta = Date(datetime)
        val portcall = portcallDataSource.getNearestByImoAndDate(
            shipImo = imo,
            port = port,
            estimatedTime = portAta,
            finished = null,
        )
        return if (portcall != null) {
            log(portcall.portcallId, imo, port, "Found from portAta $portAta", Level.INFO)
            val updatedPortcall = if (portcall.portAtaTime == null) {
                portcall.copy(
                    status = PortcallStatus.INBOUND,
                    portAtaTime = portAta,
                    startTime = portAta,
                    startTimeType = UpdateType.PORT,
                )
            } else {
                portcall
            }
            portcallDataSource.createOrUpdate(updatedPortcall)
            updatedPortcall
        } else {
            log("No Portcall", imo, port, "Found from portAta $portAta", Level.INFO)
            null
        }
    }

    /**
     * Process the port Atd on a existing portcall. And set the status of that portcall
     */
    fun processPortAtd(
        imo: String,
        port: String,
        datetime: Long,
        findFromDate: Date = fetchTimestamp(Date(), -30, TimeUnit.DAYS),
    ): Portcall? {
        val portAtd = Date(datetime)
        // fetch the portcall with the closest startTime to the given portAtd time
        val portcall = portcallDataSource.getNearestByImoAndDate(imo, port, portAtd, findFromDate, portAtd, null)
        return if (portcall != null) {
            log(portcall.portcallId, imo, port, "Found from portAtd $portAtd", Level.INFO)
            val updatedPortcall = portcall.copy(
                // only update the portAtd once.
                portAtdTime = portcall.portAtdTime ?: portAtd,
                status = PortcallStatus.OUTBOUND,
                // always update the endTime with the latest portAtd
                endTime = portAtd,
            )
            portcallDataSource.createOrUpdate(updatedPortcall)
            updatedPortcall
        } else {
            log("No Portcall", imo, port, "Found from portAtd $portAtd", Level.INFO)
            return null
        }
    }
}
