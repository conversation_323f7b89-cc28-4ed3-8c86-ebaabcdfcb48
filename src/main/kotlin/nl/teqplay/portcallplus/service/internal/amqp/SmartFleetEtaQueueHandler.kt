package nl.teqplay.portcallplus.service.internal.amqp

import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallStatus
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.UpdateType
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.properties.SmartFleetProperties
import nl.teqplay.portcallplus.utils.fetchTimestamp
import nl.teqplay.portcallplus.utils.log
import nl.teqplay.portcallplus.utils.mapper
import nl.teqplay.smartfleet.model.publish.EtaEventPublishedMessage
import org.apache.commons.lang.exception.ExceptionUtils
import org.springframework.amqp.core.Message
import org.springframework.amqp.core.MessageListener
import org.springframework.stereotype.Service
import java.util.Date
import java.util.concurrent.TimeUnit
import java.util.logging.Level
import kotlin.math.abs

/**
 * This class handles the EtaEventPublishedMessage gotten from Smartfleet and applies the information to create
 * new portcalls based on the ETA event
 */
@Service
class SmartFleetEtaQueueHandler(
    private val portcallDataSource: PortcallDataSource,
    private val smartFleetProperties: SmartFleetProperties,
) : MessageListener {
    val PORTCALL_ETA_MIN_DIFFERENCE = 300000
    val portEventsPorts: List<String> = smartFleetProperties.eta.ports

    override fun onMessage(message: Message) {
        try {
            val etaEvent = mapper.readValue<EtaEventPublishedMessage>(message.body)
            var port: String? = null
            for (enabledPort in portEventsPorts) {
                if (etaEvent.unlocode.contains(enabledPort)) {
                    port = enabledPort
                    break
                }
            }
            if (port != null) {
                log(message = " [x] Received $etaEvent")
                processEtaEvent(etaEvent, port)
            }
        } catch (ex: Exception) {
            log(message = "Rabbitmq exception. Reason: ${ExceptionUtils.getFullStackTrace(ex)}", level = Level.WARNING)
        }
    }

    /**
     * Process the eta event event
     */
    private fun processEtaEvent(
        etaEvent: EtaEventPublishedMessage,
        port: String,
        findFromDate: Date = fetchTimestamp(Date(), -30, TimeUnit.DAYS),
    ) {
        val predictedStartTime = etaEvent.predicted?.eta?.let { Date(it) } ?: Date()
        if (predictedStartTime < findFromDate) {
            log(
                message = "Ignoring smartfleet Eta event for ship:${etaEvent.imo} from fleet: ${etaEvent.fleet}. ETA is to old: $predictedStartTime",
            )
            return
        }

        val portcall = portcallDataSource.getNearestByImoAndDate(
            etaEvent.imo,
            port,
            predictedStartTime,
            findFromDate,
            fetchTimestamp(predictedStartTime, 5, TimeUnit.DAYS),
        )

        // If no portcall exists, create new one
        if (portcall == null) {
            portcallDataSource.createOrUpdate(createNewPortcall(etaEvent.imo, port, predictedStartTime))
        } else {
            // If portcall still INBOUND and there is a difference
            if (portcall.status == PortcallStatus.INBOUND) {
                val difference = portcall.startTime.time - predictedStartTime.time
                if (portcall.startTime != predictedStartTime && abs(difference) > PORTCALL_ETA_MIN_DIFFERENCE) {
                    val updatedPortcall = portcall.copy(startTime = predictedStartTime)
                    portcallDataSource.createOrUpdate(updatedPortcall)
                }
            }
        }
    }

    /**
     * Create a portcall without visits with the data received
     */
    private fun createNewPortcall(
        imoNumber: String,
        port: String,
        eta: Date,
    ): Portcall {
        return Portcall(
            portcallDataSource.generateId(port, imoNumber, eta),
            setOf(),
            port,
            imoNumber,
            ScheduledTaskType.SMARTFLEET,
            eta,
            UpdateType.NOMINATION,
            PortcallStatus.INBOUND,
        )
    }
}
