package nl.teqplay.portcallplus.service.internal

import com.github.seratch.jslack.Slack
import com.github.seratch.jslack.api.model.block.DividerBlock
import com.github.seratch.jslack.api.model.block.LayoutBlock
import com.github.seratch.jslack.api.model.block.SectionBlock
import com.github.seratch.jslack.api.model.block.composition.MarkdownTextObject
import com.github.seratch.jslack.api.model.block.composition.PlainTextObject
import com.github.seratch.jslack.api.webhook.Payload
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.MTURK
import nl.teqplay.portcallplus.api.model.ScheduledTaskType.UNKNOWN
import nl.teqplay.portcallplus.datasource.ServiceFetchCounterDataSource
import nl.teqplay.portcallplus.model.httpResponse.ServiceOverallCounter
import nl.teqplay.portcallplus.properties.StatusProperties
import nl.teqplay.portcallplus.utils.fetchTimestamp
import nl.teqplay.portcallplus.utils.getStartOfTheDay
import nl.teqplay.portcallplus.utils.log
import org.springframework.stereotype.Service
import java.util.Date
import java.util.concurrent.TimeUnit
import java.util.logging.Level

/**
 * Service class housing all the logic and scheduling mechanism for updating [Portcall] based on NxtPort's VesselStay data
 */
@Service
class StatusSlackReportingService(
    private val serviceFetchCounterDataSource: ServiceFetchCounterDataSource,
    private val statusProperties: StatusProperties,
) {
    private var isEnabled: Boolean = statusProperties.reportOnSlack

    fun publishCountersOnSlack(): Boolean {
        if (isEnabled) {
            val url = "*****************************************************************************"
            val sections = mutableListOf<LayoutBlock>()
            sections.add(DividerBlock())
            sections.add(
                SectionBlock.builder().text(
                    PlainTextObject.builder().text(
                        "Yesterday's Portcall+ ${statusProperties.notificationTag} update",
                    ).build(),
                ).build(),
            )
            sections.add(DividerBlock())
            // insert header
            sections.add(
                SectionBlock.builder().fields(
                    listOf(
                        MarkdownTextObject("*Task Name*", false),
                        MarkdownTextObject("*# of Portcalls updated | Failed Requests | Total Requests*", false),
                    ),
                ).build(),
            )
            sections.add(DividerBlock())

            ScheduledTaskType.getAllTasks()
                .filter { it != UNKNOWN }
                .forEach { sections.add(createSectionBlock(it)) }
            sections.add(DividerBlock())
            val payload = Payload.builder().blocks(sections).build()
            val slack = Slack.getInstance()
            slack.send(url, payload)
        }
        return isEnabled
    }

    /**
     * Toggle for Enabling of disabling slack counters cron task
     */
    fun toggleEnable(): Boolean {
        isEnabled = !isEnabled
        return isEnabled
    }

    private fun createSectionBlock(taskType: ScheduledTaskType): SectionBlock {
        // set startTime to be yesterday 00:00:00, and endTime to be today 00:00:00 == 24 hours
        val currentTime = Date()
        val startDate = getStartOfTheDay(fetchTimestamp(currentTime, -1, TimeUnit.DAYS))
        val endDate = getStartOfTheDay(currentTime)

        return try {
            if (taskType == MTURK) {
                createMTurkSectionBlock(startDate, endDate)
            } else {
                val counter = fetchCounterForTaskType(taskType, startDate, endDate)
                SectionBlock.builder().fields(
                    listOf(
                        MarkdownTextObject("*${taskType.name}*", false),
                        MarkdownTextObject("${counter.portcallUpdates} | ${counter.failure} | ${counter.total}", false),
                    ),
                ).build()
            }
        } catch (ex: Exception) {
            ex.printStackTrace()
            val message = "Task: $taskType threw an exception: $ex"
            log(message = message, level = Level.SEVERE)
            SectionBlock.builder().fields(
                listOf(
                    MarkdownTextObject("*${taskType.name}*", false),
                    MarkdownTextObject(message, false),
                ),
            ).build()
        }
    }

    private fun fetchCounterForTaskType(
        taskType: ScheduledTaskType,
        startDate: Date,
        endDate: Date,
    ): ServiceOverallCounter {
        val counters = ServiceOverallCounter()
        val serviceCounters = serviceFetchCounterDataSource.getCounters(taskType = taskType, from = startDate, to = endDate)
        serviceCounters.forEach {
            counters.portcallUpdates.getAndAdd(it.getPortcallUpdateCount())
            counters.noPortcallUpdates.getAndAdd(it.getNoPortcallUpdateCount())
            counters.failure.getAndAdd(it.failedImoNumbers.size)
        }
        return counters
    }

    private fun createMTurkSectionBlock(
        startDate: Date,
        endDate: Date,
    ): SectionBlock {
        val serviceCounters =
            serviceFetchCounterDataSource.getCounters(taskType = MTURK, from = startDate, to = endDate)

        // Number of HITs created for portcall | Rejected submission | Total submission | Unmatched submission
        var hitCreated = 0
        var rejected = 0
        var wrong = 0
        var totalSubmissions = 0
        var unmatched = 0
        serviceCounters.forEach {
            hitCreated += it.create.size
            rejected += it.noUpdates.size
            wrong += it.emptyResult.size
            totalSubmissions += it.agentUpdate.size + it.noUpdates.size + it.failedImoNumbers.size
            unmatched += it.failedImoNumbers.size
        }
        return SectionBlock.builder().fields(
            listOf(
                MarkdownTextObject("*MTURK*", false),
                MarkdownTextObject(
                    "Created: $hitCreated | Submissions: $totalSubmissions | Wrong: $wrong | Rejected: $rejected | Unmatched: $unmatched",
                    false,
                ),
            ),
        ).build()
    }
}
