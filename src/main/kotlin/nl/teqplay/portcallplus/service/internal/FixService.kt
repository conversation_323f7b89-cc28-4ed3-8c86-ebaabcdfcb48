package nl.teqplay.portcallplus.service.internal

import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusAgentChangedEvent
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallAliasName
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.model.httpResponse.fixagent.Failed
import nl.teqplay.portcallplus.model.httpResponse.fixagent.PortcallAgentFixResult
import nl.teqplay.portcallplus.model.httpResponse.fixagent.Successful
import nl.teqplay.portcallplus.service.common.EventService
import nl.teqplay.portcallplus.service.external.nxtport.VesselStayServiceV2
import org.springframework.stereotype.Service
import java.util.Date

@Service
class FixService(
    private val portcallDataSource: PortcallDataSource,
    private val eventService: EventService,
    private val eventStreamService: EventStreamService,
    private val vesselStayServiceV2: VesselStayServiceV2,
) {
    fun createAndFireMissingAgentChangedEvents(portcallIds: List<String>): List<PortcallPlusAgentChangedEvent> {
        val publishedEvents = mutableListOf<PortcallPlusAgentChangedEvent>()
        portcallIds.map { portcallId ->
            val portcall = portcallDataSource.get(portcallId)

            if (portcall != null) {
                val agentChangedEvent = createMissingAgentChangedEventForPortcall(portcall)

                if (agentChangedEvent != null) {
                    publishedEvents.add(agentChangedEvent)
                    eventStreamService.publish(agentChangedEvent)
                }
            }
        }

        return publishedEvents
    }

    private fun createMissingAgentChangedEventForPortcall(portcall: Portcall): PortcallPlusAgentChangedEvent? {
        val imo = portcall.imo.toIntOrNull() ?: return null
        val port = AreaIdentifier(
            id = null,
            type = AreaIdentifier.AreaType.PORT,
            name = portcall.port,
            unlocode = portcall.port,
        )

        return eventService.createEventIfAgentChanged(
            currentAgent = null,
            updatedAgent = portcall.vesselAgent,
            portcallId = portcall.portcallId,
            port = port,
            imo = imo,
            updateTimestamp = portcall.updateTimestamp,
        )
    }

    /**
     * Method to update the agent of portcalls in the range [from]-[to], by scrapping the Port of Antwerp-Bruges site.
     * If [overrideAgent] is true, it'll set the agent a valid one was scraped.
     * If [triggerAgentChangeEvents] is true, it'll create and send the related PortcallPlusAgentChangedEvents.
     */
    fun setAgentsToNxtPortPortcallsByTimeRange(
        from: Date,
        to: Date,
        overrideAgent: Boolean,
        triggerAgentChangeEvents: Boolean,
    ): PortcallAgentFixResult {
        val portcallIds = portcallDataSource
            .getByStartTime(from, to, ScheduledTaskType.NXTPORT_V2)
            .map { it.portcallId }
        return this.setAgentsToNxtPortPortcalls(portcallIds, overrideAgent, triggerAgentChangeEvents)
    }

    /**
     * Method to update the agent for the given [portcallIds], by scrapping the Port of Antwerp-Bruges site.
     * If [overrideAgent] is true, it'll set the agent a valid one was scraped.
     * If [triggerAgentChangeEvents] is true, it'll create and send the related PortcallPlusAgentChangedEvents.
     */
    fun setAgentsToNxtPortPortcalls(
        portcallIds: List<String>,
        overrideAgent: Boolean,
        triggerAgentChangeEvents: Boolean,
    ): PortcallAgentFixResult {
        val failed = mutableListOf<Failed>()
        val updatedPortcallIdsAndAgents = portcallIds.mapNotNull { portcallId ->
            val portcall = portcallDataSource.get(portcallId)
            if (portcall == null) {
                failed.add(Failed(portcallId, "PortcallId not found."))
                return@mapNotNull null
            }
            if (portcall.source != ScheduledTaskType.NXTPORT_V2) {
                failed.add(Failed(portcallId, "Portcall source is not ${ScheduledTaskType.NXTPORT_V2}, but ${portcall.source}."))
                return@mapNotNull null
            }
            if (!portcall.vesselAgent.isNullOrBlank() && !overrideAgent) {
                failed.add(Failed(portcallId, "Portcall had set the agent '${portcall.vesselAgent}' and override is false."))
                return@mapNotNull null
            }
            // taking the last one, because it's always the most updated one!
            val lastAlias = portcall.portcallAlias.lastOrNull { it.source == PortcallAliasName.NXTPORT }?.alias
            if (lastAlias == null) {
                failed.add(Failed(portcallId, "Portcall had no NXTPORT alias, only ${portcall.portcallAlias}."))
                return@mapNotNull null
            }
            val scrapedAgent = vesselStayServiceV2.fetchVesselAgent(portcall.imo, lastAlias)
            if (scrapedAgent == null) {
                failed.add(Failed(portcallId, "No agent found for IMO ${portcall.imo} and alias $lastAlias."))
                return@mapNotNull null
            }
            val portcallToUpdate = portcall.copy(vesselAgent = scrapedAgent)
            runCatching {
                portcallDataSource.createOrUpdate(portcallToUpdate)
            }.onFailure {
                failed.add(Failed(portcallId, "Fail to update PortcallId $portcallId with agent $scrapedAgent"))
                return@mapNotNull null
            }
            Successful(portcallId, scrapedAgent)
        }
        val firedEvents = if (triggerAgentChangeEvents) {
            val updatedPortcallIds = updatedPortcallIdsAndAgents.map { (itPortcallId, _) -> itPortcallId }
            createAndFireMissingAgentChangedEvents(updatedPortcallIds)
        } else {
            emptyList()
        }
        return PortcallAgentFixResult(updatedPortcallIdsAndAgents, failed, firedEvents)
    }
}
