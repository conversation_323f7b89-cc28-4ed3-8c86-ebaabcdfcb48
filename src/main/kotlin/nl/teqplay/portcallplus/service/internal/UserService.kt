package nl.teqplay.portcallplus.service.internal

import nl.teqplay.portcallplus.datasource.UserDataSource
import nl.teqplay.portcallplus.model.data.User
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Service

@Service
class UserService(
    private val userDataSource: UserDataSource,
) {
    fun get(username: String) = userDataSource.find(username)

    fun getCurrentUser(): User? {
        val principal = SecurityContextHolder.getContext()?.authentication?.principal
        return if (principal is User) {
            principal
        } else {
            null
        }
    }

    fun getCurrentUsername() = getCurrentUser()?.username
}
