package nl.teqplay.portcallplus.service.internal

import nl.teqplay.aisengine.shiphistory.model.AisCurrentMessage
import nl.teqplay.csi.model.ship.info.ShipRegisterInfo
import nl.teqplay.csi.model.ship.info.component.ShipRole
import nl.teqplay.poma.api.v1.Location
import nl.teqplay.poma.api.v1.Port
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallStatus
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.properties.PortCallEndFallbackProperties
import nl.teqplay.portcallplus.service.common.EventService
import nl.teqplay.portcallplus.utils.ItemCache
import nl.teqplay.portcallplus.utils.log
import nl.teqplay.portcallplus.utils.toPlatformLocations
import nl.teqplay.portcallplus.utils.toSkeletonLocation
import nl.teqplay.portcallplus.utils.toSkeletonLocations
import nl.teqplay.skeleton.model.Polygon
import nl.teqplay.skeleton.poma.client.PomaInfrastructureClient
import nl.teqplay.skeleton.util.PolygonUtils
import nl.teqplay.skeleton.util.pointInPolygon
import org.springframework.stereotype.Component
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.Date
import java.util.concurrent.atomic.AtomicInteger
import java.util.logging.Level
import kotlin.math.ceil

@Component
class PortcallEndFallback(
    private val portcallDataSource: PortcallDataSource,
    private val eventService: EventService,
    private val csiService: CsiService,
    private val platformService: PlatformService,
    pomaClient: PomaInfrastructureClient,
    private val portCallEndFallbackProperties: PortCallEndFallbackProperties,
) {
    val fallbackRuntimeInterval = portCallEndFallbackProperties.runtimeHours

    val nextBatch = AtomicInteger(0)
    val maxBatches = AtomicInteger(0)
    val batchSize = AtomicInteger(0)
    val finishedCount = AtomicInteger(0)

    private val portCache = ItemCache(pomaClient::getPortByUnlocode, Duration.ofDays(7))

    fun closeAutomaticallyPortcalls() {
        // get the current batch amount and increment the Atomic int for the next run
        val currentBatch = nextBatch.getAndIncrement()
        if (currentBatch >= maxBatches.get()) {
            setupBatch()
        }
        log(message = "Starting batch ${nextBatch.get()} out of ${maxBatches.get()}. Batch contains ${batchSize.get()}")
        log(message = "skipping: ${nextBatch.get() * batchSize.get() - finishedCount.get()} with a limit of ${batchSize.get()}")
        val unfinishedPortcalls = portcallDataSource.getUnfinishedBatched(
            finished = finishedCount.get(),
            skipPages = nextBatch.get(),
            pageSize = batchSize.get(),
        )
        unfinishedPortcalls.forEach { if (shouldPortcallBeEnded(it)) it.finish() }
    }

    private fun setupBatch() {
        val unfinishedPortcallsCount = portcallDataSource.countUnfinished().toDouble()
        // Split in batches of one minute, keep 0.1 minute as a buffer to not take longer than 24 hours
        batchSize.set(ceil(unfinishedPortcallsCount / fallbackRuntimeInterval / (60 - 5)).toInt())
        nextBatch.set(0)
        maxBatches.set(ceil(unfinishedPortcallsCount / batchSize.get()).toInt())
        log(message = "Setting up batch processing, processing ${batchSize.get()} portcalls every minute")
    }

    internal fun shouldPortcallBeEnded(portcall: Portcall): Boolean {
        val shipInfo = csiService.getShipByImo(portcall.imo) ?: return false
        val mmsi = shipInfo.identifiers.mmsi ?: return false
        val currentStatus = platformService.getCurrentByMmsi(mmsi) ?: return false

        return isBunkerPortcallOpenedTooLong(shipInfo, portcall) ||
            isOutboundWithoutAtd(currentStatus, portcall) ||
            isInboundButOutdated(currentStatus, portcall) ||
            isAlongSideButShipOutsidePort(currentStatus, portcall)
    }

    private fun Portcall.finish(): Portcall {
        log(this.portcallId, this.imo, this.port, "Ending portcall with fallback", Level.INFO)
        // Portcall end time would normally be the eos atd, if this is not there a fallback will be used.
        // fallback order: EOS atd -> Port ATD -> Last detected event -> current time
        val endTime = this.eosAtdTime ?: this.portAtdTime ?: Date()

        val updatedPortcall = this.copy(
            endTime = endTime,
            eosAtdTime = endTime,
        )
        eventService.onPortcallFinish(updatedPortcall)
        finishedCount.incrementAndGet()
        return portcallDataSource.createOrUpdate(updatedPortcall)
    }

    /**
     * In some ports we get nominated for Bunker portcalls. If the bunker vessels then don't leave the port anymore we
     * have no way of closing the portcall as it is technically going on infinitely. With this we set a hard cap at 30 days
     */
    private fun isBunkerPortcallOpenedTooLong(
        shipInfo: ShipRegisterInfo,
        portcall: Portcall,
    ): Boolean {
        if (shipInfo.types.role == ShipRole.BUNKER) {
            if (portcall.startTime.time <= Instant.now().minus(30, ChronoUnit.DAYS).toEpochMilli()) {
                return true
            }
        }
        return false
    }

    /**
     * If no port atd is set but the portcall is Outbound. We check if the ship is outside the port.
     * If it is outside the port we finish the portcall.
     */
    private fun isOutboundWithoutAtd(
        status: AisCurrentMessage,
        portcall: Portcall,
    ): Boolean {
        if (portcall.eosAtdTime == null && portcall.status == PortcallStatus.OUTBOUND) {
            val port = portCache.get(portcall.port) ?: return false
            val areaPolygon = Polygon(port.getOutboundArea().map { it.toSkeletonLocation() })
            return !pointInPolygon(
                areaPolygon,
                status.location,
            )
        }
        return false
    }

    /**
     * If the portcall is in INBOUND state the ship should still be in the port, and it should have next visits
     * - If the ship is outside the port when the portcall started 2 weeks ago, we finish the portcall
     * - When the portcall started 2 weeks ago and there are no new open visits, we finish the portcall
     */
    private fun isInboundButOutdated(
        status: AisCurrentMessage,
        portcall: Portcall,
    ): Boolean {
        if (portcall.status == PortcallStatus.INBOUND) {
            if (portcall.startTime.time <= Instant.now().minus(7, ChronoUnit.DAYS).toEpochMilli()) {
                val port = portCache.get(portcall.port) ?: return false
                val area = port.getOutboundArea().toPlatformLocations().toTypedArray()
                val blowupArea = PolygonUtils.blowUpPolygon(area, 0.27).toList().toSkeletonLocations()
                return !pointInPolygon(
                    Polygon(blowupArea),
                    status.location,
                )
            }
        }
        return false
    }

    /**
     * If the portcall status is ALONGSIDE but the ship in no longer inside the port, we finish it
     */
    private fun isAlongSideButShipOutsidePort(
        status: AisCurrentMessage,
        portcall: Portcall,
    ): Boolean {
        if (portcall.status == PortcallStatus.ALONGSIDE) {
            val port = portCache.get(portcall.port) ?: return false
            val area = port.getOutboundArea().toPlatformLocations().toTypedArray()
            val blowupArea = PolygonUtils.blowUpPolygon(area, 0.27).toList().toSkeletonLocations()
            return !pointInPolygon(
                Polygon(blowupArea),
                status.location,
            )
        }
        return false
    }

    private fun Port.getOutboundArea(): List<Location> {
        return eosArea.nullWhenEmpty() ?: outerArea.nullWhenEmpty() ?: area
    }

    private fun List<Location>?.nullWhenEmpty(): List<Location>? {
        if (this.isNullOrEmpty()) return null
        return this
    }
}
