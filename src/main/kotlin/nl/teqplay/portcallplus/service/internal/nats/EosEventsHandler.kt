package nl.teqplay.portcallplus.service.internal.nats

import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallStatus
import nl.teqplay.portcallplus.api.model.UpdateType
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.portcallplus.properties.PortEventsProperties
import nl.teqplay.portcallplus.service.common.EventService
import nl.teqplay.portcallplus.service.internal.CsiService
import nl.teqplay.portcallplus.utils.log
import org.springframework.stereotype.Component
import java.util.Date
import java.util.logging.Level

@Component
class EosEventsHandler(
    private val portcallDataSource: PortcallDataSource,
    csiService: CsiService,
    private val eventService: EventService,
    eventsProperties: PortEventsProperties,
) : AreaEventHandler(csiService, eventsProperties, AreaIdentifier.AreaType.END_OF_SEA_PASSAGE) {
    /**
     * Process the port Ata on a existing portcall
     */
    override fun processAta(
        imo: String,
        port: String,
        datetime: Long,
    ): Portcall? {
        val eosAtaAta = Date(datetime)
        val portcall = portcallDataSource.getNearestByImoAndDate(
            shipImo = imo,
            port = port,
            estimatedTime = eosAtaAta,
            finished = false,
        )
        return if (portcall != null) {
            log(portcall.portcallId, imo, port, "Found from portAta $eosAtaAta", Level.INFO)
            val updatedPortcall = if (portcall.portAtaTime == null) {
                portcall.copy(
                    status = PortcallStatus.INBOUND,
                    eosAtaTime = eosAtaAta,
                    startTime = eosAtaAta,
                    startTimeType = UpdateType.END_OF_SEA,
                )
            } else {
                portcall
            }
            portcallDataSource.createOrUpdate(updatedPortcall)
            updatedPortcall
        } else {
            log("No open portcall", imo, port, "Found from eos $eosAtaAta", Level.INFO)
            null
        }
    }

    /**
     * Process the port Atd on a existing portcall. And set the status of that portcall.
     * Once a EOS is processed for a portcall that portcall is finished.
     */
    override fun processAtd(
        imo: String,
        port: String,
        datetime: Long,
        findFromDate: Date,
    ): Portcall? {
        val eosAtdTime = Date(datetime)
        // fetch the portcall with the closest startTime to the given portAtd time
        val portcall = portcallDataSource.getNearestByImoAndDate(imo, port, eosAtdTime, findFromDate, eosAtdTime, false, false)
        return if (portcall != null) {
            if (portcall.eosAtdTime != null) {
                log(portcall.portcallId, imo, port, "Found eosAtd event, portcall already closed. Ignoring message", Level.INFO)
                return null
            }

            if (portcall.portAtaTime == null) {
                log(portcall.portcallId, imo, port, "Found eosAtd event but no portAta, will not close portcall.", Level.INFO)
                return null
            }

            log(portcall.portcallId, imo, port, "Found from eosAtdTime $eosAtdTime", Level.INFO)
            val updatedPortcall = portcall.copy(
                // only update the portAtd once.
                eosAtdTime = portcall.eosAtdTime ?: eosAtdTime,
                status = PortcallStatus.OUTBOUND,
                // always update the endTime with the latest portAtd
                endTime = eosAtdTime,
            )
            portcallDataSource.createOrUpdate(updatedPortcall)
            eventService.onPortcallFinish(updatedPortcall)
            updatedPortcall
        } else {
            log("No open portcall", imo, port, "Found from eosAtdTime $eosAtdTime", Level.INFO)
            return null
        }
    }
}
