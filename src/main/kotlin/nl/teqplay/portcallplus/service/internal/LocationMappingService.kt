package nl.teqplay.portcallplus.service.internal

import nl.teqplay.poma.api.v1.Berth
import nl.teqplay.portcallplus.datasource.LocationMappingDataSource
import nl.teqplay.portcallplus.model.LocationMapping
import nl.teqplay.portcallplus.model.Unlocode
import nl.teqplay.portcallplus.utils.pointInArea
import nl.teqplay.portcallplus.utils.toSkeletonLocation
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.poma.client.PomaInfrastructureClient
import org.springframework.stereotype.Service
import kotlin.math.cos

@Service
class LocationMappingService(
    private val locationMappingDataSource: LocationMappingDataSource,
    private val pomaInfrastructureClient: PomaInfrastructureClient,
) {
    fun getOrCreateEmptyLocation(
        source: String,
        port: Unlocode,
        name: String,
    ) = getLocation(source, port, name)
        ?: locationMappingDataSource.createOrUpdate(LocationMapping(source, port, name, null))

    private fun getLocation(
        source: String,
        port: Unlocode,
        name: String,
    ) = locationMappingDataSource.getBySourcePortName(source, port, name)

    fun getPomaBerthForLocation(locationMapping: LocationMapping): Berth? {
        if (locationMapping.location == null) {
            return null
        }

        val square = locationMapping.location.toSquare(10)
        val matchingBerths = pomaInfrastructureClient.getBerths(
            port = locationMapping.port,
            topLeftLat = square.first.lat,
            topLeftLon = square.first.lon,
            bottomRightLat = square.second.lat,
            bottomRightLon = square.second.lon,
        )

        val filteredBerths = matchingBerths.filter { berth ->
            pointInArea(berth.area.map { it.toSkeletonLocation() }, locationMapping.location)
        }
        // If there are no berths found there is no reason to continue.
        if (filteredBerths.isEmpty()) {
            return null
        }

        val matchLongName = filteredBerths.find { it.nameLong == locationMapping.name }
        val matchName = filteredBerths.find { it.name == locationMapping.name }
        // return the best matching or first item
        return matchLongName ?: matchName ?: filteredBerths.first()
    }

    /**
     * Convert a location into a small square to check Poma against.
     */
    private fun Location.toSquare(squareSizeInMeters: Int): Pair<Location, Location> {
        val meters = squareSizeInMeters / 2
        return Pair(
            addMetersToLocation(-meters, this),
            addMetersToLocation(meters, this),
        )
    }

    private fun addMetersToLocation(
        meters: Int,
        location: Location,
    ): Location {
        /**
         *  number of km per degree = ~111km (111.32 in google maps, but range varies
         *  between 110.567km at the equator and 111.699km at the poles)
         *
         *  111.32km = 111320.0m (".0" is used to make sure the result of division is
         *  double even if the "meters" variable can't be explicitly declared as double)
         */
        val coef = meters / 111320.0
        val newLat: Double = location.lat + coef
        // pi / 180 ~= 0.01745
        val newLong: Double = location.lon + coef / cos(location.lat * 0.01745)

        return Location(
            lat = newLat,
            lon = newLong,
        )
    }
}
