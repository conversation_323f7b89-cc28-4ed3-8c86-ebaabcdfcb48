package nl.teqplay.portcallplus.service.internal

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import jakarta.annotation.PostConstruct
import nl.teqplay.portcallplus.datasource.PortcallDataSource
import nl.teqplay.skeleton.common.config.NatsProperties
import nl.teqplay.skeleton.nats.NatsClientBuilder
import org.springframework.stereotype.Component
import java.time.Duration
import java.time.Instant
import java.util.Date

@Component
class NatsRequestReplyService(
    private val natsClientBuilder: NatsClientBuilder,
    private val config: NatsProperties,
    private val objectMapper: ObjectMapper,
    private val platformService: PlatformService,
    private val portcallDataSource: PortcallDataSource,
) {
    /**
     * Request model for metadata, i.e. the portcall ID.
     */
    data class RequestMetadataByImoAndPort(
        val imo: String?,
        val port: String?,
        val time: Instant?,
        val from: Instant?,
        val to: Instant?,
        val allowFuturePortcalls: Boolean?,
    )

    /**
     * Exposed metadata based on the request.
     */
    data class Metadata(
        val portcallId: String?,
    )

    /**
     * Max days we go back in time to select a finish portcall
     */
    private val MAX_LAST_FINISHED_PORTCALL_DAYS = Duration.ofDays(30)

    /**
     * We look back at most 1 year to avoid assigning events to not closed old portcalls
     */
    private val MAX_LAST_ONGOING_PORTCALL_DAYS = Duration.ofDays(365)

    /**
     * Initializes a replier to a subject for portreporter-monitor to use.
     * A request by IMO and port can be done, which returns the ID of the nearest portcall.
     */
    @PostConstruct
    fun init() {
        val context = natsClientBuilder.requestReply(config, "portcallplus") ?: return
        context.reply(
            subject = "portreporter-monitor.request.v1.identifiers",
            deserializer = {
                runCatching { objectMapper.readValue<RequestMetadataByImoAndPort>(it) }
                    .getOrNull()
            },
            serializer = objectMapper::writeValueAsBytes,
            handler = { request, _ ->
                val imo = request.imo ?: return@reply Metadata(portcallId = null)
                val time = request.time ?: return@reply Metadata(portcallId = null)
                val portcallId = when (val port = request.port) {
                    // request from platform for NLRTM+NLAMS, since HAMIS/IRIS is running there
                    "NLRTM", "NLAMS" -> platformService.getCurrentPortcallByImo(imo, port)?.portcallId
                    // otherwise, always fallback to locally available portcalls
                    else -> findPortcall(imo, port, time, request)
                }
                return@reply Metadata(portcallId = portcallId)
            },
        )
    }

    internal fun findPortcall(
        imo: String,
        port: String?,
        time: Instant,
        request: RequestMetadataByImoAndPort,
    ): String? {
        val maxFrom = Date.from(time.minus(MAX_LAST_ONGOING_PORTCALL_DAYS))

        // Always check for active portcalls first that are not finished
        val activePortcall = portcallDataSource.getNearestByImoAndDate(
            shipImo = imo,
            port = port,
            estimatedTime = Date.from(time),
            intervalFrom = maxFrom,
            finished = false,
            allowFuturePortcalls = request.allowFuturePortcalls ?: true,
        )

        val actualActivePortcall = if (request.allowFuturePortcalls == false) {
            // When we don't allow future portcalls try to find them anyhow
            // As otherwise ATD events won't match when we only know visits of the current portcall in the future (e.g. berth nominations)
            val potentiallyFutureActivePortcall = portcallDataSource.getNearestByImoAndDate(
                shipImo = imo,
                port = port,
                estimatedTime = Date.from(time),
                intervalFrom = maxFrom,
                finished = false,
                allowFuturePortcalls = true,
            )

            // We have a port ata which means we should instead match the future portcall
            if (potentiallyFutureActivePortcall?.portAtaTime != null) {
                potentiallyFutureActivePortcall
            } else {
                activePortcall
            }
        } else {
            // The search already allowed future portcalls so no need to do an extra check
            activePortcall
        }

        return if (actualActivePortcall != null) {
            actualActivePortcall.portcallId
        } else {
            // Look back 30 days for finished portcalls when there is no active portcall
            val intervalFrom = Date.from(time.minus(MAX_LAST_FINISHED_PORTCALL_DAYS))
            val timeAsDate = Date.from(time)

            val fallbackFinishedPortcall = portcallDataSource.getNearestByImoAndDate(
                shipImo = imo,
                port = port,
                estimatedTime = timeAsDate,
                intervalFrom = intervalFrom,
                intervalTo = timeAsDate,
                finished = true,
                allowFuturePortcalls = request.allowFuturePortcalls ?: true,
            )

            fallbackFinishedPortcall?.portcallId
        }
    }
}
