package nl.teqplay.portcallplus.service.internal

import nl.teqplay.csi.api.client.ShipRegisterClient
import nl.teqplay.csi.model.ship.search.ShipFilter
import nl.teqplay.portcallplus.config.CacheName
import org.springframework.cache.annotation.Cacheable
import org.springframework.stereotype.Service
import org.springframework.web.client.HttpClientErrorException

@Service
class CsiService(
    private val shipRegisterClient: ShipRegisterClient,
) {
    @Cacheable(CacheName.SHIPS_BY_NAME)
    fun getShipsByName(shipName: String) =
        shipRegisterClient.search(
            ShipFilter(
                name = shipName,
                mmsi = null,
                eni = null,
                imo = null,
                shipType = null,
            ),
        )

    @Cacheable(CacheName.SHIP_BY_MMSI)
    fun getShipByMmsi(mmsi: String) =
        try {
            shipRegisterClient.getByMmsi(mmsi)
        } catch (e: HttpClientErrorException.NotFound) {
            null
        }

    @Cacheable(CacheName.SHIP_BY_IMO)
    fun getShipByImo(imo: String) =
        try {
            shipRegisterClient.getByImo(imo)
        } catch (e: HttpClientErrorException.NotFound) {
            null
        }

    fun getShipsByMmsis(mmsis: List<String>) = shipRegisterClient.getByMmsiList(mmsis)
}
