package nl.teqplay.portcallplus.config

import jakarta.annotation.PreDestroy
import nl.teqplay.portcallplus.properties.RabbitMqProperties
import nl.teqplay.portcallplus.service.internal.amqp.PortEventsQueueHandler
import org.springframework.amqp.rabbit.annotation.EnableRabbit
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory
import org.springframework.amqp.rabbit.listener.MessageListenerContainer
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.net.URI

/**
 * Register beans for connecting to the AMQP queue.
 * This queue is used to receive ATA/ATD events from the platform
 */
@Configuration
@EnableRabbit
@Deprecated("Deprecated in favor of PortEventsHandler which reads the events from NATs")
class PortEventsAmqpConfiguration {
    private val container by lazy { SimpleMessageListenerContainer() }

    /**
     * Instantiate a [MessageListenerContainer] connecting the [PortEventsQueueHandler] to the configured queue.
     *
     * @param config configuration values
     * @param portEventsQueueHandler the service that dispatches the messages to the correct beans
     * @return the listener container, used by Spring to process the messages
     */
    @ConditionalOnProperty(prefix = "rabbitmq", name = ["enabled"])
    @Bean
    fun messageListenerContainer(
        rabbitMqProperties: RabbitMqProperties,
        portEventsQueueHandler: PortEventsQueueHandler,
    ): MessageListenerContainer {
        container.connectionFactory = CachingConnectionFactory(URI(rabbitMqProperties.uri))
        container.addQueueNames(rabbitMqProperties.queuename)
        container.setMessageListener(portEventsQueueHandler)
        return container
    }

    @PreDestroy
    private fun shutdown() {
        container.shutdown()
    }
}
