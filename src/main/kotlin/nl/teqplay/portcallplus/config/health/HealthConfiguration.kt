package nl.teqplay.portcallplus.config.health

import com.github.seratch.jslack.Slack
import com.github.seratch.jslack.api.model.block.DividerBlock
import com.github.seratch.jslack.api.model.block.LayoutBlock
import com.github.seratch.jslack.api.model.block.SectionBlock
import com.github.seratch.jslack.api.model.block.composition.MarkdownTextObject
import com.github.seratch.jslack.api.model.block.composition.PlainTextObject
import com.github.seratch.jslack.api.webhook.Payload
import nl.teqplay.portcallplus.model.data.monitor.SubSystemStatus
import nl.teqplay.portcallplus.properties.SlackProperties
import nl.teqplay.portcallplus.service.common.TaskCounterService
import org.springframework.boot.actuate.health.HealthIndicator
import org.springframework.boot.actuate.health.Status
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.util.concurrent.ConcurrentHashMap

@Configuration
class HealthConfiguration(
    private val taskServices: Array<TaskCounterService>,
    private val slackProperties: SlackProperties,
) {
    private val environmentReporting = slackProperties.environment
    private val slackWebhook = slackProperties.webHook
    private val mapDownSubSystemStatus: ConcurrentHashMap<String, SubSystemStatus> = ConcurrentHashMap<String, SubSystemStatus>()

    /**
     * Portcallplus global health indicator, considering all services.
     */
    @Bean
    fun portcallPlusGlobalHealthIndicator(taskServices: Array<TaskCounterService>): HealthIndicator {
        return PortcallPlusServiceHealthIndicator(
            taskServices,
        )
    }

    fun runHealthCheck() {
        val subSystemStatus: List<SubSystemStatus> = taskServices.map { it.getSubSystemStati() }
            .flatten()
        if (subSystemStatus.isNotEmpty()) {
            val sections = mutableListOf<LayoutBlock>()

            val listSectionsAlreadyDown = mutableListOf<LayoutBlock>()
            val listSectionsToBeAdded = mutableListOf<LayoutBlock>()
            subSystemStatus.forEach { currentSubSystemStatus ->
                val previousSubSystemStatus = mapDownSubSystemStatus[currentSubSystemStatus.name]

                if (currentSubSystemStatus.status != Status.UP && previousSubSystemStatus == null) {
                    // Subsystem gone unhealthy
                    listSectionsToBeAdded.add(
                        SectionBlock.builder().fields(
                            listOf(
                                MarkdownTextObject("*${currentSubSystemStatus.name}*", false),
                                MarkdownTextObject("*UP -> ${currentSubSystemStatus.status}*", false),
                            ),
                        ).build(),
                    )
                    mapDownSubSystemStatus[currentSubSystemStatus.name] = currentSubSystemStatus
                } else if (previousSubSystemStatus != null && currentSubSystemStatus.status == Status.UP) {
                    // System has recovered
                    listSectionsToBeAdded.add(
                        SectionBlock.builder().fields(
                            listOf(
                                MarkdownTextObject("*${currentSubSystemStatus.name}*", false),
                                MarkdownTextObject("*${previousSubSystemStatus.status} -> ${currentSubSystemStatus.status}*", false),
                            ),
                        ).build(),
                    )
                    mapDownSubSystemStatus.remove(currentSubSystemStatus.name)
                }
            }

            if (listSectionsToBeAdded.isNotEmpty()) {
                sections.add(
                    SectionBlock.builder().text(
                        PlainTextObject.builder().text(
                            "$environmentReporting Portcall+ adapter status",
                        ).build(),
                    ).build(),
                )
                sections.add(DividerBlock())
                sections.add(
                    SectionBlock.builder().fields(
                        listOf(
                            MarkdownTextObject("*Task Name*", false),
                            MarkdownTextObject("*Adapter status*", false),
                        ),
                    ).build(),
                )
                sections.add(DividerBlock())
                if (listSectionsAlreadyDown.isNotEmpty()) {
                    sections.addAll(listSectionsAlreadyDown)
                }
                sections.addAll(listSectionsToBeAdded)
                sections.add(DividerBlock())
            }

            if (listSectionsToBeAdded.isNotEmpty()) {
                val payload = Payload.builder().blocks(sections).build()
                val slack = Slack.getInstance()
                slack.send(slackWebhook, payload)
            }
        }
    }
}
