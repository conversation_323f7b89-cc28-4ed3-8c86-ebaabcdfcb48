package nl.teqplay.portcallplus.config.health

import nl.teqplay.portcallplus.model.data.monitor.SubSystemStatus
import nl.teqplay.portcallplus.service.common.TaskCounterService
import nl.teqplay.skeleton.actuator.DEGRADED
import nl.teqplay.skeleton.actuator.degraded
import org.springframework.boot.actuate.health.AbstractHealthIndicator
import org.springframework.boot.actuate.health.Health
import org.springframework.boot.actuate.health.Status

/**
 * We need the to have PortcallPlusServiceHealthIndicator to be able to invoke the method [serviceHealth].[doHealthCheck],
 * which is protected in AbstractHealthIndicator, forcing us to extend from it to be able to access it
 */
open class PortcallPlusServiceHealthIndicator(
    private val taskServices: Array<TaskCounterService>,
) : AbstractHealthIndicator() {
    private val subSystemStatiKey = "subSystemStati"
    private val degradedKey = "degradedSubSystems"
    private val downKey = "downSubSystems"
    private val unknownKey = "unknownStatusSubsystems"
    val serviceNameKey = "serviceName"
    val degradedNames = "degradedSubSystemNames"
    val downNames = "downSubSystemNames"
    val unknownNames = "unknownStatusSubsystemNames"

    override fun doHealthCheck(builder: Health.Builder?) {
        val subSystemStati: List<SubSystemStatus> = taskServices.map { it.getSubSystemStati() }.flatten()

        builder?.withDetail(serviceNameKey, "PortcallPlus global health indicator")
        builder?.let { itBuilder ->
            itBuilder.withDetail(subSystemStatiKey, subSystemStati)

            if (subSystemStati.all { it.status == Status.UP }) {
                itBuilder.up()
            }
            // adding all unknownStatus subsystems
            subSystemStati.filter { it.status == Status.UNKNOWN }.takeUnless { it.isEmpty() }?.let {
                itBuilder.withDetail(unknownKey, it)
                itBuilder.withDetail(unknownNames, it.map { it.name })
                itBuilder.unknown()
            }
            // adding all degraded subsystems
            subSystemStati.filter { it.status == Status(DEGRADED) }.takeUnless { it.isEmpty() }?.let {
                itBuilder.withDetail(degradedKey, it)
                itBuilder.withDetail(degradedNames, it.map { it.name })
                itBuilder.degraded()
            }
            // adding all down subsystems
            subSystemStati.filter { it.status == Status.DOWN }.takeUnless { it.isEmpty() }?.let {
                itBuilder.withDetail(downKey, it)
                itBuilder.withDetail(downNames, it.map { it.name })
                itBuilder.down()
            }
        }
    }
}
