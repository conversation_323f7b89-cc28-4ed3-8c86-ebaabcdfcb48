package nl.teqplay.portcallplus.config

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.MapperFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.databind.json.JsonMapper
import com.fasterxml.jackson.databind.module.SimpleModule
import com.fasterxml.jackson.datatype.joda.JodaModule
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.KotlinModule
import de.undercouch.bson4jackson.BsonGenerator
import jakarta.annotation.PostConstruct
import nl.teqplay.portcallplus.utils.DateSerializer
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import java.util.Date

@Configuration
class ObjectMapperConfiguration {
    @PostConstruct
    fun configureMongo() {
        BsonGenerator.Feature.WRITE_BIGDECIMALS_AS_DECIMAL128
    }

    @Bean
    @Primary
    fun objectMapper(): ObjectMapper {
        val dateModule = SimpleModule("dateModule")
        dateModule.addSerializer(Date::class.java, DateSerializer())

        return JsonMapper.builder()
            .addModule(JodaModule())
            .addModule(JavaTimeModule())
            .addModule(dateModule)
            .addModule(KotlinModule.Builder().build())
            .enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES)
            .enable(MapperFeature.USE_BASE_TYPE_AS_DEFAULT_IMPL)
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .disable(SerializationFeature.FAIL_ON_EMPTY_BEANS)
            .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
            .serializationInclusion(JsonInclude.Include.NON_NULL)
            .build()
    }
}
