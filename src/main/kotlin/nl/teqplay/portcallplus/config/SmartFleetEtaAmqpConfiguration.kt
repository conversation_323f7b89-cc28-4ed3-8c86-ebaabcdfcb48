package nl.teqplay.portcallplus.config

import jakarta.annotation.PreDestroy
import nl.teqplay.portcallplus.properties.SmartFleetProperties
import nl.teqplay.portcallplus.service.internal.amqp.SmartFleetEtaQueueHandler
import org.springframework.amqp.rabbit.annotation.EnableRabbit
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory
import org.springframework.amqp.rabbit.listener.MessageListenerContainer
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.net.URI

/**
 * Register beans for connecting to the AMQP queue.
 * This queue is used to receive ETA events from Smartfleet
 */
@Configuration
@EnableRabbit
class SmartFleetEtaAmqpConfiguration {
    private val container by lazy { SimpleMessageListenerContainer() }

    /**
     * Instantiate a [MessageListenerContainer] connecting the [SmartFleetEtaQueueHandler] to the configured queue.
     *
     * @param config configuration values
     * @param portEventsQueueHandler the service that dispatches the messages to the correct beans
     * @return the listener container, used by Spring to process the messages
     */
    @ConditionalOnProperty(prefix = "smartfleet", name = ["enabled"])
    @Bean("SmartFleetMessageListener")
    fun messageListenerContainer(
        smartFleetProperties: SmartFleetProperties,
        smartFleetEtaQueueHandler: SmartFleetEtaQueueHandler,
    ): MessageListenerContainer {
        container.connectionFactory = CachingConnectionFactory(URI(smartFleetProperties.eta.uri))
        container.addQueueNames(smartFleetProperties.eta.queuename)
        container.setMessageListener(smartFleetEtaQueueHandler)
        return container
    }

    @PreDestroy
    private fun shutdown() {
        container.shutdown()
    }
}
