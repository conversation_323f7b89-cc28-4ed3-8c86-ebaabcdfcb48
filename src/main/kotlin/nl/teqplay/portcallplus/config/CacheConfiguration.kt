package nl.teqplay.portcallplus.config

import com.github.benmanes.caffeine.cache.Caffeine
import com.github.benmanes.caffeine.cache.Ticker
import nl.teqplay.portcallplus.properties.CacheTtlProperties
import nl.teqplay.portcallplus.properties.PomaProperties
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.cache.CacheManager
import org.springframework.cache.annotation.EnableCaching
import org.springframework.cache.caffeine.CaffeineCache
import org.springframework.cache.support.SimpleCacheManager
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.Duration

object CacheName {
    const val BASIC_AUTH = "platform-basic-auth"
    const val NXTPORT_API = "nxtport-vesselstay-api"
    const val SHIPS_BY_NAME = "ships-by-name"
    const val SHIP_BY_MMSI = "ship-by-mmsi"
    const val SHIP_BY_IMO = "ship-by-imo"
    const val BERTHS_WITH_PORT = "berths-with-port"
}

@EnableCaching
@Configuration
class CacheConfiguration {
    @Autowired
    private lateinit var cacheTtlProperties: CacheTtlProperties

    @Autowired
    private lateinit var pomaProperties: PomaProperties

    @Bean
    fun cacheManager(ticker: Ticker): CacheManager {
        val manager = SimpleCacheManager()
        manager.setCaches(
            listOf(
                buildCache(CacheName.BASIC_AUTH, ticker, cacheTtlProperties.platformBasicAuth),
                buildCache(CacheName.NXTPORT_API, ticker, cacheTtlProperties.nxtportVesselstayApi),
                buildCache(CacheName.SHIPS_BY_NAME, ticker, cacheTtlProperties.shipFetchApi),
                buildCache(CacheName.SHIP_BY_MMSI, ticker, cacheTtlProperties.shipFetchApi),
                buildCache(CacheName.SHIP_BY_IMO, ticker, cacheTtlProperties.shipFetchApi),
                buildCache(CacheName.BERTHS_WITH_PORT, ticker, pomaProperties.berthsWithPort),
            ),
        )
        return manager
    }

    companion object {
        /**
         * Returns all cached values in the given [cacheName]
         */
        fun getCache(
            cacheManager: CacheManager,
            cacheName: String,
            cacheKey: Any? = null,
        ): Map<Any, Any>? {
            val cache = (cacheManager.getCache(cacheName) as? CaffeineCache)?.nativeCache
            return if (cacheKey != null) {
                cache?.getIfPresent(cacheKey)?.let {
                    mapOf(cacheKey to it)
                }
            } else {
                cache?.asMap()
            }
        }

        /**
         * Evicts all cached values for the given [cacheName]
         */
        fun evictCache(
            cacheManager: CacheManager,
            cacheName: String,
        ): Map<Any, Any>? {
            getCache(cacheManager, cacheName)?.forEach {
                cacheManager.getCache(cacheName)?.evict(it.key)
            }
            return getCache(cacheManager, cacheName)
        }
    }

    private fun buildCache(
        name: String,
        ticker: Ticker,
        duration: Duration,
    ): CaffeineCache {
        return CaffeineCache(
            name,
            Caffeine.newBuilder()
                .expireAfterWrite(duration)
                .expireAfterAccess(duration)
                .maximumSize(cacheTtlProperties.maxSize)
                .ticker(ticker)
                .build(),
        )
    }

    @Bean
    fun ticker(): Ticker {
        return Ticker.systemTicker()
    }
}
