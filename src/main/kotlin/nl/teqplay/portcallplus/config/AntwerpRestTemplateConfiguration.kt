package nl.teqplay.portcallplus.config

import nl.teqplay.portcallplus.config.annotations.ANTWERP_REST_TEMPLATE
import nl.teqplay.portcallplus.properties.NxtPortV2Properties
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.client.RestTemplate

@Configuration
class AntwerpRestTemplateConfiguration(
    private val config: NxtPortV2Properties,
) {
    private val baseUrl = config.portOfAntwerp.baseUrl
    private val timeout = config.portOfAntwerp.timeout

    @Bean(ANTWERP_REST_TEMPLATE)
    fun getAntwerpRestTemplate(restTemplateBuilder: RestTemplateBuilder): RestTemplate {
        return restTemplateBuilder
            .rootUri(baseUrl)
            .connectTimeout(timeout)
            .build()
    }
}
