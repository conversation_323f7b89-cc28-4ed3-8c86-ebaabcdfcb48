package nl.teqplay.portcallplus.config

import nl.teqplay.portcallplus.config.annotations.BeanNames
import nl.teqplay.portcallplus.model.config.S3Properties
import nl.teqplay.portcallplus.properties.CorpusChristiProperties
import nl.teqplay.portcallplus.service.common.S3BucketClient
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class S3Configuration(
    private val corpusChristiProperties: CorpusChristiProperties,
) {
    private val corpusChristiS3Properties = with(corpusChristiProperties.s3) {
        S3Properties(
            region = this.region,
            bucketName = this.bucketName,
            accesskey = this.accesskey,
            secret = this.secret,
        )
    }

    @Bean(BeanNames.CORPUS_CHRISTI_S3_BUCKET_CLIENT)
    fun corpusChristiS3Client() = S3BucketClient(corpusChristiS3Properties)

    /*
    @Bean(BeanNames.Another_S3_Client_BeanName)
    fun AnyFutureS3Client() = S3BucketClient(anotherS3PropertiesObject)
     */
}
