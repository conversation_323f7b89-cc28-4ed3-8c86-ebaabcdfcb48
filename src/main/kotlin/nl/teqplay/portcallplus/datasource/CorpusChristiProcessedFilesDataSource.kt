package nl.teqplay.portcallplus.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.portcallplus.model.data.corpuschristi.FileParsingSummary
import org.springframework.stereotype.Repository

@Repository
class CorpusChristiProcessedFilesDataSource(database: MongoDatabase) : DataSource<FileParsingSummary>(database) {
    final val collectionName = "corpusChristiFileParsing"
    override val collection by lazy {
        fetchCollection(collectionName, FileParsingSummary::class.java)
    }
}
