package nl.teqplay.portcallplus.datasource

import com.mongodb.kotlin.client.FindIterable
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.model.data.ServiceFetchCounter
import nl.teqplay.portcallplus.model.data.ServiceFetchDetail
import nl.teqplay.portcallplus.utils.getStartOfTheDay
import nl.teqplay.skeleton.datasource.kmongo.and
import nl.teqplay.skeleton.datasource.kmongo.descending
import nl.teqplay.skeleton.datasource.kmongo.div
import nl.teqplay.skeleton.datasource.kmongo.ensureIndex
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.gte
import nl.teqplay.skeleton.datasource.kmongo.`in`
import nl.teqplay.skeleton.datasource.kmongo.lte
import nl.teqplay.skeleton.datasource.kmongo.or
import org.bson.conversions.Bson
import org.springframework.stereotype.Repository
import java.util.Date

/**
 * Counter to keep track of all requests and eventTime changes made corresponding to a [ScheduledTaskType]
 */
@Repository
class ServiceFetchCounterDataSource(database: MongoDatabase) : DataSource<ServiceFetchCounter>(database) {
    override val collection by lazy {
        fetchCollection("serviceFetchCounter", ServiceFetchCounter::class.java).apply {
            // add an index on the taskType
            ensureIndex(descending(ServiceFetchCounter::taskType))
            ensureIndex(descending(ServiceFetchCounter::_id, ServiceFetchCounter::taskType))
        }
    }

    fun getCounters(
        portcallIds: Collection<String> = listOf(),
        taskType: ScheduledTaskType?,
        from: Date = getStartOfTheDay(Date()),
        to: Date = Date(),
    ): FindIterable<ServiceFetchCounter> {
        val filters = mutableListOf(ServiceFetchCounter::_id gte from, ServiceFetchCounter::_id lte to)
        // add task type and token filter
        taskType?.let { filters.add(ServiceFetchCounter::taskType eq it) }
        if (portcallIds.isNotEmpty()) {
            val portcallIdFilter = mutableListOf<Bson>()
            portcallIdFilter.add(ServiceFetchCounter::create / ServiceFetchDetail::reference `in` portcallIds)
            portcallIdFilter.add(ServiceFetchCounter::etaUpdate / ServiceFetchDetail::reference `in` portcallIds)
            portcallIdFilter.add(ServiceFetchCounter::berthUpdate / ServiceFetchDetail::reference `in` portcallIds)
            portcallIdFilter.add(ServiceFetchCounter::etdUpdate / ServiceFetchDetail::reference `in` portcallIds)
            portcallIdFilter.add(ServiceFetchCounter::agentUpdate `in` portcallIds)
            portcallIdFilter.add(ServiceFetchCounter::finish / ServiceFetchDetail::reference `in` portcallIds)
            portcallIdFilter.add(ServiceFetchCounter::noUpdates `in` portcallIds)
            return collection.find(and(and(filters), or(portcallIdFilter))).sort(descending(ServiceFetchCounter::_id))
        }
        return collection.find(and(filters)).sort(descending(ServiceFetchCounter::_id))
    }
}
