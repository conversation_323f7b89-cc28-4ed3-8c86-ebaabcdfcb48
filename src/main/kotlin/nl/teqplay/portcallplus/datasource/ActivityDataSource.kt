package nl.teqplay.portcallplus.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.model.data.Activity
import nl.teqplay.portcallplus.utils.getTimeInRange
import nl.teqplay.skeleton.datasource.kmongo.and
import nl.teqplay.skeleton.datasource.kmongo.contains
import nl.teqplay.skeleton.datasource.kmongo.descending
import nl.teqplay.skeleton.datasource.kmongo.ensureIndex
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.gte
import nl.teqplay.skeleton.datasource.kmongo.lte
import nl.teqplay.skeleton.datasource.kmongo.ne
import org.springframework.stereotype.Repository
import java.util.Date
import java.util.concurrent.TimeUnit

@Repository
class ActivityDataSource(database: MongoDatabase) : DataSource<Activity>(database) {
    override val collection by lazy {
        fetchCollection("activity", Activity::class.java).apply {
            ensureIndex(descending(Activity::taskName))
        }
    }

    fun get(
        imoNumber: String? = null,
        taskType: ScheduledTaskType? = null,
        from: Date?,
        to: Date?,
    ): List<Activity> {
        val (fromDate, toDate) = getTimeInRange(from, to, TimeUnit.DAYS.toMillis(15))
        val filters = mutableListOf(Activity::creationTimestamp gte fromDate, Activity::creationTimestamp lte toDate)
        imoNumber?.let { filters.add(Activity::imoList contains it) }
        taskType?.let { filters.add(Activity::taskName eq it) }

        return collection.find(and(filters)).sort(sortByUpdateAndCreationTimestamp).toList()
    }

    fun getLastActivityWithUpdates(taskType: ScheduledTaskType): Activity? {
        return collection
            .find(and(Activity::taskName eq taskType, Activity::imoList ne emptySet()))
            .sort(descending(Activity::updateTimestamp)).first()
    }

    fun getLastActivity(taskType: ScheduledTaskType): Activity? {
        return collection
            .find(Activity::taskName eq taskType)
            .sort(descending(Activity::updateTimestamp)).first()
    }
}
