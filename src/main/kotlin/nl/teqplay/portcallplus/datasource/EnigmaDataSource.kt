package nl.teqplay.portcallplus.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.portcallplus.model.data.enigma.EnigmaTravels
import org.springframework.stereotype.Repository

@Repository
class EnigmaDataSource(database: MongoDatabase) : DataSource<EnigmaTravels>(database) {
    override val collection by lazy {
        fetchCollection("enigma", EnigmaTravels::class.java)
    }
}
