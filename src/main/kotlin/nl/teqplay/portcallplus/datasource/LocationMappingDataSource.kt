package nl.teqplay.portcallplus.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.portcallplus.model.LocationMapping
import nl.teqplay.portcallplus.model.Unlocode
import nl.teqplay.skeleton.datasource.kmongo.and
import nl.teqplay.skeleton.datasource.kmongo.descending
import nl.teqplay.skeleton.datasource.kmongo.ensureIndex
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.findOne
import org.springframework.stereotype.Repository

@Repository
class LocationMappingDataSource(database: MongoDatabase) : DataSource<LocationMapping>(database) {
    override val collection by lazy {
        fetchCollection("externalLocationMapping", LocationMapping::class.java).apply {
            ensureIndex(descending(LocationMapping::source))
            ensureIndex(descending(LocationMapping::port))
            ensureIndex(descending(LocationMapping::name))
        }
    }

    fun getBySourcePortName(
        source: String,
        port: Unlocode,
        name: String,
    ): LocationMapping? {
        return collection.findOne(
            and(
                LocationMapping::port eq port,
                LocationMapping::source eq source,
                LocationMapping::name eq name,
            ),
        )
    }
}
