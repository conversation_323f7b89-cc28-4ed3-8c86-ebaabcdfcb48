package nl.teqplay.portcallplus.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.portcallplus.model.data.portbase.PortbaseDataModel
import org.springframework.stereotype.Repository

@Repository
class PortBaseDataSource(database: MongoDatabase) : DataSource<PortbaseDataModel>(database) {
    override val collection by lazy {
        fetchCollection("portbase", PortbaseDataModel::class.java)
    }
}
