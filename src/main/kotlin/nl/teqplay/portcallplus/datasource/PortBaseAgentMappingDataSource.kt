package nl.teqplay.portcallplus.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.portcallplus.model.data.portbase.PortbaseAgentMappingDataModel
import nl.teqplay.skeleton.datasource.kmongo.and
import nl.teqplay.skeleton.datasource.kmongo.eq
import org.bson.conversions.Bson
import org.springframework.stereotype.Repository

@Repository
class PortBaseAgentMappingDataSource(database: MongoDatabase) : DataSource<PortbaseAgentMappingDataModel>(database) {
    override val collection by lazy {
        fetchCollection("portbaseAgentMapping", PortbaseAgentMappingDataModel::class.java)
    }

    fun getByAgentDescription(
        description: String,
        port: String?,
    ): PortbaseAgentMappingDataModel? {
        val filters = mutableListOf<Bson>()
        filters.add(PortbaseAgentMappingDataModel::agentDescription eq description)
        port?.let {
            filters.add(PortbaseAgentMappingDataModel::port eq it)
        }
        return collection.find(and(filters)).firstOrNull()
    }
}
