package nl.teqplay.portcallplus.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.portcallplus.model.data.ScheduledTaskRun
import org.springframework.stereotype.Repository

@Repository
class ScheduledTaskRunDataSource(database: MongoDatabase) : DataSource<ScheduledTaskRun>(database) {
    override val collection by lazy {
        fetchCollection("scheduledTaskRun", ScheduledTaskRun::class.java)
    }

    override fun list(): List<ScheduledTaskRun> {
        return collection.find().sort(sortByUpdateAndCreationTimestamp).toList()
    }
}
