package nl.teqplay.portcallplus.datasource

import com.mongodb.client.model.ReplaceOptions
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.portcallplus.api.model.Portcall
import nl.teqplay.portcallplus.api.model.PortcallAlias
import nl.teqplay.portcallplus.api.model.PortcallAliasName
import nl.teqplay.portcallplus.api.model.ScheduledTaskType
import nl.teqplay.portcallplus.utils.generatePortcallId
import nl.teqplay.portcallplus.utils.getTimeInRange
import nl.teqplay.portcallplus.utils.log
import nl.teqplay.portcallplus.utils.removeSpecialChars
import nl.teqplay.skeleton.datasource.kmongo.and
import nl.teqplay.skeleton.datasource.kmongo.ascending
import nl.teqplay.skeleton.datasource.kmongo.descending
import nl.teqplay.skeleton.datasource.kmongo.distinct
import nl.teqplay.skeleton.datasource.kmongo.div
import nl.teqplay.skeleton.datasource.kmongo.ensureIndex
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.gte
import nl.teqplay.skeleton.datasource.kmongo.`in`
import nl.teqplay.skeleton.datasource.kmongo.lte
import nl.teqplay.skeleton.datasource.kmongo.ne
import nl.teqplay.skeleton.datasource.kmongo.or
import nl.teqplay.skeleton.datasource.kmongo.regex
import nl.teqplay.skeleton.datasource.kmongo.replaceOneById
import org.bson.conversions.Bson
import org.springframework.stereotype.Repository
import java.util.Date
import java.util.TreeSet
import java.util.concurrent.TimeUnit
import java.util.logging.Level
import kotlin.math.abs

@Repository
class PortcallDataSource(database: MongoDatabase) : DataSource<Portcall>(database) {
    override val collection by lazy {
        fetchCollection("portcall", Portcall::class.java).apply {
            // add indexes
            ensureIndex(descending(Portcall::imo))
            ensureIndex(descending(Portcall::port))
            ensureIndex(descending(Portcall::startTime))
            ensureIndex(descending(Portcall::creationTimestamp))
            ensureIndex(descending(Portcall::endTime))
            ensureIndex(descending(Portcall::portAtdTime))
            ensureIndex(descending(Portcall::eosAtdTime))
            ensureIndex(descending(Portcall::vesselAgent))
            ensureIndex(descending(Portcall::source))
            ensureIndex(descending(Portcall::portcallAlias / PortcallAlias::source))
            ensureIndex(descending(Portcall::portcallAlias / PortcallAlias::alias))
        }
    }

    /**
     * fetches the portcalls by the defined filters
     */
    fun getByFiltered(
        shipImo: String? = null,
        port: String? = null,
        vesselAgent: String? = null,
        portcallId: String? = null,
        originUnlocode: String? = null,
        startTime: Date? = null,
        endTime: Date? = null,
        source: ScheduledTaskType? = null,
        finished: Boolean? = null,
        limit: Int = 10,
    ): TreeSet<Portcall> {
        val filters = mutableListOf<Bson>()
        shipImo?.let { filters.add(Portcall::imo eq it) }
        portcallId?.let { filters.add(Portcall::portcallId eq it) }
        originUnlocode?.let { filters.add(Portcall::originUnlocode eq it) }
        port?.let { filters.add(Portcall::port eq it) }
        startTime?.let { filters.add(Portcall::startTime gte it) }
        endTime?.let { filters.add(Portcall::startTime lte it) }
        vesselAgent?.let { filters.add(Portcall::vesselAgent eq it) }
        source?.let { filters.add(Portcall::source eq it) }
        when (finished) {
            true -> filters.add(Portcall::eosAtdTime ne null)
            false -> filters.add(Portcall::eosAtdTime eq null)
            else -> {}
        }
        val result = TreeSet<Portcall>()
        collection.find(and(filters)).sort(sortByUpdateAndCreationTimestamp).limit(limit).useCursor {
            result.addAll(it)
        }
        return result
    }

    /**
     * Returns the most recently updated portcalls for the given [imoNumbers]
     */
    fun getMostRecentPortcalls(imoNumbers: Set<String>): List<Portcall> {
        return imoNumbers.mapNotNull {
            super.getMostRecentDocument(Portcall::imo eq it)
        }
    }

    /**
     * Get the nearest Portcall to startTime within the interval supplied
     */
    fun getNearestByImoAndDateInterval(
        shipImo: String,
        port: String? = null,
        startTime: Date,
        intervalInMillis: Long,
        finished: Boolean? = false,
    ) = getNearestByImoAndDate(
        shipImo = shipImo,
        port = port,
        estimatedTime = startTime,
        intervalFrom = Date(startTime.time - intervalInMillis),
        intervalTo = Date(startTime.time + intervalInMillis),
        finished = finished,
    )

    /**
     * Get the nearest Portcall to [estimatedTime] within the interval supplied [intervalFrom] to [intervalTo]
     */
    fun getNearestByImoAndDate(
        shipImo: String,
        port: String? = null,
        estimatedTime: Date,
        intervalFrom: Date? = null,
        intervalTo: Date? = null,
        finished: Boolean? = false,
        allowFuturePortcalls: Boolean = true,
    ): Portcall? {
        var currentPortcall: Portcall? = null
        for (portcall in getByFiltered(
            shipImo = shipImo,
            port = port,
            startTime = intervalFrom,
            endTime = intervalTo,
            finished = finished,
        )) {
            if (currentPortcall == null ||
                abs(estimatedTime.time - portcall.startTime.time) < abs(estimatedTime.time - currentPortcall.startTime.time)
            ) {
                if (!allowFuturePortcalls) {
                    val portcallStartTime = portcall.portAtaTime ?: portcall.startTime
                    val isFuturePortcall = portcallStartTime.after(Date())

                    if (isFuturePortcall) {
                        continue
                    }
                }

                currentPortcall = portcall
            }
        }
        return currentPortcall
    }

    /**
     * Fetches the most recent portcall for the given [shipImo]
     * @param shipImo The imo to match a portcall with
     * @param port The port to match a portcall with
     * @param startTime Filters portcalls with [Portcall.startTime] in the range of [startTime] and [endTime]
     * @param endTime Filters portcalls with [Portcall.startTime] in the range of [startTime] and [endTime]
     * @param finished If true -> fetches only closed portcalls ([Portcall.portAtdTime] not null),
     * If false (by default) -> fetches only open portcalls ([Portcall.portAtdTime] null)
     * If null -> fetches both open and closed portcalls
     */
    fun getMostRecentByImo(
        shipImo: String,
        port: String? = null,
        startTime: Date? = null,
        endTime: Date? = null,
        finished: Boolean? = false,
    ): Portcall? {
        // fetch all relevant portcalls sorted by startTime
        val portcalls = getByFiltered(shipImo = shipImo, port = port, startTime = startTime, endTime = endTime, finished = finished)
        // fetch the most recently started portcall that has not ended. If none found. Return the most recently ended portcall
        val linkedPortcall = portcalls.firstOrNull { it.portAtdTime == null } ?: portcalls.firstOrNull()
        if (linkedPortcall == null) {
            log(imoNumber = shipImo, message = "No matching portcall found")
        }
        return linkedPortcall
    }

    fun searchByAlias(
        searchPattern: String,
        portcallAliasName: PortcallAliasName,
    ): List<Portcall> {
        val aliasSearch = and(
            Portcall::portcallAlias / PortcallAlias::source eq portcallAliasName,
            Portcall::portcallAlias / PortcallAlias::alias regex "^$searchPattern",
        )
        val result = collection.find(aliasSearch).toList()
        return result.ifEmpty {
            collection.find(Portcall::_id regex "^$searchPattern").toList()
        }
    }

    /**
     * For a given [portcallAliasName], returns all the [Portcall] that exactly match the either the [Portcall._id]
     * or the [PortcallAlias.alias]
     */
    fun getByIdsAndAlias(
        ids: Collection<String>,
        portcallAliasName: PortcallAliasName,
    ): List<Portcall> {
        val getByAlias = or(
            Portcall::_id `in` ids,
            Portcall::portcallAlias / PortcallAlias::alias `in` ids,
        )
        return collection.find(
            and(Portcall::portcallAlias / PortcallAlias::source eq portcallAliasName, getByAlias),
        ).toList()
    }

    /**
     * Creates portcall [model] if its not existing in the db. Or updates the existing one if there are changes in the
     * model
     */
    override fun createOrUpdate(model: Portcall): Portcall {
        val existing = get(model._id)
        if (existing != null) {
            // only update if there is an update in the portcall model
            if (model != existing) {
                // keep the creation time the same and update only the updateTimestamp
                model.resetTimestamps(existing)
                model.resetUpdateTimestamp()
            } else {
                return existing
            }
        } else {
            model.resetCreationTimestamp()
        }
        collection.replaceOneById(model._id, model, ReplaceOptions().upsert(true))
        return model
    }

    /**
     * A function to generate the id of a portcall
     */
    fun generateId(
        port: String,
        imo: String,
        time: Date,
    ): String {
        var attempt = 0
        while (true) {
            val id = generatePortcallId(port, imo, time, attempt)
            val portcall = get(id)
            if (portcall == null) {
                if (attempt > 30) {
                    log(id, imo, port, "Took more than 30 attempts to find id!", Level.WARNING)
                }
                return id
            }
            attempt++
        }
    }

    /**
     * Gets all the Portcalls that have received an update between the given [from] and [to] dates. The date is restricted
     * to a max of 30 days
     */
    fun getByUpdateTimes(
        ports: List<String>?,
        from: Date,
        to: Date,
    ): List<Portcall> {
        val (fromDate, toDate) = getTimeInRange(from, to, TimeUnit.DAYS.toMillis(30))
        val filter = if (!ports.isNullOrEmpty()) Portcall::port `in` ports else null
        return super.getByUpdateTimes(filter, fromDate, toDate)
    }

    fun getVesselAgentsByFormattedKey(): Map<String, String> {
        return this.getAllVesselAgents().associateBy { removeSpecialChars(it) }
    }

    /**
     * Get all portcalls in the range [from]-[to] within a [maxRangeInMillis] of the given [source] (if provided).
     */
    fun getByStartTime(
        from: Date,
        to: Date,
        source: ScheduledTaskType? = null,
        maxRangeInMillis: Long = TimeUnit.DAYS.toMillis(30),
    ): List<Portcall> {
        val (fromDate, toDate) = getTimeInRange(from, to, maxRangeInMillis)
        val filters = mutableListOf<Bson>()
        filters.add(Portcall::startTime gte fromDate)
        filters.add(Portcall::startTime lte toDate)
        source?.let { filters.add(Portcall::source eq it) }
        return collection
            .find(and(filters))
            .sort(ascending(Portcall::startTime))
            .toList()
    }

    /**
     * Gets all the distinct [Portcall.vesselAgent] from the db
     */
    private fun getAllVesselAgents(): Set<String> {
        val result = collection.distinct(Portcall::vesselAgent).toList().filterNotNull().toHashSet()
        result.removeAll(listOf(null, "-", ""))
        return result
    }

    fun countUnfinished() =
        collection.find(
            Portcall::eosAtdTime eq null,
        ).count()

    fun getUnfinishedBatched(
        finished: Int = 0,
        skipPages: Int = 0,
        pageSize: Int,
    ): List<Portcall> =
        collection.find(
            Portcall::eosAtdTime eq null,
        ).skip((skipPages * pageSize) - finished).limit(pageSize).toList()
}
