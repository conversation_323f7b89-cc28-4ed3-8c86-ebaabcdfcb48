package nl.teqplay.portcallplus.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.portcallplus.model.service.Nomination
import nl.teqplay.skeleton.datasource.kmongo.and
import nl.teqplay.skeleton.datasource.kmongo.eq
import org.bson.conversions.Bson
import org.springframework.stereotype.Repository
import java.util.Date

@Repository
class NominationDataSource(database: MongoDatabase) : DataSource<Nomination>(database) {
    override val collection by lazy {
        fetchCollection("nomination", Nomination::class.java)
    }

    /**
     * Fetched nominations by the defined filters
     */
    fun getByType(
        portcallId: String? = null,
        provider: String? = null,
        startTime: Date,
        endTime: Date,
    ): List<Nomination> {
        val filters = mutableListOf<Bson>()
        provider?.let { filters.add(Nomination::provider eq it) }
        portcallId?.let { filters.add(Nomination::_id eq portcallId) }
        return if (portcallId != null) {
            collection.find(and(filters)).sort(sortByUpdateAndCreationTimestamp).toList()
        } else {
            collection.find(super.appendUpdateTimesFilter(and(filters), startTime, endTime))
                .sort(sortByUpdateAndCreationTimestamp).toList()
        }
    }
}
