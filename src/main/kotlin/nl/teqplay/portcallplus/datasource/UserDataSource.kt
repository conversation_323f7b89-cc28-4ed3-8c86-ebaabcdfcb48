package nl.teqplay.portcallplus.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.portcallplus.model.data.User
import nl.teqplay.skeleton.auth.credentials.datasources.UserDataSource
import nl.teqplay.skeleton.common.exception.ForbiddenException
import nl.teqplay.skeleton.datasource.kmongo.ensureUniqueIndex
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.findOne
import org.springframework.stereotype.Repository

@Repository
class UserDataSource(
    database: MongoDatabase,
) : UserDataSource, DataSource<User>(database) {
    final val collectionName = "users"

    override val collection by lazy {
        fetchCollection(collectionName, User::class.java).apply {
            ensureUniqueIndex(User::username)
        }
    }

    override fun find(username: String): User? =
        collection.findOne(
            User::username eq username,
        )

    override fun findOrCreate(username: String) = throw ForbiddenException("The use of this method is forbidden by architecture.")
}
