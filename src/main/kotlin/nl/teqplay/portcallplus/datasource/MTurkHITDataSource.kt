package nl.teqplay.portcallplus.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.portcallplus.api.model.DataModel
import nl.teqplay.portcallplus.model.data.mturk.MTurkHITDetails
import nl.teqplay.portcallplus.model.data.mturk.MTurkHITResponse
import nl.teqplay.portcallplus.model.service.MTurkHIT
import nl.teqplay.skeleton.datasource.kmongo.and
import nl.teqplay.skeleton.datasource.kmongo.div
import nl.teqplay.skeleton.datasource.kmongo.ensureUniqueIndex
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.findOne
import nl.teqplay.skeleton.datasource.kmongo.gte
import nl.teqplay.skeleton.datasource.kmongo.`in`
import nl.teqplay.skeleton.datasource.kmongo.ne
import org.bson.conversions.Bson
import org.springframework.stereotype.Repository
import software.amazon.awssdk.services.mturk.model.AssignmentStatus
import java.time.Duration
import java.time.Instant
import java.util.Date

@Repository
class MTurkHITDataSource(database: MongoDatabase) : DataSource<MTurkHITDetails>(database) {
    override val collection by lazy {
        fetchCollection("mturk", MTurkHITDetails::class.java).apply {
            // add indexes
            ensureUniqueIndex(
                MTurkHITDetails::request / MTurkHIT::shipImo,
                MTurkHITDetails::request / MTurkHIT::portcallId,
            )
        }
    }

    /**
     * Gets a unique [MTurkHITDetails] for the given [shipImo] and [portcallId]
     */
    fun get(
        shipImo: String,
        portcallId: String,
    ): MTurkHITDetails? {
        return collection.findOne(
            MTurkHITDetails::request / MTurkHIT::shipImo eq shipImo,
            MTurkHITDetails::request / MTurkHIT::portcallId eq portcallId,
        )
    }

    fun getByFiltered(
        shipImo: String? = null,
        portcallId: String? = null,
    ): List<MTurkHITDetails> {
        val filters = mutableListOf<Bson>()
        shipImo?.let { filters.add(MTurkHITDetails::request / MTurkHIT::shipImo eq it) }
        portcallId?.let { filters.add(MTurkHITDetails::request / MTurkHIT::portcallId eq it) }
        return collection.find(and(filters)).toList()
    }

    fun getOverallApprovedAndSubmittedAnswers(
        status: AssignmentStatus = AssignmentStatus.APPROVED,
        date: Date = Date.from(
            Instant.now().minus(Duration.ofDays(1)),
        ),
    ): List<MTurkHITDetails> {
        val filters = mutableListOf<Bson>()
        filters.add(MTurkHITDetails::overallAssignmentStatus eq AssignmentStatus.APPROVED)
        filters.add(MTurkHITDetails::assignments / MTurkHITResponse::status eq AssignmentStatus.SUBMITTED)
        filters.add(MTurkHITDetails::creationTimestamp gte date)
        return collection.find(and(filters)).toList()
    }

    fun getAsSequence(ids: Collection<String>): Sequence<MTurkHITDetails> {
        return collection.find(DataModel::_id `in` ids).asSequence()
    }

    fun getPendingHitsToProcessAsSequence(): Sequence<MTurkHITDetails> {
        val filter = and(
            MTurkHITDetails::expired eq false,
            MTurkHITDetails::disappearedInAmazon ne true,
            MTurkHITDetails::approvedAnswer eq null,
        )
        return collection.find(filter).asSequence()
    }
}
