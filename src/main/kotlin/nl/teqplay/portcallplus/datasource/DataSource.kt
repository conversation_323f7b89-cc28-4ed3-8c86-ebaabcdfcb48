package nl.teqplay.portcallplus.datasource

import com.mongodb.client.model.ReplaceOptions
import com.mongodb.kotlin.client.MongoCollection
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.portcallplus.api.model.DataModel
import nl.teqplay.skeleton.datasource.kmongo.and
import nl.teqplay.skeleton.datasource.kmongo.descending
import nl.teqplay.skeleton.datasource.kmongo.ensureIndex
import nl.teqplay.skeleton.datasource.kmongo.findOneById
import nl.teqplay.skeleton.datasource.kmongo.gte
import nl.teqplay.skeleton.datasource.kmongo.`in`
import nl.teqplay.skeleton.datasource.kmongo.lte
import nl.teqplay.skeleton.datasource.kmongo.replaceOneById
import org.bson.conversions.Bson
import org.springframework.stereotype.Repository
import java.util.Date

@Repository
abstract class DataSource<T : DataModel>(private val database: MongoDatabase) {
    val sortByUpdateAndCreationTimestamp = and(descending(DataModel::updateTimestamp, DataModel::creationTimestamp))

    protected abstract val collection: MongoCollection<T>

    /**
     * Gets a singleton connection to mongo
     */
    protected fun <T : Any> fetchCollection(
        collectionName: String,
        clazz: Class<T>,
    ): MongoCollection<T> {
        return database.getCollection(collectionName, clazz).apply {
            // add an index on the update and creation Timestamp
            ensureIndex(sortByUpdateAndCreationTimestamp)
        }
    }

    /**
     * Fetch all the documents
     */
    fun list() = collection.find().toList()

    /**
     * Creates or updates the given [model] to the collection
     */
    fun createOrUpdate(model: T): T {
        val existing = get(model._id)
        if (existing != null) {
            // keep the creation time the same and update only the updateTimestamp
            model.resetTimestamps(existing)
            model.resetUpdateTimestamp()
        } else {
            model.resetCreationTimestamp()
        }
        collection.replaceOneById(model._id, model, ReplaceOptions().upsert(true))
        return model
    }

    fun createMany(models: List<T>) {
        collection.insertMany(models)
    }

    fun insert(model: T): T {
        collection.insertOne(model)
        return model
    }

    /**
     * Fetch a document based on the given [id]
     */
    fun get(id: Any) = collection.findOneById(id)

    /**
     * Fetch documents based on the given [ids]
     */
    fun get(ids: Collection<Any>) = collection.find(DataModel::_id `in` ids).toList()

    /**
     * Fetches the most recent document updated or inserted
     */
    fun getMostRecentDocument(filter: Bson? = null): T? {
        return if (filter == null) {
            collection.find().sort(sortByUpdateAndCreationTimestamp).limit(1)
        } else {
            collection.find(filter).sort(sortByUpdateAndCreationTimestamp).limit(1)
        }.firstOrNull()
    }

    /**
     * Gets all the documents based on its [DataModel.updateTimestamp], if they are within the timerange given
     */
    fun getByUpdateTimes(
        from: Date,
        to: Date,
    ) = getByUpdateTimes(null, from, to)

    /**
     * Gets all the documents based on its [DataModel.updateTimestamp], if they are within the timerange given
     */
    protected fun getByUpdateTimes(
        filter: Bson? = null,
        from: Date,
        to: Date,
    ): List<T> {
        return collection.find(appendUpdateTimesFilter(filter, from, to))
            .sort(sortByUpdateAndCreationTimestamp).toList()
    }

    /**
     * Appends [from] and [to] range filters on the [DataModel.updateTimestamp] to the existing given [filter]
     */
    protected fun appendUpdateTimesFilter(
        filter: Bson?,
        from: Date,
        to: Date,
    ): Bson {
        val filters = mutableListOf<Bson>()
        filter?.let { filters.add(it) }
        filters.add(DataModel::updateTimestamp gte from)
        filters.add(DataModel::updateTimestamp lte to)
        return and(filters)
    }
}
