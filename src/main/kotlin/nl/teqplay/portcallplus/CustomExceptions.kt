package nl.teqplay.portcallplus

import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.NOT_FOUND)
class ModelNotFoundException(
    message: String?,
    uniqueReference: String = "",
) : RuntimeException(getExceptionMessage(uniqueReference, message))

@ResponseStatus(value = HttpStatus.PRECONDITION_FAILED)
class PreconditionException(
    message: String?,
    uniqueReference: String = "",
) : RuntimeException(getExceptionMessage(uniqueReference, message))

@ResponseStatus(value = HttpStatus.BAD_REQUEST)
class MissingDataException(message: String?, uniqueReference: String = "") : RuntimeException(getExceptionMessage(uniqueReference, message))

@ResponseStatus(value = HttpStatus.CONFLICT, reason = "Duplicate item.")
class DuplicateItemException(
    message: String?,
    uniqueReference: String = "",
) : RuntimeException(getExceptionMessage(uniqueReference, message))

@ResponseStatus(value = HttpStatus.BAD_REQUEST, reason = "Issue with connecting to service.")
class BadServiceException(message: String?, uniqueReference: String = "") : RuntimeException(getExceptionMessage(uniqueReference, message))

/**
 * Parses a nice message for the Exception thrown
 */
private fun getExceptionMessage(
    uniqueReference: String,
    message: String?,
): String {
    return if (uniqueReference.isNotBlank()) {
        "[$uniqueReference]:$message"
    } else {
        message ?: ""
    }
}
