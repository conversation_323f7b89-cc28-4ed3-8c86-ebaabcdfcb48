import com.bmuschko.gradle.docker.tasks.image.DockerPushImage
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

buildscript {
    ext {
        // Kotlin
        kotlin_version = '1.9.25'
        kotlinx_version = '1.6.4'
        klogging_version = '7.0.7'

        // Teqplay
        skeleton_version = '2.9.1-b122.1'
        platform_version = '9.6.4'
        csi_version = '20250612-b55.1'
        aisengine_version = 'master-2.3.0'
        smartfleet_version = '20241226-b12505406988.6.1'

        // Spring
        spring_boot_version = '3.4.7'
        spring_cloud_version = '3.2.1'
        springdoc_version = '2.8.9'

        jackson_version = '2.19.0'
        caffeine_version = '3.2.0'
        janino_version = '3.1.12'
        jslack_version = '3.4.2'
        jsoup_version = '1.20.1'
        okhttp_version = '4.12.0'
        poi_ooxml_version = '5.4.1'
        javers_version = '7.8.0'
        mongodb_version = '4.11.2'

        // Testing
        mockito_kotlin_version = '5.4.0'
        mockk_version = '1.14.2'

        // Amazon
        aws_version = '2.31.54'
        aws_ecr_version = '0.7.0'
        ecr_repo_url = '050356841556.dkr.ecr.eu-west-1.amazonaws.com'

        // Plugins
        ktlint_version = '12.1.2'
        docker_remote_api_version = '9.4.0'
        gradle_versions_plugin_version = '0.46.0'
        cyclonedx_bom_version = "2.3.1"
    }
    ext['mongodb.version'] = ext.mongodb_version

    repositories {
        mavenCentral()
        maven {
            url 's3://repo.teqplay.nl/release'
            credentials(AwsCredentials) {
                accessKey "$s3_access_key"
                secretKey "$s3_secret_key"
            }
        }
        maven {
            url 's3://repo.teqplay.nl/snapshot'
            credentials(AwsCredentials) {
                accessKey "$s3_access_key"
                secretKey "$s3_secret_key"
            }
        }
    }

    dependencies {
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "org.springframework.boot:spring-boot-gradle-plugin:${spring_boot_version}"
        classpath "org.jetbrains.kotlin:kotlin-allopen:${kotlin_version}"
        classpath "org.jlleitschuh.gradle:ktlint-gradle:$ktlint_version"
    }
}

plugins {
    id 'war'
    id 'org.jetbrains.kotlin.jvm' version "$kotlin_version"
    id 'org.springframework.boot' version "$spring_boot_version"
    id 'com.bmuschko.docker-remote-api' version "$docker_remote_api_version"
    id 'com.patdouble.awsecr' version "$aws_ecr_version"
    id "com.github.ben-manes.versions" version "$gradle_versions_plugin_version"
    id "org.cyclonedx.bom" version "$cyclonedx_bom_version"
}

group 'nl.teqplay.portcallplus'

repositories {
    maven {
        url 's3://repo.teqplay.nl/release'
        credentials(AwsCredentials) {
            accessKey "$s3_access_key"
            secretKey "$s3_secret_key"
        }
    }
    maven {
        url 's3://repo.teqplay.nl/snapshot'
        credentials(AwsCredentials) {
            accessKey "$s3_access_key"
            secretKey "$s3_secret_key"
        }
    }
    maven { url 'https://jcenter.bintray.com/' }
    mavenCentral()
    maven { url 'https://jitpack.io' }
}

apply plugin: 'kotlin'
apply plugin: 'kotlin-spring'
apply plugin: 'io.spring.dependency-management'
apply plugin: "org.jlleitschuh.gradle.ktlint"

ktlint {
    version = "0.50.0"
    enableExperimentalRules = false
    filter {
        exclude { element ->
            def path = element.file.path
            path.contains("\\build\\") || path.contains("/build/")
        }
    }
}

dependencies {
    implementation project(':api')

    implementation "nl.teqplay.aisengine:models:$aisengine_version"
    implementation "nl.teqplay.aisengine:nats-stream-event:$aisengine_version"
    implementation "nl.teqplay.aisengine:client-ship-history:$aisengine_version"
    implementation "nl.teqplay.skeleton:common:$skeleton_version"
    implementation "nl.teqplay.skeleton:common-network:$skeleton_version"
    implementation "nl.teqplay.skeleton:common-internal-api-client:$skeleton_version"
    implementation "nl.teqplay.skeleton:util-filelog:$skeleton_version"
    implementation "nl.teqplay.skeleton:util-serialize-entities:$skeleton_version"
    implementation "nl.teqplay.skeleton:platform-client:$skeleton_version"
    implementation "nl.teqplay.skeleton:poma-client:$skeleton_version"
    implementation "nl.teqplay.skeleton:actuator:$skeleton_version"
    implementation("nl.teqplay.skeleton:util-location:$skeleton_version")
    //include basic auth for this project
    implementation "nl.teqplay.skeleton:datasource2:$skeleton_version"
    implementation("nl.teqplay.skeleton:auth-credentials-mongo2:$skeleton_version")
    implementation("nl.teqplay.skeleton:auth-credentials-password-auth:$skeleton_version")
    implementation("nl.teqplay.skeleton:platform-auth:$skeleton_version")
    implementation("nl.teqplay.skeleton:util-location2:$skeleton_version")
    implementation "nl.teqplay.skeleton:uncaught-exception-slack-report:$skeleton_version"

    //Swagger
    implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:$springdoc_version")

    // keycloak m2m auth
    implementation("nl.teqplay.skeleton:auth-credentials-keycloak-s2s-server:$skeleton_version")
    implementation("nl.teqplay.skeleton:auth-credentials-keycloak-any-valid-user:$skeleton_version")

    implementation("nl.teqplay.skeleton:rabbitmq:$skeleton_version")
    testImplementation("nl.teqplay.skeleton:nats-test:$skeleton_version")

    implementation "nl.teqplay.smartfleet:api:$smartfleet_version"

    implementation "nl.teqplay.csi:client:$csi_version"

    implementation "nl.teqplay.skeleton:util-filelog:$skeleton_version"
    implementation "nl.teqplay.skeleton:datasource2:$skeleton_version"

    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8"

    implementation "org.codehaus.janino:janino:$janino_version"

    implementation "org.springframework.boot:spring-boot-starter"
    implementation "org.springframework.boot:spring-boot-starter-web"
    implementation "org.springframework.boot:spring-boot-starter-amqp"
    implementation "org.springframework.boot:spring-boot-starter-validation"
    implementation 'org.springframework.security:spring-security-config'
    implementation "org.springframework.boot:spring-boot-starter-actuator"

    implementation "org.springframework.cloud:spring-cloud-starter-kubernetes-fabric8-config:$spring_cloud_version"

    implementation "com.fasterxml.jackson.core:jackson-databind:$jackson_version"
    implementation "com.fasterxml.jackson.core:jackson-annotations:$jackson_version"
    implementation "com.fasterxml.jackson.module:jackson-module-kotlin:$jackson_version"
    implementation "com.fasterxml.jackson.datatype:jackson-datatype-jsr310:$jackson_version"
    implementation "com.fasterxml.jackson.datatype:jackson-datatype-joda:$jackson_version"

    //kotlin coroutines
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:$kotlinx_version"

    // jsoup HTML parser library @ https://jsoup.org/
    implementation "org.jsoup:jsoup:$jsoup_version"

    testImplementation "org.junit.jupiter:junit-jupiter-api"
    testImplementation "org.junit.jupiter:junit-jupiter-params"
    testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine"

    testImplementation("org.springframework.boot:spring-boot-starter-test") {
        // Ensure we don't get JUnit4 in our class path
        exclude group: 'junit', module: 'junit'
    }
    testImplementation "org.mockito.kotlin:mockito-kotlin:$mockito_kotlin_version"
    testImplementation "org.mockito:mockito-junit-jupiter"
    testImplementation "io.mockk:mockk:$mockk_version"
    //enable external http connection
    implementation "com.squareup.okhttp3:okhttp:$okhttp_version"

    //JSlack
    implementation "com.github.seratch:jslack:$jslack_version"
    /** KLogger */
    implementation "io.github.oshai:kotlin-logging:$klogging_version"

    // Apache POI
    implementation "org.apache.poi:poi-ooxml:$poi_ooxml_version"

    //mturk for fetching NxtPort agent information
    implementation "software.amazon.awssdk:mturk:$aws_version"

    implementation "software.amazon.awssdk:s3:$aws_version"

    // Cache
    implementation "org.springframework.boot:spring-boot-starter-cache"
    implementation "com.github.ben-manes.caffeine:caffeine:$caffeine_version"

    //check object differences
    implementation "org.javers:javers-core:$javers_version"
}

tasks.withType(KotlinCompile).configureEach {
    compilerOptions {
        jvmTarget = JvmTarget.JVM_17
        freeCompilerArgs = ["-Xjsr305=strict"]
    }
}

test {
    useJUnitPlatform {
        includeEngines 'junit-jupiter'
    }
}

version = generateVersion()

docker {
    registryCredentials {
        url.set(ecr_repo_url)
    }
}

def dockerImageName = "$ecr_repo_url/${project.name.replace("-backend", "")}:${project.version}"

bootBuildImage {
    imageName = dockerImageName
}

tasks.register('dockerPushImage', DockerPushImage) {
    images.add(dockerImageName)
}

tasks.register('getVersion') {
    doLast {
        println project.version
    }
}

static def generateVersion() {
    if (System.getenv('GH') != null) {
        def branch = System.getenv('BRANCH').replace("/", "_").toLowerCase()
        def buildNum = System.getenv('GITHUB_RUN_NUMBER')
        def formattedTimestamp = getTimestamp()
        return branch + '-' + formattedTimestamp + '-b' + buildNum    } else {
        return 'local'
    }
}

static def getTimestamp() {
    def date = new Date()
    def formattedDate = date.format('yyyy-MM-dd')
    return formattedDate
}

cyclonedxBom {
    includeConfigs = ["runtimeClasspath"]
    outputFormat = "json"
    outputName = "bom"
}
